package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.DateUtils;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.XlsUtil;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaConfigMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpShiftChangeMapper;
import com.caidao1.wa.mybatis.model.WaEmpShiftChange;
import com.caidao1.wa.mybatis.model.WaEmpShiftChangeExample;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.wa.dto.CalendarEventDto;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmpShiftChangeDto;
import com.caidaocloud.attendance.service.application.dto.ImportCellDto;
import com.caidaocloud.attendance.service.application.dto.ImportRowDto;
import com.caidaocloud.attendance.service.application.enums.EmpShiftChangeStatus;
import com.caidaocloud.attendance.service.application.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.application.service.IEmpShiftChangeService;
import com.caidaocloud.attendance.service.domain.entity.SysEmpInfoDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.domain.entity.WaWorktimeDo;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.EmpShiftSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.EmpWorkCalendarSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.DownloadShiftTemplateDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.usermodel.DVConstraint;
import org.apache.poi.hssf.usermodel.HSSFDataValidation;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.CellReference;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StopWatch;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工考勤日历服务
 *
 * <AUTHOR>
 * @Date 2021/8/11
 */
@Slf4j
@Service
public class EmpWorkCalendarService {
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private WaEmpShiftChangeMapper waEmpShiftChangeMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Autowired
    private WaWorktimeDo waWorktimeDo;
    @Autowired
    private WaShiftDo waShiftDo;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private IEmpShiftChangeService empShiftChangeService;

    /**
     * 查询员工排班数据
     *
     * @param searchDto
     * @param userInfo
     * @return
     * @throws Exception
     */
    public PageList searchEmpWorkTimeDetailsByMonth(EmpWorkCalendarSearchDto searchDto, UserInfo userInfo) throws Exception {
        Integer ym = searchDto.getYm();
        Integer year = Integer.parseInt(String.valueOf(ym).substring(0, 4));
        Integer month = Integer.parseInt(String.valueOf(ym).substring(4, 6));
        Calendar calendar = Calendar.getInstance();
        int m = month - 1;
        calendar.set(year, m, 1);
        Integer maximum = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        calendar.set(Calendar.DATE, 1);
        //选中月份
        SimpleDateFormat ymdDf = new SimpleDateFormat("yyyyMMdd");
        Date startDate = ymdDf.parse(ym + "01");
        Date endDate = new Date(DateUtil.addMonthDate(startDate.getTime(), 1, -1) * 1000);
        Long startLongValue = startDate.getTime() / 1000;
        Long endLongValue = endDate.getTime() / 1000;
        String belongid = userInfo.getTenantId();
        //数据权限
        String datafilter = searchDto.getDataScope();
        // 本次查询的员工数据
        PageBean pageBean = PageUtil.getPageBean(searchDto);
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("emp info");
        String orgId = null != searchDto.getOrgid() ? searchDto.getOrgid().trim() : null;
        PageList<Map> pageList = sysEmpInfo.getEmpInfoPageListForCalendar(pageBean, userInfo.getTenantId(),
                orgId, startLongValue, endLongValue, searchDto.getKeywords(), datafilter);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(pageList)) {
            return pageList;
        }
        List<Long> empIdList = pageList.stream().map(o -> (Long) o.get("empid")).collect(Collectors.toList());
        stopWatch.start("emp shift");
        //查询公司所有的班次信息
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongid);
        stopWatch.stop();
        //查询员工排班
        Map<String, Object> searchParams = new HashMap<>();
        searchParams.put("belongOrgId", belongid);
        searchParams.put("start", startLongValue);
        searchParams.put("end", endLongValue);
        searchParams.put("empIds", empIdList);
        stopWatch.start("emp calendar");
        List<Map> calendarList = waConfigMapper.getNonStoreEmpShiftCalendarList(searchParams);
        stopWatch.stop();
        Map<String, Map> empWorkShiftMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(calendarList)) {
            empWorkShiftMap = calendarList.stream().collect(Collectors.toMap(o -> o.get("empid") + "_" + o.get("date"), Function.identity(), (v1, v2) -> v1));
        }
        //班次调整查询
        stopWatch.start("shift change");
        Map<String, Integer> empChangeShiftMap = waCommonService.getEmpChangeShiftMap(belongid, empIdList, startLongValue, endLongValue);
        stopWatch.stop();
        stopWatch.start("shift merge");
        //员工、班次数据聚合
        for (Map empRow : pageList) {
            Long empid = (Long) empRow.get("empid");
            int shiftDays = 0;
            Integer workTotalTime = 0;
            for (int i = 1; i <= maximum; i++) {
                int d = i;
                String ymd = ym + String.format("%02d", i);
                String mapKey = empid + "_" + ymd;
                if (!empWorkShiftMap.containsKey(mapKey) && !empChangeShiftMap.containsKey(mapKey)) {
                    continue;
                }
                Integer dateType = null;
                String name = "";
                String code = "";
                Integer id = null;
                Integer workTime = 0;
                if (empWorkShiftMap.containsKey(mapKey)) {
                    Map shift = empWorkShiftMap.get(mapKey);
                    id = (Integer) shift.get("id");
                    dateType = (Integer) shift.get("dateType");// 1 工作日 2 休息日 3 法定假日 4 公司特殊休日
                    name = (String) shift.get("name");
                    if (null != shift.get("i18nShiftDefName")) {
                        String i18n = LangParseUtil.getI18nLanguage(shift.get("i18nShiftDefName").toString(), null);
                        if (StringUtil.isNotBlank(i18n)) {
                            name = i18n;
                        }
                    }
                    code = (String) shift.get("code");
                    workTime = (Integer) shift.get("workTotalTime");
                    workTotalTime += workTime;
                }
                if (empChangeShiftMap.containsKey(mapKey)) {
                    Integer changeShiftId = empChangeShiftMap.get(mapKey);
                    if (changeShiftId != null && shiftDefMap.get(changeShiftId) != null) {
                        WaShiftDef changeShift = shiftDefMap.get(changeShiftId);
                        name = changeShift.getShiftDefName();
                        code = changeShift.getShiftDefCode();
                        dateType = changeShift.getDateType();
                        id = changeShiftId;
                        workTotalTime -= workTime;
                        workTotalTime += changeShift.getWorkTotalTime();
                    }
                }
                CalendarEventDto event = new CalendarEventDto();
                event.setId(id);
                event.setDateType(dateType);
                Date start = (new SimpleDateFormat("yyyyMMdd")).parse(ymd);
                String startYmdTxt = (new SimpleDateFormat("yyyy-MM-dd")).format(start);
                event.setStart(startYmdTxt);
                event.setStartTimestamp(start.getTime() / 1000);
                if ("".equals(code)) {
                    empRow.put("d" + d, "-");
                } else {
                    if (BooleanUtils.isFalse(searchDto.getIsExport())) {
                        event.setTitle(name);
                        empRow.put(d + "", event);
                    } else {
                        event.setTitle(code + " " + name);
                        empRow.put("d" + d, event);
                    }
                    shiftDays++;
                }
            }
            empRow.put("workTotalTime", new BigDecimal(String.valueOf(workTotalTime / 60)).setScale(2, RoundingMode.HALF_UP));
            empRow.put("shiftDays", shiftDays);
        }
        stopWatch.stop();
        log.info("searchWorkTimeDetailsByMonth end,{}", stopWatch.prettyPrint());
        return pageList;
    }

    /**
     * 调整班次
     *
     * @param changeDto
     * @param userInfo
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEmpWorkCalendarShift(com.caidaocloud.attendance.service.interfaces.dto.EmpShiftChangeDto changeDto, UserInfo userInfo) throws Exception {
        Long dateLongValue = DateUtil.getTimesampByDateStr2(changeDto.getDate());
        WaEmpShiftChangeExample empShiftChangeExample = new WaEmpShiftChangeExample();
        empShiftChangeExample.createCriteria().andBelongOrgIdEqualTo(userInfo.getTenantId()).andEmpidEqualTo(changeDto.getEmpId()).
                andWorkDateEqualTo(dateLongValue).andStatusEqualTo(2).andWorkDateEqualTo(dateLongValue);
        //查询老的排班数据
        Integer oldShiftId = null;
        List<WaEmpShiftChange> empShiftChangeList = waEmpShiftChangeMapper.selectByExample(empShiftChangeExample);
        if (CollectionUtils.isNotEmpty(empShiftChangeList)) {
            oldShiftId = empShiftChangeList.get(0).getNewShiftDefId();
        } else {
            Map<Long, WaShiftDef> shiftDefMap = waCommonService.getEmpWorkShift(userInfo.getTenantId(), changeDto.getEmpId(), null, dateLongValue, dateLongValue);
            if (MapUtils.isNotEmpty(shiftDefMap)) {
                oldShiftId = shiftDefMap.get(dateLongValue).getShiftDefId();
            }
        }
        //将之前做的调整失效
        WaEmpShiftChange shiftChange = new WaEmpShiftChange();
        shiftChange.setStatus(4);
        shiftChange.setUpdtime(DateUtil.getCurrentTime(true));
        shiftChange.setUpduser(userInfo.getUserId());
        waEmpShiftChangeMapper.updateByExampleSelective(shiftChange, empShiftChangeExample);
        //添加新的班次记录
        WaEmpShiftChange waEmpShiftChange = new WaEmpShiftChange();
        waEmpShiftChange.setBelongOrgId(userInfo.getTenantId());
        waEmpShiftChange.setStatus(2);
        waEmpShiftChange.setCrttime(DateUtil.getCurrentTime(true));
        waEmpShiftChange.setCrtuser(userInfo.getUserId());
        waEmpShiftChange.setEmpid(changeDto.getEmpId());
        waEmpShiftChange.setNewShiftDefId(changeDto.getChangeShiftId());
        waEmpShiftChange.setOldShiftDefId(oldShiftId);
        waEmpShiftChange.setWorkDate(dateLongValue);
        waEmpShiftChange.setRemark(changeDto.getRemark());
        waEmpShiftChangeMapper.insertSelective(waEmpShiftChange);
    }

    /***
     * 员工替班历史记录查询
     * @param searchDto
     * @param userInfo
     * @return
     */
    public PageList<Map> getEmpShiftChangeList(EmpShiftSearchDto searchDto, UserInfo userInfo) {
        PageBean pageBean = PageUtil.getPageBean(searchDto);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc,work_date.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        Map<String, Object> params = new HashMap<>();
        params.put("belongOrgId", userInfo.getTenantId());
        params.put("empid", searchDto.getEmpId());
        if (StringUtils.isNotBlank(searchDto.getWorkDate())) {
            params.put("workDate", DateUtil.getTimesampByDateStr2(searchDto.getWorkDate()));
        }
        params.put("keywords", searchDto.getKeywords());
        if (StringUtils.isNotBlank(pageBean.getFilter())) {
            String filter = pageBean.getFilter();
            if (filter != null && filter.contains("orgid")) {
                filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
            }
            params.put("filter", filter);
        } else {
            params.put("shiftStatus", ApprovalStatusEnum.PASSED.getIndex());
        }
        if (StringUtils.isNotBlank(searchDto.getDataScope())) {
            params.put("datafilter", searchDto.getDataScope());
        }
        PageList<Map> pageList = waWorktimeDo.getEmpShiftChangePageList(pageBounds, params);
        pageList.forEach(item -> {
            String newI18n = LangParseUtil.getI18nLanguage(item.get("newI18nShiftDefName").toString(), null);
            if (StringUtil.isNotBlank(newI18n)) {
                item.put("newShiftInfo", String.format("%s %s", item.get("newShiftCode"), newI18n));
            }
            String oldI18n = LangParseUtil.getI18nLanguage(item.get("oldI18nShiftDefName").toString(), null);
            if (StringUtil.isNotBlank(oldI18n)) {
                item.put("oldShiftInfo", String.format("%s %s", item.get("oldShiftCode"), oldI18n));
            }
        });
        return pageList;
    }

    public List getShiftOptions(String belongid, Integer dateType, ShiftBelongModuleEnum belongModule) {
        List<WaShiftDo> shiftDos = waShiftDo.getWaShiftDefList(belongid, belongModule);
        if (CollectionUtils.isEmpty(shiftDos)) {
            return shiftDos;
        }
        if (dateType != null) {
            shiftDos = shiftDos.stream().filter(o -> o.getDateType().equals(dateType)).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(shiftDos)) {
            return shiftDos;
        }
        return shiftDos.stream().map(o -> {
            o.setShiftDefName(LangParseUtil.getI18nLanguage(o.getI18nShiftDefName(), o.getShiftDefName()));
            Map<String, Object> map = new HashMap<>();
            map.put("value", o.getShiftDefId());
            map.put("text", o.getShiftDefCode() + " " + o.getShiftDefName());
            map.put("dateType", o.getDateType());
            map.put("shiftCode", o.getShiftDefCode());
            map.put("shiftName", o.getShiftDefName());
            Long nowDate = DateUtil.getOnlyDate();
            if (o.getStartTime() != null && o.getStartTime() >= 0) {
                String s = DateUtil.convertDateTimeToStr(nowDate + (o.getStartTime() * 60), "HH:mm", true);
                map.put("startTimeTxt", s);
            }
            if (o.getEndTime() != null && o.getEndTime() >= 0) {
                String e = DateUtil.convertDateTimeToStr(nowDate + (o.getEndTime() * 60), "HH:mm", true);
                map.put("endTimeTxt", e);
            }
            return map;
        }).collect(Collectors.toList());
    }

    /**
     * 下载排班导入模板
     *
     * @param response
     * @param request
     * @param dto
     * @throws Exception
     */
    public void downloadEmpShiftTemplate(HttpServletResponse response, HttpServletRequest request, DownloadShiftTemplateDto dto) throws Exception {
        String yearMonth = dto.getYearMonth();
        Calendar cale = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        cale.setTime(formatter.parse(yearMonth));
        Integer year = cale.get(Calendar.YEAR);
        Integer month = cale.get(Calendar.MONTH);
        String fileName = XlsUtil.encodeFilename(String.format("%s年%s月排班导入模板.xls", year, month), request);
        response.reset();
        // 指定下载的文件名
        response.setHeader("Content-Disposition", "attachment;" + fileName);
        response.setContentType("application/vnd.ms-excel");
        response.setHeader("Pragma", "no-cache");
        response.setHeader("Cache-Control", "no-cache");
        response.setDateHeader("Expires", 0);
        OutputStream output = response.getOutputStream();
        DataOutputStream dataOutPut = new DataOutputStream(output);
        // 创建对象
        Workbook wb = new HSSFWorkbook();
        CellStyle cellStyle = wb.createCellStyle();
        // 指定单元格居中对齐
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        cellStyle.setWrapText(true);
        // 设置单元格字体
        cellStyle.setFont(this.getFont(wb, (short) 11, "宋体", null, false, false));
        // 创建sheet表
        Sheet sheet = wb.createSheet("sheet1");
        // 设置前三列
        sheet.setColumnWidth(0, 3000);
        sheet.setColumnWidth(1, 3000);
        sheet.setColumnWidth(2, 3000);
        sheet.setColumnWidth(3, 3000);
        sheet.setColumnWidth(4, 3000);
        List<Map<String, String>> titleLists = this.getSheetTimeTitles(yearMonth);
        // 设置合并行
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 0, 0));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 1, 1));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 2, 2));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 3, 3));
        sheet.addMergedRegion(new CellRangeAddress(1, 2, 4, 4));
        // 创建表格标题行 第一行
        Row titleRowOne = sheet.createRow(0);
        titleRowOne.setHeightInPoints(40f);
        CellStyle titleRowOneStyle = wb.createCellStyle();
        // 指定单元格垂直居中对齐
        titleRowOneStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定单元格居中对齐
        titleRowOneStyle.setAlignment(HorizontalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        titleRowOneStyle.setWrapText(true);
        // 设置第一行字体
        Font titleRowOneStyleFont = this.getFont(wb, (short) 12, "微软雅黑", null, Boolean.TRUE, false);
        titleRowOneStyle.setFont(titleRowOneStyleFont);
        // 设置第一行内容
        Cell titleRow1Cell1 = titleRowOne.createCell(0);
        titleRow1Cell1.setCellValue("排班周期:");
        titleRow1Cell1.setCellStyle(titleRowOneStyle);
        Cell titleRow1Cell2 = titleRowOne.createCell(1);
        titleRow1Cell2.setCellType(CellType.STRING);
        titleRow1Cell2.setCellValue(yearMonth);
        titleRow1Cell2.setCellStyle(titleRowOneStyle);
        // 日期表头，第二行
        Row titleRow2 = sheet.createRow(1);
        CellStyle titleRowStyle = this.getCellStyle(wb);
        //账号，二行一列
        Cell titleRow2Cell1 = titleRow2.createCell(0);
        titleRow2Cell1.setCellValue("账号");
        titleRow2Cell1.setCellStyle(titleRowStyle);
        //姓名，二行二列
        Cell titleRow2Cell2 = titleRow2.createCell(1);
        titleRow2Cell2.setCellValue("姓名");
        titleRow2Cell2.setCellStyle(titleRowStyle);
        //入职日期，二行三列
        Cell titleRow2Cell3 = titleRow2.createCell(2);
        titleRow2Cell3.setCellValue("入职日期");
        titleRow2Cell3.setCellStyle(titleRowStyle);
        //排班天数，二行四列
        Cell titleRow2Cell4 = titleRow2.createCell(3);
        titleRow2Cell4.setCellValue("排班天数");
        titleRow2Cell4.setCellStyle(titleRowStyle);
        //排班工时，二行五列
        Cell titleRow2Cell5 = titleRow2.createCell(4);
        titleRow2Cell5.setCellValue("排班工时");
        titleRow2Cell5.setCellStyle(titleRowStyle);
        //第三行
        Row titleRow3 = sheet.createRow(2);
        //三行一列
        Cell titleRow3Cell1 = titleRow3.createCell(0);
        titleRow3Cell1.setCellValue("");
        titleRow3Cell1.setCellStyle(titleRowStyle);
        //三行二列
        Cell titleRow3Cell2 = titleRow3.createCell(1);
        titleRow3Cell2.setCellValue("");
        titleRow3Cell2.setCellStyle(titleRowStyle);
        //三行三列
        Cell titleRow3Cell3 = titleRow3.createCell(2);
        titleRow3Cell3.setCellValue("");
        titleRow3Cell3.setCellStyle(titleRowStyle);
        //三行四列
        Cell titleRow3Cell4 = titleRow3.createCell(3);
        titleRow3Cell4.setCellValue("");
        titleRow3Cell4.setCellStyle(titleRowStyle);
        //三行五列
        Cell titleRow3Cell5 = titleRow3.createCell(4);
        titleRow3Cell5.setCellValue("");
        titleRow3Cell5.setCellStyle(titleRowStyle);
        titleRow3.setHeightInPoints(20f);
        //设置考勤日、星期标题
        for (int i = 0; i < titleLists.size(); i++) {
            // 设置日期值
            Map m = titleLists.get(i);
            sheet.setColumnWidth(i + 5, 1450);
            Cell cell1 = titleRow2.createCell(i + 5);
            cell1.setCellValue(m.get("week").toString());
            cell1.setCellStyle(titleRowStyle);
            Cell cell2 = titleRow3.createCell(i + 5);
            cell2.setCellValue(m.get("day").toString());
            cell2.setCellStyle(titleRowStyle);
        }
        String belongOrgId = sessionService.getUserInfo().getTenantId();
        //查询员工
        List<SysEmpInfo> empInfoLists = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(dto.getEmpIds())) {
            empInfoLists = sysEmpInfo.getEmpInfoByIds(belongOrgId, dto.getEmpIds());
        }
        //查询班次作为加载下拉框数据
        List<WaShiftDo> mSelects = waShiftDo.getWaShiftDefList(belongOrgId, ShiftBelongModuleEnum.ATTENDANCE);
        Sheet hiddenSelect = wb.createSheet("hidden");
        String[] s = new String[0];
        if (CollectionUtils.isNotEmpty(mSelects)) {
            s = new String[mSelects.size()];
            int size = empInfoLists.size() == 0 ? 1 : empInfoLists.size();
            for (int i = 0; i < s.length; i++) {
                WaShiftDo m1 = mSelects.get(i);
                s[i] = LangParseUtil.getI18nLanguage(m1.getI18nShiftDefName(), m1.getShiftDefName());
                Row row = hiddenSelect.createRow(i + size + 2);
                for (int j = 5; j < titleLists.size() + 5; j++) {
                    Cell cell = row.createCell(j);
                    cell.setCellValue(s[i]);
                }
            }
        }
        //设置隐藏名
        Name name = wb.createName();
        name.setNameName("hidden");
        name.setRefersToFormula("hidden!A1:A" + (s.length + empInfoLists.size() + 5));
        CellStyle dataStyle = wb.createCellStyle();
        dataStyle.setFont(this.getFont(wb, (short) 11, "宋体", null, false, false));
        // 指定单元格垂直居中对齐
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 指定当单元格内容显示不下时自动换行
        dataStyle.setWrapText(true);
        Map dataMap = dto.getRows();
        //导入数据
        for (int i = 0; i < empInfoLists.size(); i++) {
            SysEmpInfo m = empInfoLists.get(i);
            Map rowDataMap = null;
            if (dataMap != null) {
                rowDataMap = (Map) dataMap.get(m.getEmpid());
            }
            Row row = sheet.createRow(3 + i);
            Cell cell0 = row.createCell(0);
            //工号
            cell0.setCellValue(m.getWorkno());
            cell0.setCellStyle(dataStyle);
            //姓名
            Cell cell1 = row.createCell(1);
            cell1.setCellValue(m.getEmpName());
            cell1.setCellStyle(dataStyle);
            //入职日期
            Cell cell2 = row.createCell(2);
            cell2.setCellValue(m.getHireDate() == null ? "" : DateUtil.convertDateTimeToStr(m.getHireDate(), "yyyy-MM-dd", true));
            cell2.setCellStyle(dataStyle);
            //排班天数
            String shiftDays = rowDataMap == null ? "" : rowDataMap.get("shiftDays").toString();
            Cell cell3 = row.createCell(3);
            cell3.setCellValue(shiftDays);
            cell3.setCellStyle(dataStyle);
            //排班工时
            Cell cell4 = row.createCell(4);
            String workTotalTime = rowDataMap == null ? "" : rowDataMap.get("workTotalTime").toString();
            cell4.setCellValue(workTotalTime);
            cell4.setCellStyle(dataStyle);
            for (int j = 0; j < titleLists.size(); j++) {
                Cell cell = row.createCell(j + 5);
                if (rowDataMap != null) {
                    Map<String, String> title = titleLists.get(j);
                    CalendarEventDto cellValue = (CalendarEventDto) rowDataMap.get(title.get("day"));
                    cell.setCellValue(cellValue == null ? "" : cellValue.getTitle());
                } else {
                    cell.setCellValue("");
                }
                cell.setCellStyle(dataStyle);
            }
        }
        DVConstraint constraint = DVConstraint.createFormulaListConstraint("hidden");
        int lastRow = 1;
        if (CollectionUtils.isNotEmpty(empInfoLists)) {
            lastRow = empInfoLists.size();
        }
        CellRangeAddressList cellRangeAddressList = new CellRangeAddressList(3, lastRow + 2, 5, titleLists.size() + 4);
        DataValidation dataValidation = new HSSFDataValidation(cellRangeAddressList, constraint);
        sheet.addValidationData(dataValidation);
        sheet.createFreezePane(3, 3, 3, 3);
        for (int i = 3; i <= empInfoLists.size() + 3; i++) {
            Row row = sheet.getRow(i);
            if (row != null) {
                for (int j = 5; j < titleLists.size() + 5; j++) {
                    Cell cell = row.getCell(j);
                    if (cell == null) {
                        cell = row.createCell(j);
                    }
                    cell.setCellStyle(cellStyle);
                }
            }
        }
        wb.setSheetHidden(1, true);
        try {
            dataOutPut.flush();
            wb.write(dataOutPut);
            dataOutPut.close();
        } catch (IOException e) {
            dataOutPut.close();
            e.printStackTrace();
        }
    }

    /**
     * 设置单元格字体
     *
     * @param wb
     * @param fontSize
     * @param fontName
     * @param fontColor
     * @param isBold
     * @param isItalic
     * @return
     */
    private Font getFont(Workbook wb, short fontSize, String fontName, Short fontColor, boolean isBold, boolean isItalic) {
        // 设置字体
        Font font = wb.createFont();
        font.setBold(isBold);
        font.setItalic(isItalic);
        font.setFontHeightInPoints(fontSize);
        if (fontColor != null) {
            font.setColor(fontColor);
        }
        font.setFontName(fontName);
        return font;
    }

    /**
     * 设置单元格样式
     *
     * @param wb
     * @return
     */
    private CellStyle getCellStyle(Workbook wb) {
        // 设置样式
        CellStyle cellStyle = wb.createCellStyle();
        cellStyle.setFont(this.getFont(wb, (short) 11, "微软雅黑", HSSFColor.HSSFColorPredefined.WHITE.getIndex(), true, false));
        // 指定单元格垂直居中对齐
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);
        cellStyle.setFillForegroundColor(IndexedColors.LIGHT_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        return cellStyle;
    }

    /**
     * 生成表头日期
     *
     * @param yearMonth
     * @return
     * @throws Exception
     */
    private List<Map<String, String>> getSheetTimeTitles(String yearMonth) throws Exception {
        Date startDate = this.getDate(yearMonth, Boolean.TRUE);
        Date endDate = this.getDate(yearMonth, Boolean.FALSE);
        List<Map<String, String>> timeTitles = new ArrayList<>();
        Integer timeDiff = DateUtils.getIntervalDays(startDate, endDate);
        for (Integer i = 0; i <= timeDiff; i++) {
            Date d = new Date(DateUtil.addDate(startDate.getTime(), i) * 1000);
            Map<String, String> m = new HashMap<>();
            m.put("day", String.valueOf(DateUtils.getDay(d)));
            String w = DateUtil.getWeekStr(d);
            m.put("week", w.replace("星期", "周"));
            timeTitles.add(m);
        }
        return timeTitles;
    }

    /**
     * 获取日期
     *
     * @param date
     * @param isStart
     * @return
     * @throws Exception
     */
    private Date getDate(String date, boolean isStart) throws Exception {
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        Date startDate = df.parse(String.format("%s%s", date, "01"));
        if (isStart) {
            return startDate;
        }
        return new Date(DateUtil.addMonthDate(startDate.getTime(), 1, -1) * 1000);
    }

    /**
     * 导入员工排班
     *
     * @param file
     * @return
     */
    public Result<String> importEmpShift(MultipartFile file) {
        try {
            log.info("Start to import emp shift");
            DataInputStream dataInputStream = new DataInputStream(file.getInputStream());
            //Workbook wb = WorkbookFactory.create(dataInputStream);
            Workbook wb = FileUtil.createWorkbook(file.getOriginalFilename(), dataInputStream);
            if (null == wb) {
                //return Result.fail("解析导入文件的Workbook错误，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.PARSE_WORKBOOK_ERR, "解析导入文件的Workbook错误，请检查后重新上传");
            }
            Sheet sheet = wb.getSheet("sheet1");
            if (null == sheet) {
                //return Result.fail("解析导入文件的Sheet错误，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.PARSE_SHEET_ERR, "解析导入文件的Sheet错误，请检查后重新上传");
            }
            int firstRowNum = sheet.getFirstRowNum();
            int lastRowNum = sheet.getLastRowNum();
            if (lastRowNum == firstRowNum || lastRowNum <= 2) {
                //return Result.fail("导入文件为空，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, "导入文件为空，请检查后重新上传");
            }
            Row rowOne = sheet.getRow(0);
            Cell cellOne = rowOne.getCell(1);
            cellOne.setCellType(CellType.STRING);
            String yearMonth = cellOne.getStringCellValue();
            Date startDate = this.getDate(yearMonth, Boolean.TRUE);
            Date endDate = this.getDate(yearMonth, Boolean.FALSE);
            Integer dayDiff = DateUtils.getIntervalDays(startDate, endDate);
            Row weekRow = sheet.getRow(1);
            Row dayRow = sheet.getRow(2);
            int lastCellNum = weekRow.getLastCellNum();
            if (!dayDiff.equals(lastCellNum - 6)) {
                //return Result.fail("导入文件排班周期与表头日期不匹配，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_NOT_MATCH, "");
            }
            if (lastCellNum != dayRow.getLastCellNum()) {
                //return Result.fail("导入文件表头日期长度不匹配，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_HEAD_LENGTH_NOT_MATCH, "");
            }
            //查询租户下所有的班次
            UserInfo userInfo = sessionService.getUserInfo();
            String belongOrgId = userInfo.getTenantId();
            List<WaShiftDo> orgShifts = waShiftDo.getWaShiftDefList(belongOrgId, ShiftBelongModuleEnum.ATTENDANCE);
            Map<String, Integer> orgShiftMap = orgShifts.stream().collect(Collectors.toMap(WaShiftDo::getShiftDefName, WaShiftDo::getShiftDefId));
            List<ImportRowDto> rows = new ArrayList<>();
            for (int i = 3; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (null != row) {
                    Cell workNoCell = row.getCell(0);
                    String workNo = "";
                    if (workNoCell != null) {
                        workNoCell.setCellType(CellType.STRING);
                        workNo = workNoCell.getStringCellValue().trim();
                    }
                    boolean shiftNameNotBlank = false;
                    Integer rowNum = i + 1;
                    List<ImportCellDto> cells = new ArrayList<>();
                    //第六列
                    for (int j = 5; j <= lastCellNum; j++) {
                        Cell cell = row.getCell(j);
                        if (null == cell) {
                            cell = row.createCell(j);
                            cell.setCellValue("");
                        }
                        cell.setCellType(CellType.STRING);
                        String shiftName = StringUtil.isNotBlank(cell.getStringCellValue()) ? cell.getStringCellValue().trim() : "";
                        String coordinate = CellReference.convertNumToColString(cell.getColumnIndex()) + rowNum;
                        if (StringUtil.isNotBlank(shiftName)) {
                            if (!orgShiftMap.containsKey(shiftName) || orgShiftMap.get(shiftName) == null) {
                                //return Result.fail(String.format("%s单元格的班次%s不存在，请检查后重新上传", coordinate, shiftName));
                                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_SHIFT_NOT_EXIST, "").getMsg(), coordinate, shiftName));
                            }
                            shiftNameNotBlank = true;
                            cells.add(new ImportCellDto(coordinate, shiftName, orgShiftMap.get(shiftName), DateUtil.addDate(startDate.getTime(), j - 5)));
                        }
                    }
                    if (StringUtil.isBlank(workNo) && !shiftNameNotBlank) {
                        continue;
                    }
                    if (StringUtil.isNotBlank(workNo) && !shiftNameNotBlank) {
                        //return Result.fail(String.format("第%s行班次为空，请检查后重新上传", rowNum));
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_SHIFT_EMPTY, "").getMsg(), rowNum));
                    }
                    if (StringUtil.isBlank(workNo) && shiftNameNotBlank) {
                        //return Result.fail(String.format("第%s行账号为空，请检查后重新上传", rowNum));
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_EMPTY, "").getMsg(), rowNum));
                    }
                    if (StringUtil.isNotBlank(workNo)) {
                        rows.add(new ImportRowDto(workNo, rowNum, cells));
                    }
                }
            }
            log.info("End to import emp shift, data:[rows:{}]", JSONUtils.ObjectToJson(rows));
            if (CollectionUtils.isEmpty(rows)) {
                //return Result.fail("导入文件账号列为必填项，请检查后重新上传");
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_MUST, "");
            }
            Map<String, Long> workNoCountMap = rows.stream().collect(Collectors.groupingBy(ImportRowDto::getWorkNo, Collectors.counting()));
            Optional<String> optional = workNoCountMap.keySet().stream().filter(workNo -> workNoCountMap.get(workNo) > 1).findFirst();
            if (optional.isPresent()) {
                //return Result.fail(String.format("导入文件账号%s存在重复，请检查后重新上传", optional.get()));
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_REPEAT, "").getMsg(), optional.get()));
            }
            List<String> workNos = rows.stream().map(ImportRowDto::getWorkNo).distinct().collect(Collectors.toList());
            List<SysEmpInfo> empList = sysEmpInfo.getEmpInfoByWorkNos(belongOrgId, workNos);
            if (CollectionUtils.isEmpty(empList)) {
                //return Result.fail("请在导入文件的账号列填写正确的账号");
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_ERR, "");
            }
            Map<String, Long> workNoAndEmpIdMap = empList.stream().collect(Collectors.toMap(SysEmpInfo::getWorkno, SysEmpInfo::getEmpid));
            Long startTime = startDate.getTime() / 1000;
            Long endTime = endDate.getTime() / 1000;
            List<EmpShiftChangeDto> addList = new ArrayList<>();
            List<EmpShiftChangeDto> updateList = new ArrayList<>();
            for (ImportRowDto row : rows) {
                String workNo = String.valueOf(row.getWorkNo());
                if (!workNoAndEmpIdMap.containsKey(workNo) || workNoAndEmpIdMap.get(workNo) == null) {
                    //return Result.fail(String.format("第%s行的账号%s不存在，请检查后重新上传", row.getRowNum(), workNo));
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_NOT_EXIST, "").getMsg(), row.getRowNum(), workNo));
                }
                Long empId = workNoAndEmpIdMap.get(workNo);
                List<EmpShiftChangeDto> empShiftChanges = empShiftChangeService.getEmpShiftChanges(Collections.singletonList(empId), startTime, endTime, EmpShiftChangeStatus.APPROVED.ordinal());
                Map<Long, EmpShiftChangeDto> workDateMap = empShiftChanges.stream().collect(Collectors.toMap(EmpShiftChangeDto::getWorkDate, Function.identity(), (k1, k2) -> k2));
                List<ImportCellDto> cells = row.getCells();
                cells.forEach(cell -> {
                    Long workDate = cell.getWorkDate();
                    EmpShiftChangeDto newEmpShiftChange = this.getEmpShiftDetailDto(empId, cell.getWorkDate(), orgShiftMap.get(cell.getShiftName()), null, userInfo);
                    if (workDateMap.containsKey(workDate)) {
                        EmpShiftChangeDto dto = workDateMap.get(workDate);
                        dto.setStatus(EmpShiftChangeStatus.INVALID.ordinal());
                        dto.setUpduser(userInfo.getUserId());
                        dto.setUpdtime(System.currentTimeMillis() / 1000);
                        updateList.add(dto);
                        newEmpShiftChange.setOldShiftDefId(dto.getNewShiftDefId());
                    }
                    addList.add(newEmpShiftChange);
                });
            }
            // 更新
            if (CollectionUtils.isNotEmpty(updateList)) {
                log.info("Update emp shift, data:[updateList:{}]", JSONUtils.ObjectToJson(updateList));
                empShiftChangeService.updateEmpShiftChanges(updateList);
            }
            // 新增
            if (CollectionUtils.isNotEmpty(addList)) {
                log.info("Save emp shift, data:[addList:{}]", JSONUtils.ObjectToJson(addList));
                empShiftChangeService.saveEmpShiftChanges(addList);
            }
        } catch (FileNotFoundException fne) {
            log.error("File not exists {}", fne.getMessage(), fne);
            return Result.fail("File not exists");
        } catch (IOException ie) {
            log.error("File parsing failed {}", ie.getMessage(), ie);
            return Result.fail("File parsing failed");
        } catch (Exception e) {
            log.error("An exception occurred while importing the file, {}", e.getMessage(), e);
            return Result.fail("An exception occurred while importing the file");
        }
        return Result.ok("success");
    }

    /**
     * 生成排班变更实体对象
     *
     * @param empId
     * @param workDate
     * @param shiftId
     * @param olShiftId
     * @param userInfo
     * @return
     */
    private EmpShiftChangeDto getEmpShiftDetailDto(Long empId, Long workDate, Integer shiftId, Integer olShiftId, UserInfo userInfo) {
        EmpShiftChangeDto dto = new EmpShiftChangeDto();
        dto.setBelongOrgId(userInfo.getTenantId());
        dto.setEmpid(empId);
        dto.setNewShiftDefId(shiftId);
        dto.setOldShiftDefId(olShiftId);
        dto.setStatus(EmpShiftChangeStatus.APPROVED.ordinal());
        dto.setWorkDate(workDate);
        dto.setCrtuser(userInfo.getUserId());
        dto.setCrttime(System.currentTimeMillis() / 1000);
        dto.setUpduser(userInfo.getUserId());
        dto.setUpdtime(System.currentTimeMillis() / 1000);
        return dto;
    }

    /**
     * 导出员工排班
     *
     * @param response
     * @param request
     * @param dto
     */
    public void exportEmpShift(HttpServletResponse response, HttpServletRequest request, EmpWorkCalendarSearchDto dto) throws Exception {
        log.info("Start to export emp shift param:[{}]", JSONUtils.ObjectToJson(dto));
        dto.setPageSize(10000);
        dto.setIsExport(Boolean.FALSE);
        Map dataMap = new HashMap();
        List<Long> empIds = new ArrayList<>();
        PageList<Map> pageList = this.searchEmpWorkTimeDetailsByMonth(dto, sessionService.getUserInfo());
        log.info("Ready to export emp shift pageList:[{}]", JSONUtils.ObjectToJson(pageList));
        if (CollectionUtils.isNotEmpty(pageList)) {
            for (Map empRow : pageList) {
                Long empId = (Long) empRow.get("empid");
                empIds.add(empId);
                dataMap.put(empId, empRow);
            }
        }
        this.downloadEmpShiftTemplate(response, request, new DownloadShiftTemplateDto(String.valueOf(dto.getYm()), empIds, dataMap));
        log.info("End to export emp shift dataMap:[{}]", JSONUtils.ObjectToJson(dataMap));
    }
}