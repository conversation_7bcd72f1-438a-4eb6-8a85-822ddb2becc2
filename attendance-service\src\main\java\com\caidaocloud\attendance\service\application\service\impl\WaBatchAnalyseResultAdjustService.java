package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidao1.wa.mybatis.model.WaWorktimeDetail;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.mobile.utils.JsonTool;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.workflow.dto.WfAttachmentDto;
import com.caidaocloud.attendance.core.workflow.dto.WfBusinessDataDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfDetailDto;
import com.caidaocloud.attendance.core.workflow.dto.WfResponseDto;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.application.dto.BatchAnalyseResultAdjustDetailDto;
import com.caidaocloud.attendance.service.application.dto.BatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.application.dto.emp.EmpOrgPostInfo;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.application.service.emp.WaEmpService;
import com.caidaocloud.attendance.service.application.service.user.WorkFlowApprovalProcessorService;
import com.caidaocloud.attendance.service.application.service.workflow.WorkflowCallBackService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.interfaces.dto.BatchAnalyseResultAdjustQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeBatchAnalyseResultAdjustDto;
import com.caidaocloud.attendance.service.interfaces.dto.common.WaKeyValue;
import com.caidaocloud.attendance.service.interfaces.dto.user.WaClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchAnalyseResultAdjustPageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaAbnormalResultVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.TimeSlot;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hr.workflow.enums.WfAttachmentTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.*;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.dto.WfRevokeDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 批量考勤异常调整
 *
 * <AUTHOR>
 * @Date 2024/6/25
 */
@Slf4j
@Service
public class WaBatchAnalyseResultAdjustService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1),
            snowflakeUtil2 = new SnowflakeUtil(0, 1),
            snowflakeUtil3 = new SnowflakeUtil(1, 0);
    @Autowired
    private WaSobService waSobService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private IWfService wfService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private WorkflowCallBackService workflowCallBackService;
    @Autowired
    private WaSobDo waSobDo;
    @Autowired
    private WaParseGroupDo waParseGroupDo;
    @Autowired
    private SysEmpInfoDo sysEmpInfoDo;
    @Autowired
    private WaBatchAnalyseResultAdjustDo waBatchAnalyseResultAdjustDo;
    @Autowired
    private WaAnalyseResultAdjustDo waAnalyseResultAdjustDo;
    @Autowired
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private WaEmpService waEmpService;
    @Autowired
    private WaClockPlan waClockPlanService;
    @Autowired
    private WaRegisterRecordBdkDo registerRecordBdkDo;
    @Autowired
    private WaGroupDo waGroupDo;
    @Autowired
    private WorkFlowApprovalProcessorService workFlowApprovalProcessorService;

    /**
     * 查询考勤异常数据
     *
     * @param empid
     * @param startDate
     * @param endDate
     * @return
     */
    public List<WaAbnormalResultVo> getWaResultList(Long empid, Long startDate, Long endDate) {
        UserInfo userInfo = UserContext.getAndCheckUser();
        // 查询员工考勤异常数据
        List<WaAnalyze> errorAnalyzeList = waBatchAnalyseResultAdjustDo.getEmpWaAnalyzeList(userInfo.getTenantId(), empid,
                startDate, endDate, true);
        if (CollectionUtils.isEmpty(errorAnalyzeList)) {
            return Lists.newArrayList();
        }
        // 查询已经调整过的考勤数据
        List<WaAnalyseResultAdjustDo> adjustList = waAnalyseResultAdjustDo.listByDateRange(empid,
                startDate, endDate, Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isNotEmpty(adjustList)) {
            List<Long> adjustDateList = adjustList.stream().map(WaAnalyseResultAdjustDo::getBelongDate).distinct().collect(Collectors.toList());
            errorAnalyzeList = errorAnalyzeList.stream().filter(o -> !adjustDateList.contains(o.getBelongDate())).collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(errorAnalyzeList)) {
            return Lists.newArrayList();
        }
        // 排除当前以及未来日期的数据
        Long curDate = DateUtil.getOnlyDate();
        errorAnalyzeList = errorAnalyzeList.stream().filter(o -> o.getBelongDate() < curDate).collect(Collectors.toList());
        return errorAnalyzeList.stream().map(WaAbnormalResultVo::convert2Vo).collect(Collectors.toList());
    }

    /**
     * 根据日期查询当天考勤异常数据
     *
     * @param empid
     * @param date
     * @return
     * @throws Exception
     */
    public WaAbnormalResultVo getWaResult(Long empid, Long date) throws Exception {
        UserInfo userInfo = UserContext.getAndCheckUser();

        WaSob waSob = waSobService.getWaSob(empid, date);
        if (waSob != null) {
            if (null != waSob.getStatus() && waSob.getStatus() == 1) {// 帐套已关闭
                throw new ServerException(messageResource.getMessage("L006000", new Object[]{}, new Locale(SessionHolder.getLang())));
            }
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            Long sobEndDate = waSob.getSobEndDate();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            if (DateUtil.getOnlyDate() > sobEndDate) {
                //throw new ServerException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
                throw new ServerException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
            }
        }

        // 检查是否有做过考勤异常调整
        List<WaAnalyseResultAdjustDo> adjustList = waAnalyseResultAdjustDo.listByDate(empid, date,
                Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isNotEmpty(adjustList)) {
            throw new ServerException(messageResource.getMessage("L006114", new Object[]{}, new Locale(SessionHolder.getLang())));
        }

        // 未考勤分析的数据不支持异常调整
        List<WaAnalyze> analyzeList = waBatchAnalyseResultAdjustDo.getEmpWaAnalyzeList(userInfo.getTenantId(), empid,
                date, date, false);
        if (CollectionUtils.isEmpty(analyzeList)) {
            throw new ServerException(messageResource.getMessage("L006115", new Object[]{}, new Locale(SessionHolder.getLang())));
        }
        return WaAbnormalResultVo.convert2Vo(analyzeList.get(0));
    }

    public WaClockPlanDto getWaClockPlanDto(String tenantId, Long staffId) {
        long currentTime = DateUtil.getOnlyDate();
        WaClockPlan waClockPlan = waClockPlanService.getMyWaClockPlan(ConvertHelper.longConvert(tenantId), tenantId, staffId, currentTime);
        if (null == waClockPlan) {
            return null;
        }
        return ObjectConverter.convert(waClockPlan, WaClockPlanDto.class);
    }

    /**
     * 检查异常申请数据
     *
     * @param batchAdjustDto
     * @return
     */
    public void check(BatchAnalyseResultAdjustDto batchAdjustDto) throws Exception {
        List<AnalyseResultAdjustDto> adjustDtoList = batchAdjustDto.getAdjustDetailList();
        if (CollectionUtils.isEmpty(adjustDtoList)) {
            // 请求参数异常
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201910", WebUtil.getRequest()));
        }
        adjustDtoList.sort(Comparator.comparing(AnalyseResultAdjustDto::getBelongDate));
        long minStartDate = adjustDtoList.get(0).getBelongDate();
        long maxEndDate = adjustDtoList.get(adjustDtoList.size() - 1).getBelongDate();

        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(UserContext.getTenantId(), batchAdjustDto.getEmpid());
        if (null == empInfo) {
            // 员工不存在
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10016", WebUtil.getRequest()));
        }
        // 查询员工排班
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(),
                batchAdjustDto.getEmpid(), tmType, minStartDate - 86400, maxEndDate + 86400, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(pbMap)) {
            // 未排班
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
        }
        Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(empInfo.getBelongOrgId());
        if (MapUtils.isEmpty(shiftMap)) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
        }
        WaSobDo waSob = waSobDo.getById(empInfo.getBelongOrgId(), batchAdjustDto.getWaSobId());
        if (null == waSob) {
            //throw new ServerException("未设置考勤周期");
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WA_CYCLE_NOT_SET, null).getMsg());
        }
        if (waSob.getStatus().equals(CycleStatusEnum.ARCHIVED.getIndex())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WA_SOB_ISLOCK, Boolean.FALSE).getMsg());
        }
        Integer sysPeriodMonth = waSob.getSysPeriodMonth();
        Long sobEndDate = waSob.getSobEndDate();
        String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
        String[] dateList = enDate.split("-");
        if (DateUtil.getOnlyDate() > sobEndDate) {
            //throw new ServerException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员");
            throw new ServerException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
        }

        // 补打卡上限校验
        // 获取员工打卡方案
        WaClockPlanDto clockPlanDto = getWaClockPlanDto(empInfo.getBelongOrgId(), empInfo.getEmpid());
        if (null == clockPlanDto) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.NO_CHECK_IN_PLAN_SET_UP, null).getMsg());
        }
        if (BooleanUtils.isTrue(clockPlanDto.getIsSupplement()) && clockPlanDto.getSupplementCount() != null) {
            Integer bdkCount = registerRecordBdkDo.getEmpDkCountByType(empInfo.getEmpid(), ClockWayEnum.FILLCLOCK.getIndex(),
                    waSob.getStartDate(), waSob.getEndDate());
            if (bdkCount != null && bdkCount >= clockPlanDto.getSupplementCount()) {
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.BDK_NUM_EXCEEDED_UPPER_LIMIT, null).getMsg());
            }
        }

        // 查询申请中的考勤调整数据
        List<Long> adjustDateList = new ArrayList<>();
        List<WaAnalyseResultAdjustDo> adjustList = waAnalyseResultAdjustDo.listByDateRange(batchAdjustDto.getEmpid(),
                minStartDate, maxEndDate, Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()));
        if (CollectionUtils.isNotEmpty(adjustList)) {
            adjustDateList = adjustList.stream().map(WaAnalyseResultAdjustDo::getBelongDate).distinct().collect(Collectors.toList());
        }

        for (AnalyseResultAdjustDto adjustDto : adjustDtoList) {
            if (adjustDto.getSigninTime() == null && adjustDto.getSignoffTime() == null) {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_202847", WebUtil.getRequest()));
            }
            long belongDate = adjustDto.getBelongDate();
            // 检查是否有审批中的数据
            if (adjustDateList.contains(belongDate)) {
                //throw new ServerException(DateUtil.getDateStrByTimesamp(belongDate) + "已提交异常申请");
                throw new ServerException(String.format(ResponseWrap.wrapResult(AttendanceCodes.RESULT_ADJUST_APPLY_SUBMITTED, null).getMsg(), DateUtil.getDateStrByTimesamp(belongDate)));
            }
            // 排班检查
            if (null == pbMap.get(belongDate)) {
                throw new ServerException(DateUtil.getDateStrByTimesamp(belongDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
            }
            Integer shiftDefId = pbMap.get(belongDate).getShiftDefId();
            WaShiftDef shiftDef = shiftMap.get(shiftDefId);
            if (null == shiftDef) {
                throw new ServerException(DateUtil.getDateStrByTimesamp(belongDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
            }
            // 检查员工补卡时间是否在设置的范围内
            checkTimeRule(shiftDef.getClockTimeLimit(), adjustDto.getSigninTime());

            if (shiftDef.getOnDutyStartTime() == null || shiftDef.getOnDutyEndTime() == null) {
                // 未设置上班打卡时间
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201914", WebUtil.getRequest()));
            }
            if (shiftDef.getOffDutyStartTime() == null || shiftDef.getOffDutyEndTime() == null) {
                // 未设置下班打卡时间
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201915", WebUtil.getRequest()));
            }
            if (adjustDto.getSigninTime() != null) {
                long signinDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(adjustDto.getSigninTime()));
                if (signinDate != belongDate && pbMap.get(signinDate) == null) {
                    throw new ServerException(DateUtil.getDateStrByTimesamp(belongDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
                }
            }
            if (adjustDto.getSignoffTime() != null) {
                long signoffDate = DateUtil.getTimesampByDateStr2(DateUtil.getDateStrByTimesamp(adjustDto.getSignoffTime()));
                if (signoffDate != belongDate && pbMap.get(signoffDate) == null) {
                    throw new ServerException(DateUtil.getDateStrByTimesamp(belongDate) + MessageHandler.getMessage("caidao.exception.error_10017", WebUtil.getRequest()));
                }
            }
        }
    }

    /**
     * 检查员工补卡时间是否在设置的范围内
     *
     * @param timeSlotStr
     * @param regDateTime
     */
    private void checkTimeRule(String timeSlotStr, Long regDateTime) {
        if (StringUtil.isBlank(timeSlotStr) || null == regDateTime) {
            return;
        }
        List<com.caidaocloud.dto.TimeSlot> timeSlots = JsonTool.json2list(timeSlotStr, TimeSlot.class);
        if (CollectionUtils.isEmpty(timeSlots)) {
            return;
        }
        long regTime = (regDateTime - DateUtil.getOnlyDate(new Date(regDateTime * 1000L))) / 60;
        if (timeSlots.stream().noneMatch(t -> {
            Integer startTime = t.getStartTime();
            Integer endTime = t.getEndTime();
            if (startTime >= endTime) {
                endTime += 1440;
            }
            return startTime <= regTime && endTime >= regTime;
        })) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.TIME_NOT_ALLOWED, null).getMsg());
        }
    }

    /**
     * 保存异常申请数据
     *
     * @param adjustDto
     * @return
     * @throws Exception
     */
    public boolean save(BatchAnalyseResultAdjustDto adjustDto) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(UserContext.getTenantId(), adjustDto.getEmpid());
        if (null == empInfo) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, Boolean.FALSE).getMsg());
        }
        // 检查流程是否已启用
        Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.BATCH_ANALYSE_ADJUST.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
        }
        Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
        // 保存单据
        WaBatchAnalyseResultAdjustDo batchData = SpringUtil.getBean(WaBatchAnalyseResultAdjustService.class)
                .saveApply(adjustDto, empInfo);
        // 开启工作流
        String wfBusKey = String.format("%s_%s", batchData.getBatchId(), BusinessCodeEnum.BATCH_ANALYSE_ADJUST.getCode());
        if (!workflowEnabledResultData) {
            WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
            wfCallbackResultDto.setBusinessKey(wfBusKey);
            wfCallbackResultDto.setCallbackType(WfCallbackTriggerOperationEnum.APPROVED);
            wfCallbackResultDto.setTenantId(batchData.getTenantId());
            workflowCallBackService.saveBatchAnalyseAdjustApproval(wfCallbackResultDto);
        } else {
            WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
            wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.BATCH_ANALYSE_ADJUST.getCode());
            wfBeginWorkflowDto.setBusinessId(String.valueOf(batchData.getBatchId()));
            wfBeginWorkflowDto.setApplicantId(batchData.getEmpid().toString());
            wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
            wfBeginWorkflowDto.setEventTime(System.currentTimeMillis());
            startWorkflow(wfBeginWorkflowDto, batchData);
        }
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public WaBatchAnalyseResultAdjustDo saveApply(BatchAnalyseResultAdjustDto adjustDto, SysEmpInfo empInfo) {
        Long batchId = snowflakeUtil.createId();
        // 调整明细
        List<WaAnalyseResultAdjustDo> adjustList = FastjsonUtil.convertList(adjustDto.getAdjustDetailList(),
                WaAnalyseResultAdjustDo.class);
        adjustList.sort(Comparator.comparing(WaAnalyseResultAdjustDo::getBelongDate));
        adjustList.forEach(adjust -> {
            adjust.setAdjustId(snowflakeUtil2.createId());
            adjust.doInitCrtField();
            adjust.setBatchId(batchId);
            adjust.setEmpid(empInfo.getEmpid());
            adjust.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
            waAnalyseResultAdjustDo.save(adjust);
        });
        // 调整单据
        WaBatchAnalyseResultAdjustDo batchData = new WaBatchAnalyseResultAdjustDo();
        batchData.setBatchId(batchId);
        batchData.setWaSobId(adjustDto.getWaSobId());
        batchData.setEmpid(empInfo.getEmpid());
        batchData.setStartDate(adjustList.get(0).getBelongDate());
        batchData.setEndDate(adjustList.get(adjustList.size() - 1).getBelongDate());
        batchData.setFileName(adjustDto.getFileName());
        batchData.setFilePath(adjustDto.getFilePath());
        batchData.setBusinessKey(String.format("%s_%s", batchId, BusinessCodeEnum.BATCH_ANALYSE_ADJUST.getCode()));
        batchData.setStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        batchData.doInitCrtField();
        waBatchAnalyseResultAdjustDo.save(batchData);
        return batchData;
    }

    public void startWorkflow(WfBeginWorkflowDto wfBeginWorkflowDto, WaBatchAnalyseResultAdjustDo batchData) {
        Result<?> result = null;
        try {
            result = wfRegisterFeign.begin(wfBeginWorkflowDto);
            log.debug("BatchAnalyseResultAdjust startWorkflow result={}", FastjsonUtil.toJsonStr(result));
        } catch (Exception e) {
            log.error("{}", e.getMessage(), e);
            waAnalyseResultAdjustDo.deleteByBatchId(batchData.getBatchId());
            waBatchAnalyseResultAdjustDo.deleteById(batchData.getBatchId());
            if (e instanceof ServerException) {
                if (null != e.getMessage() && e.getMessage().startsWith("No outgoing sequence flow")) {
                    throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
                } else {
                    throw e;
                }
            } else {
                throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201916", WebUtil.getRequest()));
            }
        }
        if (null == result || !result.isSuccess() || null == result.getData()
                || StringUtils.isBlank(result.getData().toString())) {
            waAnalyseResultAdjustDo.deleteByBatchId(batchData.getBatchId());
            waBatchAnalyseResultAdjustDo.deleteById(batchData.getBatchId());
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_START_ERR, null).getMsg());
        }
    }

    /**
     * 批量异常申请通过后同步更新员工考勤数据
     *
     * @param batchAdjustDo
     */
    @Transactional(rollbackFor = Exception.class)
    public void updateEmpAnalyseResult(WaBatchAnalyseResultAdjustDo batchAdjustDo) {
        if (null == batchAdjustDo || null == batchAdjustDo.getBatchId()) {
            log.warn("updateEmpAnalyseResult fail batchAdjustDo empty");
            return;
        }
        List<WaAnalyseResultAdjustDo> adjustDoList = waAnalyseResultAdjustDo.listByBatchId(batchAdjustDo.getBatchId());
        if (CollectionUtils.isEmpty(adjustDoList)) {
            log.warn("updateEmpAnalyseResult fail adjustDoList empty batchId={}", batchAdjustDo.getBatchId());
            return;
        }
        adjustDoList.sort(Comparator.comparing(WaAnalyseResultAdjustDo::getBelongDate));
        long startDate = adjustDoList.get(0).getBelongDate();
        long endDate = adjustDoList.get(adjustDoList.size() - 1).getBelongDate();

        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(batchAdjustDo.getTenantId(), batchAdjustDo.getEmpid());
        if (null == empInfo) {
            log.warn("updateEmpAnalyseResult fail empInfo empty batchId={}", batchAdjustDo.getBatchId());
            return;
        }
        Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(empInfo.getBelongOrgId());
        if (MapUtils.isEmpty(shiftMap)) {
            log.warn("updateEmpAnalyseResult fail shiftMap empty batchId={}, tenantId={}", batchAdjustDo.getBatchId(), empInfo.getBelongOrgId());
            return;
        }
        int tmType = (empInfo.getTmType() == null || empInfo.getTmType() == 0) ? 1 : empInfo.getTmType();
        Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(),
                empInfo.getEmpid(), tmType, startDate - 86400, endDate + 86400, empInfo.getWorktimeType(), true);
        if (MapUtils.isEmpty(pbMap)) {
            log.warn("updateEmpAnalyseResult fail pbMap empty batchId={}, tenantId={}", batchAdjustDo.getBatchId(), empInfo.getBelongOrgId());
            return;
        }
        String tenantId = batchAdjustDo.getTenantId();
        Long createBy = batchAdjustDo.getCreateBy();
        Long empId = batchAdjustDo.getEmpid();

        WaSobDo waSob = waSobDo.getById(empInfo.getBelongOrgId(), batchAdjustDo.getWaSobId());
        if (null == waSob) {
            log.warn("updateEmpAnalyseResult fail waSob empty batchId={}, sobId={}", batchAdjustDo.getBatchId(), batchAdjustDo.getWaSobId());
            return;
        }
        WaGroupDo waGroup = waGroupDo.getById(waSob.getWaGroupId());
        if (null == waGroup) {
            log.warn("updateEmpAnalyseResult fail waGroup empty batchId={}, waGroupId={}", batchAdjustDo.getBatchId(), waSob.getWaGroupId());
            return;
        }
        WaParseGroupDo waParseGroup = waParseGroupDo.getById(waGroup.getParseGroupId());
        if (null == waParseGroup) {
            log.warn("updateEmpAnalyseResult fail waParseGroup empty batchId={}, parseGroupId={}", batchAdjustDo.getBatchId(), waGroup.getParseGroupId());
            return;
        }
        for (WaAnalyseResultAdjustDo adjustDo : adjustDoList) {
            Long belongDate = adjustDo.getBelongDate();
            if (!pbMap.containsKey(belongDate) || null == pbMap.get(belongDate)) {
                log.warn("updateEmpAnalyseResult fail dateShift empty batchId={}, belongDate={}", batchAdjustDo.getBatchId(), belongDate);
                continue;
            }
            WaWorktimeDetail worktimeDetail = pbMap.get(belongDate);
            if (!shiftMap.containsKey(worktimeDetail.getShiftDefId()) || null == shiftMap.get(worktimeDetail.getShiftDefId())) {
                log.warn("updateEmpAnalyseResult fail worktimeDetail empty batchId={}, belongDate={}", batchAdjustDo.getBatchId(), belongDate);
                continue;
            }
            WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());

            // 调整后的签到时间
            Long signinTime = adjustDo.getSigninTime();
            if (signinTime != null) {
                saveBdkRegister(tenantId, createBy, empId, signinTime, belongDate, shiftDef, waParseGroup);
            }
            // 调整后的签退时间
            Long signoffTime = adjustDo.getSignoffTime();
            if (signoffTime != null) {
                saveBdkRegister(tenantId, createBy, empId, signoffTime, belongDate, shiftDef, waParseGroup);
            }
        }
    }

    /**
     * 保存补打卡数据
     *
     * @param tenantId
     * @param createBy
     * @param empId
     * @param regDateTime
     * @param belongDate
     * @param shiftDef
     * @param waParseGroup
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBdkRegister(String tenantId, Long createBy, Long empId, Long regDateTime,
                                Long belongDate, WaShiftDef shiftDef, WaParseGroupDo waParseGroup) {
        // 保存补卡申请
        WaRegisterRecordBdkDo recordBdkDo = new WaRegisterRecordBdkDo();
        recordBdkDo.setRegDateTimes(regDateTime + "");
        recordBdkDo.setRegDateTime(regDateTime);
        recordBdkDo.setBelongDate(belongDate);
        // 设置打卡类型(一次卡)
        if (ClockParseEnum.ONE_TIME_CARD.getIndex().equals(waParseGroup.getClockType())) {
            recordBdkDo.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
        }
        recordBdkDo.setShiftDefId(shiftDef.getShiftDefId());
        recordBdkDo.setCrttime(DateUtil.getCurrentTime(Boolean.TRUE));
        recordBdkDo.setCrtuser(createBy);
        recordBdkDo.setApprovalStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        recordBdkDo.setEmpid(empId);
        recordBdkDo.setIsWorkflow(Boolean.TRUE);
        recordBdkDo.setType(ClockWayEnum.FILLCLOCK.getIndex());
        recordBdkDo.setResultType(ClockResultEnum.NORMAL.getIndex());
        recordBdkDo.setCorpid(ConvertHelper.longConvert(tenantId));
        recordBdkDo.setBelongOrgId(tenantId);
        recordBdkDo.setRecordId(snowflakeUtil3.createId());
        registerRecordBdkDo.save(recordBdkDo);

        // 保存补卡记录
        WaRegisterRecordDo registerRecordDo = new WaRegisterRecordDo();
        registerRecordDo.setBelongDate(belongDate);
        registerRecordDo.setRegDateTime(regDateTime);
        registerRecordDo.setCrtuser(createBy);
        registerRecordDo.setCrttime(DateUtil.getCurrentTime(Boolean.TRUE));
        registerRecordDo.setShiftDefId(shiftDef.getShiftDefId());
        registerRecordDo.setEmpid(empId);
        registerRecordDo.setIsWorkflow(Boolean.TRUE);
        registerRecordDo.setApprovalStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        registerRecordDo.setResultType(ClockResultEnum.NORMAL.getIndex());
        registerRecordDo.setType(ClockWayEnum.FILLCLOCK.getIndex());
        registerRecordDo.setCorpid(ConvertHelper.longConvert(tenantId));
        registerRecordDo.setBelongOrgId(tenantId);
        registerRecordDo.setBdkRecordId(recordBdkDo.getRecordId());
        waRegisterRecordDo.batchSave(Lists.newArrayList(registerRecordDo));

        // 补打卡审批回掉
        workFlowApprovalProcessorService.finishedBdkApproval(recordBdkDo.getRecordId(), WfCallbackTriggerOperationEnum.APPROVED);
    }

    /**
     * 分页列表
     *
     * @param pageBean
     * @param dto
     * @param userInfo
     * @return
     */
    public PageList<BatchAnalyseResultAdjustPageListVo> getPageList(PageBean pageBean, BatchAnalyseResultAdjustQueryDto dto, UserInfo userInfo) {
        Map<String, Object> queryParam = new HashMap<>();
        queryParam.put("tenantId", userInfo.getTenantId());
        queryParam.put("keywords", pageBean.getKeywords());
        queryParam.put("datafilter", dto.getDataScope());
        queryParam.put("empid", dto.getEmpid());
        doParseFilterField(pageBean, queryParam);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "ei.orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
//        if (filter == null || !filter.contains("status")) {
//            filter = filter+"and \"status\" in ('1')" ;
//        }
        queryParam.put("filter", filter);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "crttime.desc" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        PageList<Map> pageList = waBatchAnalyseResultAdjustDo.getPageList(pageBounds, queryParam);
        if (CollectionUtils.isEmpty(pageList)) {
            return new PageList<>(Lists.newArrayList(), pageList.getPaginator());
        }
        List<BatchAnalyseResultAdjustPageListVo> listVos = pageList.stream().map(data -> {
            BatchAnalyseResultAdjustPageListVo listVo = new BatchAnalyseResultAdjustPageListVo();
            listVo.setBatchId((Long) data.get("batch_id"));
            listVo.setWorkno((String) data.get("workno"));
            listVo.setEmpName((String) data.get("emp_name"));
            listVo.setOrgName((String) data.get("org_name"));
            listVo.setPostName((String) data.get("post_name"));
            listVo.setCreateDate((Long) data.get("crttime"));
            Integer status = (Integer) data.get("status");
            listVo.setStatus(status);
            listVo.setStatusName(ApprovalStatusEnum.getName(status));
            listVo.setLastApprovalTime((Long) data.get("last_approval_time"));
            listVo.setBusinessKey((String) data.get("business_key"));
            listVo.setWaSobName((String) data.get("waSobName"));
            return listVo;
        }).collect(Collectors.toList());
        return new PageList<>(listVos, pageList.getPaginator());
    }

    private void doParseFilterField(PageBean pageBean, Map<String, Object> queryParam) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    queryParam.put("startDate", Long.valueOf(filterBean.getMin()));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    queryParam.put("endDate", Long.valueOf(filterBean.getMax()));
                }
                it.remove();
            } else if ("crttime".equals(filterBean.getField()) || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(Long.parseLong(filterBean.getMin()) * 1000 + "");
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(Long.parseLong(filterBean.getMax()) * 1000 + "");
                }
            }
        }
    }

    public void preHandleFilterField(PageBean pageBean) {
        if (CollectionUtils.isEmpty(pageBean.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = pageBean.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            if ("eventtime".equals(filterBean.getField())
                    || "crttime".equals(filterBean.getField())
                    || "last_approval_time".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    filterBean.setMin(String.valueOf(Long.parseLong(filterBean.getMin()) / 1000));
                }
                if (StringUtils.isNotBlank(filterBean.getMax())) {
                    filterBean.setMax(String.valueOf(Long.parseLong(filterBean.getMax()) / 1000));
                }
            }
        }
    }

    /**
     * 撤销
     *
     * @param dto
     */
    public void revoke(RevokeBatchAnalyseResultAdjustDto dto) {
        WaBatchAnalyseResultAdjustDo batchAnalyseResultAdjust = waBatchAnalyseResultAdjustDo.getById(dto.getBatchId());
        if (null == batchAnalyseResultAdjust) {
            // 撤销单据不存在
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201917", WebUtil.getRequest()));
        }
        Integer status = batchAnalyseResultAdjust.getStatus();
        if (ApprovalStatusEnum.REVOKED.getIndex().equals(status)) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.HAVE_REVOKE_NOT_ALLOW_REVOKE, Boolean.FALSE).getMsg());
        }
        if (!ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
            // 只允许撤销审批中的数据
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201918", WebUtil.getRequest()));
        }

        // 保存撤销原因
        batchAnalyseResultAdjust.setRevokeReason(dto.getRevokeReason());
        batchAnalyseResultAdjust.setUpdateBy(UserContext.getUserId());
        batchAnalyseResultAdjust.setUpdateTime(DateUtil.getCurrentTime(true));
        waBatchAnalyseResultAdjustDo.updateById(batchAnalyseResultAdjust);

        WfRevokeDto revokeDto = new WfRevokeDto();
        revokeDto.setBusinessKey(batchAnalyseResultAdjust.getBusinessKey());
        Result<String> result = wfRegisterFeign.revokeProcessOfTask(revokeDto);
        if (null == result || !result.isSuccess()) {
            // 工作流撤销异常
            throw new ServerException(MessageHandler.getMessage("caidao.exception.error_201919", WebUtil.getRequest()));
        }
    }

    /**
     * 详情（审批页面使用）
     *
     * @param businessId
     * @param summary
     * @return
     */
    public WfResponseDto getWfDetail(Long businessId, boolean summary) {
        WfResponseDto responseDto = new WfResponseDto();

        WaBatchAnalyseResultAdjustDo batchAnalyseResultAdjust = waBatchAnalyseResultAdjustDo.getById(businessId);
        if (null == batchAnalyseResultAdjust) {
            return responseDto;
        }

        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(batchAnalyseResultAdjust.getTenantId(), batchAnalyseResultAdjust.getEmpid());
        if (empInfo == null) {
            return responseDto;
        }

        // 查询考勤周期
        WaSobDo waSob = null;
        if (null != batchAnalyseResultAdjust.getWaSobId()) {
            waSob = waSobDo.getById(empInfo.getBelongOrgId(), batchAnalyseResultAdjust.getWaSobId());
        }
        EmpOrgPostInfo empOrgPostInfo = waEmpService.getEmpOrgPostInfo(String.valueOf(empInfo.getEmpid()));
        List<WfBusinessDataDetailDto> dataList = new ArrayList<>();
        dataList.add(new WfBusinessDataDetailDto("name", ResponseWrap.wrapResult(AttendanceCodes.EMP_NAME, null).getMsg(), empInfo.getEmpName(), null));
        dataList.add(new WfBusinessDataDetailDto("workno", ResponseWrap.wrapResult(AttendanceCodes.WORK_NO, null).getMsg(), empInfo.getWorkno(), null));
        dataList.add(new WfBusinessDataDetailDto("org", ResponseWrap.wrapResult(AttendanceCodes.ORG, null).getMsg(), empOrgPostInfo.getOrg(), null));
        dataList.add(new WfBusinessDataDetailDto("post", ResponseWrap.wrapResult(AttendanceCodes.POSITION, null).getMsg(), empOrgPostInfo.getPost(), null));
        dataList.add(new WfBusinessDataDetailDto("job", ResponseWrap.wrapResult(AttendanceCodes.POST, null).getMsg(), empOrgPostInfo.getJob(), null));
        dataList.add(new WfBusinessDataDetailDto("jobGrade", ResponseWrap.wrapResult(AttendanceCodes.POSITION_LEVEL, null).getMsg(), empOrgPostInfo.getJobGrade(), null));
        dataList.add(new WfBusinessDataDetailDto("waMonth", ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_MONTH, null).getMsg(), null != waSob ? waSob.getWaSobName() : null, null));
        if (!summary) {
            // 查询调整明细
            List<WaAnalyseResultAdjustDo> adjustDoList = waAnalyseResultAdjustDo.listByBatchId(businessId);
            if (CollectionUtils.isNotEmpty(adjustDoList)) {
                List<BatchAnalyseResultAdjustDetailDto> detailDtoList = adjustDoList.stream().map(o -> {
                    BatchAnalyseResultAdjustDetailDto detailDto = new BatchAnalyseResultAdjustDetailDto();
                    detailDto.setBelongDate(DateUtil.getDateStrByTimesamp(o.getBelongDate()));
                    detailDto.setOriginalLateTime(o.getOriginalLateTime());
                    detailDto.setOriginalEarlyTime(o.getOriginalEarlyTime());
                    detailDto.setOriginalKgWorkTime(o.getOriginalKgWorkTime());
                    if (null != o.getOriginalSigninTime()) {
                        detailDto.setOriginalSigninTime(DateUtil.getTimeStrByTimesamp(o.getOriginalSigninTime()));
                    }
                    if (null != o.getOriginalSignoffTime()) {
                        detailDto.setOriginalSignoffTime(DateUtil.getTimeStrByTimesamp(o.getOriginalSignoffTime()));
                    }
                    if (null != o.getSigninTime()) {
                        detailDto.setSigninTime(DateUtil.getTimeStrByTimesamp(o.getSigninTime()));
                    }
                    if (null != o.getSignoffTime()) {
                        detailDto.setSignoffTime(DateUtil.getTimeStrByTimesamp(o.getSignoffTime()));
                    }
                    detailDto.setReason(o.getReason());
                    return detailDto;
                }).collect(Collectors.toList());
                dataList.add(new WfBusinessDataDetailDto("batchAnalyseAdjustDetail", ResponseWrap.wrapResult(AttendanceCodes.ABNORMAL_APPLY_DETAIL, null).getMsg(),
                        detailDtoList, null));
            }

            if (StringUtils.isNotBlank(batchAnalyseResultAdjust.getFilePath())) {
                List<String> fileNameList = Arrays.stream(batchAnalyseResultAdjust.getFileName().split(",")).collect(Collectors.toList());
                List<String> filePathList = Arrays.stream(batchAnalyseResultAdjust.getFilePath().split(",")).collect(Collectors.toList());
                List<WfAttachmentDto> fileList = new ArrayList<>();
                for (int i = 0; i < filePathList.size(); i++) {
                    WfAttachmentDto attachmentDto = new WfAttachmentDto();
                    attachmentDto.setType(WfAttachmentTypeEnum.image.name());
                    attachmentDto.setFileUrl(filePathList.get(i));
                    attachmentDto.setFileName(fileNameList.get(i));
                    fileList.add(attachmentDto);
                }
                dataList.add(new WfBusinessDataDetailDto("file", ResponseWrap.wrapResult(AttendanceCodes.ATTACHMENT, null).getMsg(), null, fileList));
            }
        }
        dataList.add(new WfBusinessDataDetailDto("processCode", ResponseWrap.wrapResult(AttendanceCodes.APPROVAL_CODE, null).getMsg(), batchAnalyseResultAdjust.getProcessCode() == null ? "-" : batchAnalyseResultAdjust.getProcessCode(), null));
        responseDto.setDetailList(dataList);
        return responseDto;
    }

    /**
     * 详情（业务列表页面使用）
     *
     * @param businessId
     * @return
     */
    public WfDetailDto getWfDetailDto(Long businessId) {
        WfResponseDto wfResponseDto = getWfDetail(businessId, false);
        if (CollectionUtils.isEmpty(wfResponseDto.getDetailList())) {
            return new WfDetailDto();
        }
        List<String> fileNameList = Lists.newArrayList();
        List<String> filePathList = Lists.newArrayList();
        List<KeyValue> items = Lists.newArrayList();
        String orgFullPath = "";
        for (WfBusinessDataDetailDto o : wfResponseDto.getDetailList()) {
            if (o.getKey().equals("batchAnalyseAdjustDetail")) {
                items.add(new WaKeyValue(o.getText(), o.getValue(), o.getKey()));
            } else if (o.getKey().equals("file") && CollectionUtils.isNotEmpty(o.getFileList())) {
                List<WfAttachmentDto> fileList = o.getFileList();
                fileList.forEach(fileObj -> {
                    fileNameList.add(fileObj.getFileName());
                    filePathList.add(fileObj.getFileUrl());
                });
            } else {
                items.add(new KeyValue(o.getText(), o.getValue()));
                if (o.getKey().equals("org") && null != o.getValue()) {
                    orgFullPath = o.getValue().toString();
                }
            }
        }
        WfDetailDto detailDto = new WfDetailDto();
        if (CollectionUtils.isNotEmpty(fileNameList)) {
            detailDto.setFiles(StringUtils.join(filePathList, ","));
            detailDto.setFileNames(StringUtils.join(fileNameList, ","));
        }
        detailDto.setFullPath(orgFullPath);
        detailDto.setItems(items);
        return detailDto;
    }
}
