package com.caidaocloud.attendance.service.application.service;

import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.service.application.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftSaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.multi.MultiShiftSaveDto;
import com.caidaocloud.attendance.service.interfaces.vo.shift.MultiShiftVo;
import com.caidaocloud.dto.ItemsResult;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;

import java.util.List;
import java.util.Map;

/**
 * 班次设置服务
 */
public interface IShiftService {
    /**
     * 分页查询
     *
     * @param shiftPageDto
     * @param userInfo
     * @return
     */
    AttendancePageResult<WaShiftDo> getPageList(ShiftPageDto shiftPageDto, UserInfo userInfo);

    /**
     * 查询标准班次列表
     *
     * @param belongModule
     * @return
     */
    List<MultiShiftSimpleVo> getStdList(ShiftBelongModuleEnum belongModule);

    List<MultiShiftSimpleVo> convertDoToSimpleVo(List<WaShiftDo> shiftDoList);

    /**
     * 根据班次ID查询班次信息
     *
     * @param shiftDefIds
     * @return
     */
    List<MultiShiftSimpleVo> getListByIds(List<Integer> shiftDefIds);

    /**
     * 班次设置保存（一段班）
     *
     * @param dto
     */
    void saveShiftDef(ShiftSaveDto dto);

    /**
     * 班次设置保存（多段班）
     *
     * @param dto
     */
    Integer saveMultiShiftDef(MultiShiftSaveDto dto);

    /**
     * 查询详情
     *
     * @param id
     * @return
     */
    MultiShiftVo getById(Integer id);

    /**
     * 删除
     *
     * @param id
     */
    void deleteById(Integer id);

    /**
     * 班次下拉列表
     *
     * @param belongModule 所属模块：attendance 假勤、wfm 工时 默认假勤
     * @return
     */
    ItemsResult<KeyValue> selectShiftDefList(String belongModule);

    /**
     * 查询员工排班
     *
     * @param startDate
     * @param endDate
     * @param tenantId
     * @return
     */
    Map<Integer, List<EmpShiftInfo>> getEmpShiftInfoListMaps(Long startDate, Long endDate, String tenantId);

    /**
     * 班次编码唯一检查
     *
     * @param defCode
     * @param shiftDefId
     * @param belongModule belongModule 所属模块：attendance 假勤、wfm 工时 默认假勤
     * @return
     */
    boolean checkShiftDefReCode(String defCode, Integer shiftDefId, String belongModule);

    /**
     * 班次名称唯一检查
     *
     * @param defName
     * @param shiftDefId
     * @param belongModule belongModule 所属模块：attendance 假勤、wfm 工时 默认假勤
     * @return
     */
    boolean checkShiftDefReName(String defName, Integer shiftDefId, String belongModule);

    List<WaShiftDo> getListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                    Boolean temporaryShift, Integer startTime, Integer endTime, Integer startTimeBelong, Integer endTimeBelong);
}
