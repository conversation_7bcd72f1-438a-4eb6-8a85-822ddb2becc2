package com.caidaocloud.attendance.service.interfaces.facade;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.service.AsynExecListener;
import com.caidao1.commons.service.AsyncExecService;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JsonSerializeHelper;
import com.caidao1.ioc.util.GridUtil;
import com.caidao1.report.common.OpEnum;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.constant.AuthDataScopeCode;
import com.caidaocloud.attendance.service.application.dto.WaStatisticsDto;
import com.caidaocloud.attendance.service.application.enums.TimePeriodEnum;
import com.caidaocloud.attendance.service.application.enums.TimeTypeEnum;
import com.caidaocloud.attendance.service.application.event.publish.PaySyncWaPublish;
import com.caidaocloud.attendance.service.application.service.IRegisterRecordService;
import com.caidaocloud.attendance.service.application.service.IStatisticsService;
import com.caidaocloud.attendance.service.application.service.impl.EmpTravelService;
import com.caidaocloud.attendance.service.application.service.impl.LeaveApplyService;
import com.caidaocloud.attendance.service.application.service.impl.StatisticsReportService;
import com.caidaocloud.attendance.service.application.service.impl.WorkOvertimeService;
import com.caidaocloud.attendance.service.application.service.user.MyCenterService;
import com.caidaocloud.attendance.service.infrastructure.common.AttendanceEngineMessage;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.notify.AttendanceSummaryNotify;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.MyWorkEventDto;
import com.caidaocloud.attendance.service.interfaces.vo.*;
import com.caidaocloud.attendance.service.interfaces.vo.user.*;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.record.core.annotation.LogRecordAnnotation;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.annotation.Security;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.googlecode.totallylazy.Sequences;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/api/attendance/statistics/v1")
@Api(value = "/api/attendance/statistics/v1", description = "考勤统计接口")
public class StatisticsController {

    @Autowired
    private RedisTemplate redisTemplate;
    @Lazy
    @Autowired
    private IStatisticsService statisticsService;
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private AsyncExecService asyncService;
    @Autowired
    private IRegisterRecordService registerRecordService;
    @Resource
    private PaySyncWaPublish paySyncWaPublish;
    @Autowired
    private StatisticsReportService statisticsReportService;
    @Autowired
    private MyCenterService myCenterService;
    @Resource
    private LeaveApplyService leaveApplyService;
    @Resource
    private WorkOvertimeService workOvertimeService;
    @Resource
    private EmpTravelService empTravelService;

    private static final String ATTENDANCE_ANALYZE_PROCESS = "ATTENDANCE_ANALYZE_PROCESS_";

    private static final String ATTENDANCE_ANALYZE_PARAM = "ATTENDANCE_ANALYZE_PARAM_";

    private static final String ATTENDANCE_ANALYZE_MSG_PROCESS = "ATTENDANCE_ANALYZE_MSG_PROCESS_";

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @PostMapping("/summaryCount")
    @ApiOperation("获取统计考勤汇总数据")
    @Security(code = "summaryCount")
    public Result<StatisticsSummaryVo> getSummaryStaticCount(@RequestBody SummaryStaticPageDto dto,
            HttpServletRequest request) {
        try {
            ImmutablePair<Long, Long> timePeriod = TimeTypeEnum.parsePeriod(dto.getTimeType(),
                    dto.getStartDate(), dto.getEndDate());
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "sei");
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                        .replaceAll("empid", "sei.empid");
                dataScope = dataScope + orgDataScope;
            }
            log.info("getSummaryCount dataScope = {}", dataScope);
            StatisticsSummaryVo countVo = statisticsService.getSummaryCount(dto, dataScope);
            if (dto.getTimeType() == TimeTypeEnum.DAY) {
                SummaryDetailDto detailDto = ObjectConverter.convert(dto, SummaryDetailDto.class);
                detailDto.setPageNo(1);
                detailDto.setPageSize(10);
                PageList<?> leaveData = getLeavePageData(detailDto, request);
                PageList<?> otData = getOtPageData(detailDto, request);
                PageResult<?> trData = getTrPageData(detailDto, request);
                countVo.setLtCount(CollectionUtils.isEmpty(leaveData) || leaveData.getPaginator() == null ? 0
                        : leaveData.getPaginator().getTotalCount());
                countVo.setOtCount(CollectionUtils.isEmpty(otData) || otData.getPaginator() == null ? 0
                        : otData.getPaginator().getTotalCount());
                countVo.setTrCount(trData != null ? trData.getTotal() : 0);
            }
            return ResponseWrap.wrapResult(countVo);
        } catch (Exception ex) {
            log.error("获取考勤汇总数据失败:{}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, null);
        }
    }

    @PostMapping("/summaryWorkList")
    @ApiOperation("考勤汇总应到人数与打卡人数汇总")
    public Result<PageResult<StatisticsWorkInfoVo>> getSummaryWorkList(HttpServletRequest request,
            @RequestBody SummaryDetailDto dto) {
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                        .replaceAll("empid", "sei.empid");
                dataScope = dataScope + orgDataScope;
            }
            PageResult<StatisticsWorkInfoVo> pageResult = statisticsService.getSummaryWorkList(dto.configTimePeriod(),
                    dataScope);
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("获取考勤汇总应到打卡列表失败, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, null);
        }
    }

    @PostMapping("/summaryLeaveList")
    @ApiOperation("考勤汇总获取休假记录列表")
    public Result<AttendancePageResult<LeaveApplyListVo>> getSummaryLeaveList(@RequestBody SummaryDetailDto dto,
            HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            PageList<Map> leaveList = getLeavePageData(dto, request);
            if (CollectionUtils.isEmpty(leaveList)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            List<LeaveApplyListVo> listVos = JSON.parseArray(JSON.toJSONString(leaveList))
                    .toJavaList(LeaveApplyListVo.class);
            AttendancePageResult<LeaveApplyListVo> pageResult = new AttendancePageResult<>(listVos,
                    leaveList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("[summaryLeaveList] fetch list exception: {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, null);
        }
    }

    /**
     * 获取休假列表
     *
     * @param dto
     * @param request
     * @return
     */
    private PageList<Map> getLeavePageData(SummaryDetailDto dto, HttpServletRequest request) {
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "b");
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                        .replaceAll("empid", "b.empid");
                dataScope = dataScope + orgDataScope;
            }
            log.info("[summaryLeaveList] load scope data:{}", dataScope);
            LeaveApplyDto leaveApplyDto = ObjectConverter.convert(dto.configTimePeriod(), LeaveApplyDto.class);
            leaveApplyDto.setDataScope(dataScope);
            leaveApplyDto.setEndTime(24 * 60L - 1);
            leaveApplyDto.setFilterStatus(new Integer[] { 2 });
            return (PageList<Map>) leaveApplyService.getLeaveApplyList(leaveApplyDto, pageBean,
                    sessionService.getUserInfo(), true);
        } catch (Exception e) {
            log.error("[summaryLeaveList] fetch list exception: {}", e.getMessage(), e);
            return new PageList<>();
        }
    }

    @PostMapping("/summaryOtList")
    @ApiOperation("考勤汇总获取加班记录")
    public Result<?> getSummaryOtList(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        try {
            List<?> list = getOtPageData(dto, request);
            if (CollectionUtils.isEmpty(list)) {
                return ResponseWrap.wrapResult(new AttendancePageResult<>());
            }
            PageList pageList = (PageList) list;
            List<OvertimeApplyVo> overtimeApplyVos = JSON.parseArray(JSON.toJSONString(list), OvertimeApplyVo.class);
            overtimeApplyVos.forEach(row -> {
                row.setFuncType(BaseConst.WF_FUNC_TYPE_2);
                if (StringUtils.isBlank(row.getBusinessKey())) {
                    row.setBusinessKey(String.valueOf(row.getWaid()));
                }
            });
            AttendancePageResult<OvertimeApplyVo> pageResult = new AttendancePageResult<>(overtimeApplyVos,
                    pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
            return ResponseWrap.wrapResult(pageResult);
        } catch (Exception e) {
            log.error("[summaryOtList] fetch list exception, {}", e.getMessage(), e);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, null);
        }
    }

    private PageList<?> getOtPageData(SummaryDetailDto dto, HttpServletRequest request) {
        PageBean pageBean = PageUtil.getPageBean(dto);
        String belongId = getUserInfo().getTenantId();
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "b");
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "b.orgid")
                    .replaceAll("empid", "b.empid");
            dataScope = dataScope + orgDataScope;
        }
        log.info("[summaryOtList] load scope data:{}", dataScope);
        SummaryDetailDto detailDto = dto.configTimePeriod();
        detailDto.setFilterStatus(new Integer[] { 2 });
        return (PageList<?>) workOvertimeService.getOvertimeList(belongId, pageBean, null, detailDto.getStartDate(),
                null, detailDto.getEndDate(), 24 * 60L - 1, dataScope, detailDto.getFilterStatus());
    }

    @PostMapping("/summaryTrList")
    @ApiOperation("获取考勤汇总出差列表")
    public Result<PageResult<EmpTravelVo>> getSummaryTravelList(HttpServletRequest request,
            @RequestBody SummaryDetailDto dto) {
        PageResult<EmpTravelDto> pageResult = getTrPageData(dto, request);
        List<EmpTravelVo> voList = ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class);
        return ResponseWrap.wrapResult(
                new PageResult<>(voList, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
    }

    private PageResult getTrPageData(SummaryDetailDto dto, HttpServletRequest request) {
        UserInfo userInfo = this.getUserInfo();
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "b");
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "ei.orgid")
                    .replaceAll("empid", "ei.empid");
            dataScope = dataScope + orgDataScope;
        }
        log.info("[summaryTrList] load scope data: {}", dataScope);
        EmpTravelReqDto travelReqDto = ObjectConverter.convert(dto.configTimePeriod(), EmpTravelReqDto.class);
        travelReqDto.setBelongOrgId(userInfo.getTenantId());
        travelReqDto.setStartDateTime(dto.getStartDate());
        travelReqDto.setEndDateTime(dto.getEndDate() + (24 * 60L * 60L - 1));
        travelReqDto.setStatus(new Integer[] { 2 });
        travelReqDto.setDataScope(dataScope);
        return empTravelService.getEmpTravelPageList(travelReqDto, userInfo);
    }

    @PostMapping("/summaryAllList")
    @ApiOperation("考勤汇总出勤信息周、月列表")
    public Result<?> getSummaryAllList(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        List<String> headers = statisticsService.getSummaryDynamicHeaders(dto.getStatisticsType());
        PageBean pageBean = PageUtil.getPageBean(dto);
        MonthAnalysePageDto analysePageDto = ObjectConverter.convert(dto, MonthAnalysePageDto.class);
        // analysePageDto.setMergeWorknoToEmpName(true);
        analysePageDto.setStatisticsType(dto.getStatisticsType());
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
        analysePageDto.setDataScope(dataScope);
        FilterBean filterBean = new FilterBean();
        filterBean.setOp(OpEnum.in);
        filterBean.setMin("2");
        filterBean.setField("status");
        analysePageDto.setFilterList(Sequences.join(Sequences.sequence(filterBean),
                dto.getFilterList() == null ? new ArrayList<>() : dto.getFilterList()).toList());
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            analysePageDto.setDataScope(analysePageDto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤明细汇总表 dataScope = {}", analysePageDto.getDataScope());
        List list = statisticsService.searchRegisterStatistics(analysePageDto, pageBean, getUserInfo());
        return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, dto, headers));
    }

    @PostMapping("/summaryStatisticsList")
    @ApiOperation("考勤汇总出勤信息天列表")
    public Result<?> getSummaryDayList(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        List<String> head = statisticsService.getSummaryDayHeaders(dto.getStatisticsType());
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                        .replaceAll("empid", "e.empid");
                dataScope = dataScope + orgDataScope;
            }
            log.info("[summaryStatisticsList] load scope data:{}", dataScope);
            DayAnalysePageDto analysePageDto = ObjectConverter.convert(dto.configTimePeriod(), DayAnalysePageDto.class);
            analysePageDto.setMergeWorknoToEmpName(false);
            analysePageDto.setDataScope(dataScope);
            analysePageDto.setStartDate(dto.getStartDate().intValue());
            analysePageDto.setEndDate(dto.getEndDate().intValue());
            analysePageDto
                    .setStatusOptions(dto.getStatisticsType() == 0 ? new String[] { "lat" } : new String[] { "early" });
            List list = statisticsService.getDayAnalyseList(analysePageDto, pageBean);
            return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, analysePageDto, head));
        } catch (Exception ex) {
            log.error("[summaryStatisticsList] fetch list exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), new AttendancePageResult<>(head));
        }
    }

    @PostMapping("/summaryWorkRate")
    @ApiOperation("考勤汇总出勤率")
    public Result<List<SummaryRateVo>> getSummaryWorkRate(HttpServletRequest request,
            @RequestBody SummaryDetailDto dto) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            dataScope = dataScope + orgDataScope;
        }
        List<SummaryRateVo> mapList = statisticsService.getSummaryWorkRate(dto.configTimePeriod(), dataScope);
        return ResponseWrap.wrapResult(mapList);
    }

    @PostMapping("/summaryTimeRate")
    @ApiOperation("考勤汇总人均工作时长统计")
    public Result<?> getSummaryTimeRate(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        return ResponseWrap.wrapResult(getSummaryRateByType(request, dto.configTimePeriod(), 0));
    }

    @PostMapping("/summaryOtRate")
    @ApiOperation("考勤汇总人均加班时长统计")
    public Result<?> getSummaryOtRate(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        return ResponseWrap.wrapResult(getSummaryRateByType(request, dto.configTimePeriod(), 1));
    }

    private List<SummaryRateVo> getSummaryRateByType(HttpServletRequest request, SummaryDetailDto dto,
            Integer rateType) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            dataScope = dataScope + orgDataScope;
        }
        return statisticsService.getSummaryTimeRate(dto, rateType, dataScope);
    }

    @PostMapping("/summaryLtRate")
    @ApiOperation("考勤汇总人均休假人数统计")
    public Result<?> getSummaryLtRate(HttpServletRequest request, @RequestBody SummaryDetailDto dto) {
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_SUMMARY_COUNT, "e");
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "sei.orgid")
                    .replaceAll("empid", "sei.empid");
            dataScope = dataScope + orgDataScope;
        }
        List<SummaryRateVo> mapList = statisticsService.getSummaryLtRate(dto.configTimePeriod(), dataScope);
        return ResponseWrap.wrapResult(mapList);
    }

    @ApiOperation("获取考勤统计每日明细列表")
    @PostMapping("/getDayAnalyseList")
    @Security(code = "StatisticsDayAnalyseList")
    public Result<AttendancePageResult<Map>> getDayAnalyseList(@RequestBody DayAnalysePageDto dto,
            HttpServletRequest request) {
        List<String> head = statisticsService.getDayHeaders();
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            // dto.setMergeWorknoToEmpName(true);
            // 数据权限
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_DAY_ANALYSE_LIST, "e");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                        .replaceAll("empid", "e.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("获取考勤统计每日明细列表 dataScope = {}", dataScope);
            List list = statisticsService.getDayAnalyseList(dto, pageBean);
            return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, dto, head));
        } catch (Exception ex) {
            log.error("StatisticsController.getDayAnalyseList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(AttendanceCodes.GET_LIST_FAILED, new AttendancePageResult<>(head));
        }
    }

    @ApiOperation("获取考勤统计每日明细列表-动态列")
    @PostMapping("/getDayAnalyseList/dynamic")
    public Result getDayAnalyseListForDynamic(@RequestBody DayAnalysePageDto dto, HttpServletRequest request) {
        // List<String> head = statisticsService.getDayHeaders();
        PageBean pageBean = PageUtil.getPageBean(dto);
        // dto.setMergeWorknoToEmpName(true);
        // 数据权限
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_DAY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid").replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤统计每日明细列表 dataScope = {}", dto.getDataScope());
        DynamicPageDto pageDto = statisticsService.getDayAnalyseListForDynamic(dto, pageBean, getUserInfo());
        if (pageDto.getPageData() == null || pageDto.getPageData().getItems() == null) {
            return Result.ok(new PageResult<>());
        }
        return Result.ok(pageDto.getPageData());

    }

    @ApiOperation("考勤分析")
    @PostMapping("/analyze")
    @Security(code = "StatisticsAnalyze")
    @LogRecordAnnotation(success = "{{#content}}", category = "考勤核算", menu = "考勤统计-考勤统计-每日统计")
    public Result<Boolean> analyze(@RequestBody DayAnalyseDto dto) {
        UserInfo userInfo = getUserInfo();
        if (dto.getEmpIds() != null) {
            LogRecordContext.putVariable("content", "按人员核算");
        } else {
            LogRecordContext.putVariable("content", "按周期核算");
        }
        try {
            redisTemplate.opsForValue().set(ATTENDANCE_ANALYZE_PROCESS + dto.getProgress(), 0.5d);

            // 数据权限
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_ANALYZE, "emp");
            log.info("StatisticsService.asyncRegister dataScope = {}", dataScope);
            Locale locale = ResponseWrap.getLocale();
            String finalDataScope = dataScope;
            asyncService.execCallback(new AsynExecListener() {
                @Override
                public Map execute() throws Exception {
                    AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
                    Map params = new HashMap();
                    ResponseWrap.setThreadLocale(locale);
                    try {
                        WaStatisticsDto waStatisticsDto = statisticsService.asyncRegister(userInfo.getTenantId(),
                                dto.getStartDate(),
                                dto.getEndDate(), dto.getEmpIds(), dto.getSobId(), finalDataScope, userInfo.getUserId(),
                                false);
                        engineMessage.setCount(waStatisticsDto.getRow());
                        log.info("StatisticsService.empList={}", FastjsonUtil.toJson(waStatisticsDto.getEmpList()));

                        params.put("empList", waStatisticsDto.getEmpList());
                        params.put("belongOrgId", userInfo.getTenantId());
                        params.put("startDate", dto.getStartDate());
                        params.put("endDate", dto.getEndDate());

                        if (null != waStatisticsDto.getEmpList() && !waStatisticsDto.getEmpList().isEmpty()) {
                            // 薪资考勤数据同步
                            // paySyncWaPublish.syncWaAnalyze(waStatisticsDto.getEmpList(),
                            // userInfo.getBelongOrgId(), waStatisticsDto.getStartDate(),
                            // waStatisticsDto.getEndDate());
                            // 考勤统计 周报、月报数据计算同步
                            statisticsReportService.syncWaAnalyzeStatisticsReport(waStatisticsDto.getEmpList(),
                                    dto.getSobId(), userInfo.getTenantId(), userInfo.getUserId());
                        }

                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                        engineMessage.setCode(ErrorCodes.UNKNOW_ERROR);
                        if (e instanceof CDException) {
                            engineMessage.setMessage(e.getMessage());
                        } else {
                            engineMessage
                                    .setMessage(ResponseWrap.wrapResult(AttendanceCodes.ANALYZE_FAILED, null).getMsg());
                        }
                    } finally {
                        ResponseWrap.clearThreadLocale();
                    }
                    params.put("result", engineMessage);
                    return params;
                }

                @Override
                public void callback(Map params) throws Exception {
                    log.info("StatisticsAnalyze callback progress={}", dto.getProgress());
                    redisTemplate.opsForValue().set(ATTENDANCE_ANALYZE_PROCESS + dto.getProgress(), 1.0d);
                    redisTemplate.opsForValue().set(ATTENDANCE_ANALYZE_PARAM + dto.getProgress(),
                            FastjsonUtil.toJson(params), 5, TimeUnit.MINUTES);
                    AttendanceEngineMessage engineMessage = (AttendanceEngineMessage) params.get("result");
                    if (engineMessage != null) {
                        redisTemplate.opsForValue().set(ATTENDANCE_ANALYZE_MSG_PROCESS + dto.getProgress(),
                                JsonSerializeHelper.serialize(engineMessage), 5, TimeUnit.MINUTES);
                    }
                }
            });
            return Result.ok(true);
        } catch (Exception ex) {
            log.error("StatisticsController.analyze executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), CommonConstant.FALSE);
        }
    }

    @ApiOperation("获取考勤计算进度")
    @RequestMapping(value = "/getProgress", method = RequestMethod.GET)
    public Result getProgress(@RequestParam("progress") String progress) {
        UserInfo userInfo = getUserInfo();
        AttendanceEngineMessage engineMessage = new AttendanceEngineMessage();
        if (!redisTemplate.hasKey(ATTENDANCE_ANALYZE_PROCESS + progress)) {
            return ResponseWrap.wrapResult(AttendanceCodes.PROCESS_NOT_EXIST, null);
        }
        // 进度
        Double rate = (Double) redisTemplate.opsForValue().get(ATTENDANCE_ANALYZE_PROCESS + progress);
        if (rate == null) {
            rate = 0.5d;
        }
        if (rate >= 1) {
            // 执行结果
            String analyzeMsg = (String) redisTemplate.opsForValue().get(ATTENDANCE_ANALYZE_MSG_PROCESS + progress);
            if (StringUtils.isNotBlank(analyzeMsg)) {
                AttendanceEngineMessage engineMessageForCache = JsonSerializeHelper.deserialize(analyzeMsg,
                        AttendanceEngineMessage.class);
                if (engineMessageForCache != null) {
                    engineMessage = engineMessageForCache;
                }
            }
            // 删除缓存
            redisTemplate.delete(ATTENDANCE_ANALYZE_PROCESS + progress);
            redisTemplate.delete(ATTENDANCE_ANALYZE_MSG_PROCESS + progress);
            String param = (String) redisTemplate.opsForValue().get(ATTENDANCE_ANALYZE_PARAM + progress);
            Map map = FastjsonUtil.toObject(param, Map.class);
            if (map != null && !map.isEmpty()) {
                log.info("start synWaAnalyze....progress={}", progress);
                List<Long> empList = (List<Long>) map.get("empList");
                String belongOrgId = map.get("belongOrgId") == null ? null : map.get("belongOrgId").toString();
                String startDate = map.get("startDate") == null ? null : map.get("startDate").toString();
                String endDate = map.get("endDate") == null ? null : map.get("endDate").toString();
                if (belongOrgId != null && startDate != null && endDate != null) {
                    paySyncWaPublish.syncWaAnalyze(empList, belongOrgId, Long.parseLong(startDate),
                            Long.parseLong(endDate));
                }
            } else {
                log.info("start synWaAnalyze fail param data empty progress={}", progress);
            }
            redisTemplate.delete(ATTENDANCE_ANALYZE_PARAM + progress);
        }
        engineMessage.setProcess(rate);
        if (engineMessage.getCode() != 0) {
            return Result.fail(engineMessage.getMessage());
        }
        return Result.ok(engineMessage);
    }

    /******************** 月度 ***********************/
    @ApiOperation("获取考勤明细汇总表")
    @PostMapping(value = "/searchRegisterStatisticsAdvance")
    @Security(code = "searchRegisterStatisticsAdvance")
    public Result<AttendancePageResult<Map>> searchRegisterStatisticsAdvance(@RequestBody MonthAnalysePageDto dto,
            HttpServletRequest request) {
        TimePeriodEnum periodEnum = dto.getTimePeriod();
        ImmutablePair<Long, Long> timePeriod = TimePeriodEnum.parsePeriod(periodEnum);
        if (timePeriod == null && (dto.getStartDate() == null || dto.getEndDate() == null)) {
            throw new ServerException("查询时间错误");
        } else if (timePeriod != null) {
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
        }
        List<String> headers = statisticsService.getMonthHeaders(dto.getGroupId(),
                new ImmutablePair<>(dto.getStartDate(), dto.getEndDate()));
        PageBean pageBean = PageUtil.getPageBean(dto);
        // dto.setMergeWorknoToEmpName(true);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST,
                "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤明细汇总表 dataScope = {}", dto.getDataScope());
        List list = statisticsService.searchRegisterStatisticsAdvance(dto, pageBean, getUserInfo());
        return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, dto, headers));
    }

    @ApiOperation("获取考勤明细汇总表——动态列")
    @PostMapping(value = "/searchRegisterStatisticsAdvance/dynamic")
    public Result<AttendancePageResult<Map>> searchRegisterStatisticsAdvanceByDynamic(
            @RequestBody MonthAnalysePageDto dto, HttpServletRequest request) {
        TimePeriodEnum periodEnum = dto.getTimePeriod();
        ImmutablePair<Long, Long> timePeriod = TimePeriodEnum.parsePeriod(periodEnum);
        if (timePeriod == null && (dto.getStartDate() == null || dto.getEndDate() == null)) {
            throw new ServerException("查询时间错误");
        } else if (timePeriod != null) {
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
        }
        List<String> headers = statisticsService.getMonthHeaders(dto.getGroupId(),
                new ImmutablePair<>(dto.getStartDate(), dto.getEndDate()));
        PageBean pageBean = PageUtil.getPageBean(dto);
        // dto.setMergeWorknoToEmpName(true);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST,
                "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤明细汇总表 dataScope = {}", dto.getDataScope());
        DynamicPageDto pageDto = statisticsService.getRegisterStatisticsAdvancedForDynamic(dto, pageBean,
                getUserInfo());
        if (pageDto.getPageData() == null || pageDto.getPageData().getItems() == null) {
            return ResponseWrap.wrapResult(this.getPageResult(new ArrayList<>(), pageBean, dto, headers));
        }
        return ResponseWrap.wrapResult(this.getPageResult(pageDto.getPageData().getItems(), pageBean, dto, headers));
    }

    @ApiOperation("获取考勤统计月度汇总分页列表")
    @PostMapping(value = "/searchRegisterStatistics")
    @Security(code = "StatisticsMonthlyAnalyseList")
    public Result<AttendancePageResult<Map>> searchRegisterStatistics(@RequestBody MonthAnalysePageDto dto,
            HttpServletRequest request) {
        List<String> headers = statisticsService.getMonthHeaders(dto.getGroupId(), null);
        try {
            PageBean pageBean = PageUtil.getPageBean(dto);
            // dto.setMergeWorknoToEmpName(true);
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_LIST, "e");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                        .replaceAll("empid", "e.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("获取考勤统计月度汇总分页列表 dataScope = {}", dataScope);
            List list = statisticsService.searchMonthRegisterStatistics(dto, pageBean, getUserInfo());
            return ResponseWrap.wrapResult(this.getPageResult(list, pageBean, dto, headers));
        } catch (Exception ex) {
            log.error("StatisticsController.searchRegisterStatistics executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(),
                    new AttendancePageResult<>(headers));
        }
    }

    @ApiOperation("获取考勤统计月度汇总分页列表-动态列")
    @PostMapping(value = "/searchRegisterStatistics/dynamic")
    @Security(code = "StatisticsMonthlyAnalyseListDynamic")
    public Result searchRegisterStatisticsDynamic(@RequestBody MonthAnalysePageDto dto, HttpServletRequest request) {
        // List<String> headers = statisticsService.getMonthHeaders(dto.getGroupId(),
        // null);
        PageBean pageBean = PageUtil.getPageBean(dto);
        // dto.setMergeWorknoToEmpName(true);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_LIST, "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid").replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤统计月度汇总分页列表 dataScope = {}", dto.getDataScope());
        DynamicPageDto pageDto = statisticsService.getMouthAnalyseListForDynamic(dto, pageBean);
        if (pageDto.getPageData() == null || pageDto.getPageData().getItems() == null) {
            return Result.ok(new PageResult<>());
        }
        return Result.ok(pageDto.getPageData());
    }

    private AttendancePageResult<Map> getPageResult(List list, PageBean pageBean, AttendanceBasePage basePage,
            List<String> headers) {
        Map map = GridUtil.covertList2GridJson(list, pageBean);
        if (CollectionUtils.isEmpty(list)) {
            list = Lists.newArrayList();
        }
        int totalCount = (Integer) map.get("total_count");
        if (!(list instanceof PageList) && list.size() == totalCount) {
            totalCount = pageBean.getCount();
        }
        AttendancePageResult<Map> pageResult = new AttendancePageResult<>(list, basePage.getPageNo(),
                basePage.getPageSize(), totalCount);
        pageResult.setHead(headers);
        return pageResult;
    }

    @ApiOperation("考勤结果提醒")
    @PostMapping(value = "/notify")
    @LogRecordAnnotation(success = "{{#content}}", category = "发送考勤结果", menu = "考勤统计-考勤统计-月度汇总")
    public Result<Boolean> notification(@RequestBody AttendanceSummaryNotify dto) {
        try {
            statisticsService.notification(dto, sessionService.getUserInfo(), ResponseWrap.getLocale());

            return ResponseWrap.wrapResult(Boolean.TRUE);
        } catch (Exception e) {
            log.error("消息推送失败:{}", e.getMessage(), e);
            return ResponseWrap.wrapResult(AttendanceCodes.SENDING_RESULT_FAILED, Boolean.FALSE);
        }
    }

    @ApiOperation("每日考勤详情列表")
    @GetMapping(value = "/getDayAnalyseDetailList")
    public Result<WaAnalyzeDetailVo> getDayAnalyzeDetail(@RequestParam("id") Integer analyzeId) {
        RegisterRecordRequestDto requestDto = new RegisterRecordRequestDto();
        // 每日考勤
        WaAnalyzeDto waAnalyzeDto = registerRecordService.getAnalyzeDetailById(analyzeId);
        if (null == waAnalyzeDto) {
            return ResponseWrap.wrapResult(AttendanceCodes.ANALYZE_NOT_EXIST, null);
        }
        WaAnalyzeDetailVo waAnalyzeDetailVo = ObjectConverter.convert(waAnalyzeDto, WaAnalyzeDetailVo.class);
        // 打卡记录
        Long startDate = waAnalyzeDto.getBelongDate();
        Long endDate = startDate;
        if (endDate != null) {
            int m = (23 * 60 * 60) + (59 * 60) + 59;
            endDate += m;
        }
        requestDto.setAll(Boolean.TRUE);
        requestDto.setIfShowAll(Boolean.TRUE);
        requestDto.setStartDate(startDate.intValue());
        requestDto.setEndDate(endDate.intValue());
        requestDto.setKeywords(waAnalyzeDto.getWorkNo());
        requestDto.setAnalyze(Boolean.TRUE);
        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService
                .getRegisterRecordPageList(requestDto);
        List<RegisterRecordAnalyzeVo> voList = ObjectConverter.convertList(pageResult.getItems(),
                RegisterRecordAnalyzeVo.class);
        waAnalyzeDetailVo.setRecordList(voList);
        return ResponseWrap.wrapResult(waAnalyzeDetailVo);
    }

    @ApiOperation("考勤日历")
    @GetMapping(value = "/getAnalyzeCalendar")
    public Result<List<AanlyzeCalendarVo>> getAnalyzeCalendar(
            @RequestParam(value = "empId", required = false) Long empId,
            @RequestParam(value = "searchMonth", required = false) Integer searchMonth, HttpServletRequest request) {
        String dataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(dataScope)) {
            dataScope = dataScope.replaceAll("orgid", "ei.orgid").replaceAll("empid", "ei.empid");
        }
        log.info("获取考勤日历 dataScope = {}", dataScope);
        List<AnalyzeCalendarDto> list = statisticsService.getAnalyzeCalendarList(empId, searchMonth, dataScope);
        List<AanlyzeCalendarVo> calendarVos = ObjectConverter.convertList(list, AanlyzeCalendarVo.class);
        return Result.ok(calendarVos);
    }

    @ApiOperation("门户考勤日历")
    @GetMapping(value = "/getAnalyzeCalendarPortal")
    public Result<List<AanlyzeCalendarVo>> getAnalyzeCalendarPortal() {
        List<AnalyzeCalendarDto> list = statisticsService.getAnalyzeCalendarList(null, null, null);
        List<AanlyzeCalendarVo> calendarVos = ObjectConverter.convertList(list, AanlyzeCalendarVo.class);
        return Result.ok(calendarVos);
    }

    @ApiOperation("每日考勤详情列表")
    @GetMapping(value = "/getEmpAttendance")
    public Result<MyWorkEventVo> getEmpAttendance(@RequestParam("empId") Long empId,
            @RequestParam("day") Integer day) {
        MyWorkEventVo vo = new MyWorkEventVo();
        Long daytime = DateUtil.convertStringToDateTime(day.toString(), "yyyyMMdd", true);
        WaParseGroup parseGroup = myCenterService.calcClockType(vo, daytime, empId, null);
        MyWorkEventDto myWorkEventDto = myCenterService.getMyAttendance(day, vo.getClockType(), parseGroup, empId,
                null);
        vo.setRecords(ObjectConverter.convertList(myWorkEventDto.getRecords(), MyClockInfoVo.class));
        vo.setLts(ObjectConverter.convertList(myWorkEventDto.getLts(), MyLeaveTimeVo.class));
        vo.setOts(ObjectConverter.convertList(myWorkEventDto.getOts(), MyOverTimeVo.class));
        vo.setShifts(ObjectConverter.convertList(myWorkEventDto.getShifts(), MyWorkDateShiftVo.class));
        vo.setBdkRecords(ObjectConverter.convertList(myWorkEventDto.getBdkRecords(), MyBdkClockInfoVo.class));
        vo.setTts(ObjectConverter.convertList(myWorkEventDto.getTts(), MyTravelInfoVo.class));
        vo.setLateTime(myWorkEventDto.getLateTime());
        vo.setEarlyTime(myWorkEventDto.getEarlyTime());
        vo.setKgWorkTime(myWorkEventDto.getKgWorkTime());
        vo.setEmpName(myWorkEventDto.getEmpName());
        return Result.ok(vo);
    }

    @ApiOperation("异常汇总列表")
    @PostMapping("/getDayAnalyseAbnormalList")
    @Security(code = "StatisticsDayAnalyseAbnormalList")
    public Result<PageResult<DayAnalyseAbnormalVo>> getDayAnalyseAbnormalList(
            @RequestBody DayAnalyseAbnormalPageDto dto, HttpServletRequest request) {
        try {
            String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.EMP_DAY_ANALYSE_ABNORMAL_LIST, "e");
            dto.setDataScope(dataScope);
            String orgDataScope = (String) request.getSession().getAttribute("dataScope");
            if (StringUtil.isNotBlank(orgDataScope)) {
                orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid").replaceAll("empid", "e.empid");
                dto.setDataScope(dto.getDataScope() + orgDataScope);
            }
            log.info("导出异常汇总记录 DataScope = {}", dataScope);
            PageResult<DayAnalyseAbnormalDto> pageResult = statisticsService.getDayAnalyseAbnormalList(dto,
                    getUserInfo());
            List<DayAnalyseAbnormalVo> list = ObjectConverter.convertList(pageResult.getItems(),
                    DayAnalyseAbnormalVo.class);
            return ResponseWrap.wrapResult(
                    new PageResult<>(list, pageResult.getPageNo(), pageResult.getPageSize(), pageResult.getTotal()));
        } catch (Exception ex) {
            log.error("StatisticsController.getDayAnalyseAbnormalList has exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), null);
        }
    }

    @ApiOperation("获取考勤统计月度汇总字段")
    @GetMapping(value = "/searchMonthHeaderList")
    @Security(code = "SearchMonthHeaderList")
    public Result<List<KeyValue>> searchMonthHeaderList(
            @RequestParam(name = "waGroupId", required = false) Integer waGroupId) {
        List<KeyValue> result = new ArrayList<>();
        try {
            result = statisticsService.getMonthHeadersKeyValue(waGroupId);
            return ResponseWrap.wrapResult(result);
        } catch (Exception ex) {
            log.error("StatisticsController.searchMonthHeaders executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), result);
        }
    }

    @ApiOperation("获取考勤统计月度汇总字段-动态列")
    @GetMapping(value = "/searchMonthHeaderList/dynamic")
    @Security(code = "SearchMonthHeaderList")
    public Result<List<KeyValue>> searchMonthHeaderListForDynamic(
            @RequestParam(name = "waGroupId", required = false) Integer waGroupId) {
        List<KeyValue> result = new ArrayList<>();
        try {
            result = statisticsService.searchMonthHeaderListForDynamic(waGroupId, getUserInfo());
            return ResponseWrap.wrapResult(result);
        } catch (Exception ex) {
            log.error("StatisticsController.searchMonthHeaders executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), result);
        }
    }

    @ApiOperation("获取考勤明细汇总表——动态列（支持多个考勤分组ID）")
    @PostMapping(value = "/searchRegisterStatisticsAdvance/dynamicMultiGroup")
    public Result<DynamicPageDto> searchRegisterStatisticsAdvanceByDynamicMultiGroup(
            @RequestBody MonthAnalysePageDtoV2 dto, HttpServletRequest request) {
        TimePeriodEnum periodEnum = dto.getTimePeriod();
        ImmutablePair<Long, Long> timePeriod = TimePeriodEnum.parsePeriod(periodEnum);
        if (timePeriod == null && (dto.getStartDate() == null || dto.getEndDate() == null)) {
            throw new ServerException("查询时间错误");
        } else if (timePeriod != null) {
            dto.setStartDate(timePeriod.getLeft());
            dto.setEndDate(timePeriod.getRight());
        }
        PageBean pageBean = PageUtil.getPageBean(dto);
        String dataScope = sessionService.getAndDataScope(AuthDataScopeCode.STATISTICS_MONTHLY_ANALYSE_ADVANCE_LIST,
                "e");
        dto.setDataScope(dataScope);
        String orgDataScope = (String) request.getSession().getAttribute("dataScope");
        if (StringUtil.isNotBlank(orgDataScope)) {
            orgDataScope = orgDataScope.replaceAll("orgid", "e.orgid")
                    .replaceAll("empid", "e.empid");
            dto.setDataScope(dto.getDataScope() + orgDataScope);
        }
        log.info("获取考勤明细汇总表（支持多个考勤分组ID） dataScope = {}", dto.getDataScope());
        DynamicPageDto pageDto = statisticsService.getRegisterStatisticsAdvancedForDynamicMultiGroup(dto, pageBean,
                getUserInfo());
        return Result.ok(pageDto);
    }

    @ApiOperation("获取考勤统计月度汇总字段-动态列（支持多个考勤分组ID）")
    @GetMapping(value = "/searchMonthHeaderList/dynamicMultiGroup")
    @Security(code = "SearchMonthHeaderList")
    public Result<List<KeyValue>> searchMonthHeaderListForDynamicMultiGroup(
            @RequestParam(name = "waGroupIds", required = false) List<Integer> waGroupIds) {
        List<KeyValue> result = new ArrayList<>();
        try {
            result = statisticsService.searchMonthHeaderListForDynamicMultiGroup(waGroupIds, getUserInfo());
            return ResponseWrap.wrapResult(result);
        } catch (Exception ex) {
            log.error("StatisticsController.searchMonthHeaderListForDynamicMultiGroup executes exception, {}",
                    ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), result);
        }
    }

    @ApiOperation("获取考勤统计每日明细字段_动态列")
    @GetMapping("/getDayAnalyseHeaderList/dynamic")
    @Security(code = "GetDayAnalyseHeaderList")
    public Result<List<KeyValue>> getDayAnalyseHeaderListForDynamic() {
        List<KeyValue> result = new ArrayList<>();
        try {
            result = statisticsService.getDayAnalyseHeaderListForDynamic(getUserInfo());
            return ResponseWrap.wrapResult(result);
        } catch (Exception ex) {
            log.error("StatisticsController.getDayAnalyseHeaderList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), result);
        }
    }

    @ApiOperation("获取考勤统计每日明细字段")
    @GetMapping("/getDayAnalyseHeaderList")
    @Security(code = "GetDayAnalyseHeaderList")
    public Result<List<KeyValue>> getDayAnalyseHeaderList() {
        List<KeyValue> result = new ArrayList<>();
        try {
            result = statisticsService.getDayAnalyseHeaderKeyValue();
            return ResponseWrap.wrapResult(result);
        } catch (Exception ex) {
            log.error("StatisticsController.getDayAnalyseHeaderList executes exception, {}", ex.getMessage(), ex);
            return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, ex.getMessage(), result);
        }
    }

    @ApiOperation("获取考勤统计每日明细列表-动态列")
    @GetMapping("/fetchStatisticsAvailableColumn/day")
    public Result<List<MetadataVo>> fetchStatisticsAvailableColumnByDay() {
        return Result.ok(statisticsService.fetchStatisticsAvailableColumn("day"));
    }

    @ApiOperation("获取考勤统月度统计明细列表-动态列")
    @GetMapping("/fetchStatisticsAvailableColumn/month")
    public Result<List<MetadataVo>> fetchStatisticsAvailableColumnByMonth() {
        return Result.ok(statisticsService.fetchStatisticsAvailableColumn("month"));
    }

    @ApiOperation("获取考勤统考勤明细列表-动态列")
    @GetMapping("/fetchStatisticsAvailableColumn/advance")
    public Result<List<MetadataVo>> fetchStatisticsAvailableColumnByAdvance() {
        return Result.ok(statisticsService.fetchStatisticsAvailableColumn("advance"));
    }

    @ApiOperation("重置考勤统计每日明细列表-动态列")
    @GetMapping("/reset/day")
    public Result resetAvailableColumnByDay() {
        return Result.ok(statisticsService.resetAvailableColumn("1"));
    }

    @ApiOperation("重置考勤统月度统计明细列表-动态列")
    @GetMapping("/reset/month")
    public Result resetAvailableColumnByMonth() {
        return Result.ok(statisticsService.resetAvailableColumn("2"));
    }

    @ApiOperation("重置考勤统月度统计明细列表-动态列")
    @GetMapping("/reset/advance")
    public Result resetAvailableColumnByAdvance() {
        return Result.ok(statisticsService.resetAvailableColumn("3"));
    }
}
