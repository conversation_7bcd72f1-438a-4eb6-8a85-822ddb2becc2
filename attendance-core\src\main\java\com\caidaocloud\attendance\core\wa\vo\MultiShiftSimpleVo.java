package com.caidaocloud.attendance.core.wa.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel("多段班简单信息VO")
public class MultiShiftSimpleVo {
    // 基本信息
    @ApiModelProperty("班次Id")
    private Long shiftDefId;
    @ApiModelProperty("日期类型：1、工作日、2休息日、3法定假日、4特殊休日、5 法定休日")
    private Integer dateType;
    @ApiModelProperty("班次名称")
    private String shiftDefName;
    @ApiModelProperty("班次编码")
    private String shiftDefCode;
    @ApiModelProperty("总工作时长(单位分钟)")
    private Integer workTotalTime;
    @ApiModelProperty("是否为临时班次")
    private boolean temporary;
    @ApiModelProperty("摘要")
    private String summary;
    @ApiModelProperty("上班时间(单位分钟),eg:780")
    private Integer startTime;
    @ApiModelProperty("下班时间(单位分钟),eg:1080")
    private Integer endTime;
    @ApiModelProperty("上班时间归属标记: 1 当日、2 次日")
    private Integer startTimeBelong;
    @ApiModelProperty("下班时间归属标记: 1 当日、2 次日")
    private Integer endTimeBelong;
    @ApiModelProperty("颜色标记")
    private String colorMark;
    @ApiModelProperty("班次工作时间跨夜标记: true 跨夜、false 不跨夜")
    private Boolean isNight = false;

    // 多段班时间信息
    @ApiModelProperty("多段班时间设置信息（仅工作时间）")
    private List<MultiWorkTimeInfoSimpleVo> multiWorkTimes;
    @ApiModelProperty("多段班时间设置信息（工作时间+休息时间+加班时间）")
    private List<MultiWorkTimeInfoSimpleVo> oriMultiWorkTimes;
}
