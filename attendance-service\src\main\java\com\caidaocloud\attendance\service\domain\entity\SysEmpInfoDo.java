package com.caidaocloud.attendance.service.domain.entity;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.service.domain.repository.IConditionRepository;
import com.caidaocloud.attendance.service.domain.repository.ISysEmpInfoRepository;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.hrpaas.metadata.sdk.dto.EmpSimple;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StrExpChangeUtil;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/6/24 18:53
 * @Description:
 **/
@Slf4j
@Service
@Data
public class SysEmpInfoDo {

    private Long empid;
    private String workno;
    private String empName;

    @Autowired
    private ISysEmpInfoRepository sysEmpInfoRepository;
    @Autowired
    private IConditionRepository conditionRepository;

    public List<Long> getEmpIdsByGroupExpCondition(String tenantId, ConditionTree condition) {
        if (null == condition) {
            return Lists.newArrayList();
        }
        List<EmpSimple> empSimpleList = conditionRepository.fetchMatchedEmp(tenantId, condition);
        return empSimpleList.stream().map(EmpSimple::getEmpId).map(Long::valueOf).distinct().collect(Collectors.toList());
    }

    public List<Long> getEmpIdsByGroupExp(Long corpId, String groupExp, String groupExpCondition) {
        return getEmpIdsByGroupExp(null, null, null, null, corpId, groupExp, groupExpCondition);
    }

    public List<Long> getEmpIdsByGroupExp(String tenantId, Long empId, Integer leaveId, Integer leaveTypeId, Long corpId, String groupExp, String groupExpCondition) {
        String sqlExp = StrExpChangeUtil.strExpChangeV2(groupExp);
        sqlExp = String.format("(%s) and stats<>1 ", sqlExp);
        Long currentTime = DateUtil.getCurrentTime(true);
        // 司龄（至年底）计算使用参数
        Long yearEndTime = null;
        try {
            yearEndTime = DateUtilExt.getYearsEndTime(currentTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            yearEndTime = currentTime;
        }
        List<Long> empIdList = sysEmpInfoRepository.getGroupEmpIds(tenantId, empId, leaveId, leaveTypeId, corpId, sqlExp, currentTime, yearEndTime);
        log.info("getEmpIdsByGroupExp.getGroupEmpIds.size: {}", empIdList.size());
        log.debug("getEmpIdsByGroupExp.getGroupEmpIds: {}", FastjsonUtil.toJsonStr(empIdList));
        ConditionTree condition = FastjsonUtil.toObject(groupExpCondition, ConditionTree.class);
        if (null != condition) {
            List<Long> matchedEmpIdList = getEmpIdsByGroupExpCondition(String.valueOf(corpId), condition);
            log.info("getEmpIdsByGroupExp.getEmpIdsByGroupExpCondition.size: {}", empIdList.size());
            log.debug("getEmpIdsByGroupExp.getEmpIdsByGroupExpCondition: {}", FastjsonUtil.toJsonStr(empIdList));
            empIdList.retainAll(matchedEmpIdList);
        }
        return empIdList;
    }

    public List<Integer> getLeaveIdsByGroupExp(String tenantId, Long empId, Integer leaveId, Integer leaveTypeId, Long corpId, String groupExp) {
        String sqlExp = StrExpChangeUtil.strExpChangeV2(groupExp);
        sqlExp = String.format("(%s) and stats<>1 ", sqlExp);
        Long currentTime = DateUtil.getCurrentTime(true);
        // 司龄（至年底）计算使用参数
        Long yearEndTime = null;
        try {
            yearEndTime = DateUtilExt.getYearsEndTime(currentTime);
        } catch (ParseException e) {
            log.error(e.getMessage(), e);
            yearEndTime = currentTime;
        }
        return sysEmpInfoRepository.getLeaveIdsByGroupExp(tenantId, empId, leaveId, leaveTypeId, corpId, sqlExp, currentTime, yearEndTime);
    }

    public List<Map> getEmpInfoListByGroupExp(String belongOrgId, String groupExp, String midDate, String firstDate, Integer gender) {
        return sysEmpInfoRepository.getEmpInfoListByGroupExp(belongOrgId, groupExp, midDate, firstDate, gender);
    }

    public List<Map> getQuotaRuleEmpList(String belongOrgId, String groupExp, String midDate, String firstDate, Integer leaveTypeId, List<Integer> empids, Long gender, String dateStr) {
        return sysEmpInfoRepository.getQuotaRuleEmpList(belongOrgId, groupExp, midDate, firstDate, leaveTypeId, empids, gender, dateStr);
    }

    public List<Long> getEmpIdListByWaGroup(String belongid, Long currentDate, Boolean isDefault, Integer waGroupId, Long startDate, Long endDate, String datafilter) {
        return sysEmpInfoRepository.getEmpIdListByWaGroup(belongid, currentDate, isDefault, waGroupId, startDate, endDate, datafilter);
    }

    public PageList<Map> getEmpInfoPageListForCalendar(PageBean pageBean, String belongOrgId,
                                                       String orgId, Long startDate, Long endDate,
                                                       String keywords, String datafilter) {
        return sysEmpInfoRepository.getEmpInfoPageListForCalendar(pageBean, belongOrgId, orgId, startDate, endDate,
                keywords, datafilter);
    }

    public List<SysEmpInfo> getEmpInfoByIds(String belongOrgId, List<Long> empIds) {
        return sysEmpInfoRepository.getEmpInfoByIds(belongOrgId, empIds);
    }

    public SysEmpInfo getEmpInfoById(String belongOrgId, Long empId) {
        return sysEmpInfoRepository.getEmpInfoById(belongOrgId, empId);
    }

    public PageList<SysEmpInfoDo> getEmpList(PageBean pageBean, String belongOrgId) {
        return sysEmpInfoRepository.getEmpInfoPageList(pageBean, belongOrgId);
    }

    public List<SysEmpInfo> getEmpInfoByWorkNos(String belongOrgId, List<String> workNos) {
        if (CollectionUtils.isEmpty(workNos)) {
            return new ArrayList<>();
        }
        return sysEmpInfoRepository.getEmpInfoByWorkNos(belongOrgId, workNos);
    }

    public List<Long> getLeaderOrgEmpIds(String belongOrgId, Long leaderEmpId) {
        if (leaderEmpId == null || leaderEmpId == 0) {
            return new ArrayList<>();
        }
        return sysEmpInfoRepository.getLeaderOrgEmpIds(belongOrgId, leaderEmpId);
    }

    public List<Long> getEmpIdsByLeader(String belongOrgId, Long leaderEmpId) {
        if (leaderEmpId == null || leaderEmpId == 0) {
            return new ArrayList<>();
        }
        return sysEmpInfoRepository.getEmpIdsByLeader(belongOrgId, leaderEmpId);
    }

    public List<Long> getEmpIdsByCost(String belongOrgId, Long costId) {
        if (costId == null || costId == 0) {
            return new ArrayList<>();
        }
        return sysEmpInfoRepository.getEmpIdsByCost(belongOrgId, costId);
    }

    public PageList<SysEmpInfoDo> getEmpPageList(PageBean pageBean, String tenantId, String keywords, String dataFilter) {
        return sysEmpInfoRepository.getEmpPageList(pageBean, tenantId, keywords, dataFilter);
    }

    public List<EmpInfoDTO> getEmpDtoList(String tenantId, List<Long> empIds) {
        return sysEmpInfoRepository.getEmpDtoList(tenantId, empIds);
    }

    public List<Long> getEmpList(String tenantId, Long startDate, Long endDate, String keywords, List<Long> shiftGroupIds, List<Long> orgIds, String dataFilter) {
        return sysEmpInfoRepository.getEmpList(tenantId, startDate, endDate, keywords, shiftGroupIds, orgIds, dataFilter);
    }
}
