package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaEmpQuotaDetailMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpQuotaMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpQuotaUseMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AnnualLeaveDto;
import com.caidaocloud.attendance.service.application.dto.EmpLeaveQuotaDto;
import com.caidaocloud.attendance.service.application.dto.quota.AnnualLeaveRetainDto;
import com.caidaocloud.attendance.service.application.dto.quota.ImportRetainQuotaDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.ILeaveTypeService;
import com.caidaocloud.attendance.service.application.service.IQuotaService;
import com.caidaocloud.attendance.service.application.service.IWaLeaveTypeDefService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.QuotaMapper;
import com.caidaocloud.attendance.service.infrastructure.util.NumberCheckUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveRetainRequest;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveUpdateDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.poi.hssf.usermodel.HSSFDataFormat;
import org.apache.poi.hssf.usermodel.HSSFDateUtil;
import org.apache.poi.ss.usermodel.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.caidaocloud.attendance.service.infrastructure.common.QuotaTimeFormat.formatFloat;

@Slf4j
@Service
public class AnnualLeaveService {
    @Resource
    private ISessionService sessionService;
    @Resource
    private QuotaMapper quotaMapper;
    @Resource
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Resource
    private WaEmpQuotaDetailMapper waEmpQuotaDetailMapper;
    @Resource
    private WaEmpQuotaUseMapper waEmpQuotaUseMapper;
    @Resource
    private WaLeaveTypeDo waLeaveTypeDo;
    @Resource
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;
    @Resource
    private IQuotaService quotaService;
    @Autowired
    private DataBackupDo dataBackupDo;
    @Resource
    private TextAspect textAspect;
    @Resource
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private IWaLeaveTypeDefService leaveTypeDefService;
    @Autowired
    private SysEmpInfoDo sysEmpInfo;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private RemoteImportService importService;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @CDText(exp = {"empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = AnnualLeaveDto.class)
    public PageList<AnnualLeaveDto> pageList(AnnualLeaveSearchDto dto, Integer empQuotaId, UserInfo userInfo) {
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("nowTime", DateUtil.getOnlyDate());
        params.put("empQuotaId", empQuotaId);
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate());
        params.put("leaveType", dto.getLeaveType());
        if (dto.getPeriodYear() != null) {
            params.put("years", dto.getPeriodYear());
        }
        if (null != dto.getEmpid()) {
            params.put("empid", dto.getEmpid());
        }
        if (null != dto.getKeywords()) {
            params.put("keywords", dto.getKeywords());
        }
        if (CollectionUtils.isNotEmpty(dto.getFilterList())) {
            Iterator<FilterBean> it = dto.getFilterList().iterator();
            while (it.hasNext()) {
                FilterBean filterBean = it.next();
                if ("annualLeaveStatus".equals(filterBean.getField())) {
                    params.put("annualLeaveStatus", Boolean.valueOf(filterBean.getMin()));
                    it.remove();
                }
            }
        }
        String filter = dto.getFilter();
        if (filter != null) {
            if (filter.contains("periodYear")) {
                filter = filter.replaceAll("periodYear", "period_year");
            }
        }
        params.put("filter", filter);
        params.put("datafilter", dto.getDataScope());
        PageBean pageBean = PageUtil.getPageBean(dto);
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), dto.getPageSize(), Order.formString("start_date.desc,emp_quota_id.desc"));
        PageList<Map> empQuotaList = quotaMapper.getAnnualLeavePageList(pageBounds, params);
        PageList<AnnualLeaveDto> list = new PageList<>(empQuotaList.getPaginator());
        AnnualLeaveDto annualLeaveDto = null;
        Map<Object, String> empStatusMap = new HashMap<>();
        //val leaveTypes = leaveTypeService.getLeaveTypes(null, null, null);
        val leaveTypes = leaveTypeService.getLeaveTypes(userInfo, null, null, null);
        val leaveDefList = leaveTypeDefService.getWaLeaveTypeDefList(userInfo.getTenantId());
        for (Map row : empQuotaList) {
            annualLeaveDto = new AnnualLeaveDto();
            annualLeaveDto.setConfigId((Long) row.get("configId"));
            annualLeaveDto.setQuotaName((String) row.get("ruleName"));
            if (null != row.get("i18nRuleName")) {
                String i18n = LangParseUtil.getI18nLanguage(row.get("i18nRuleName").toString(), null);
                if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                    annualLeaveDto.setQuotaName(i18n);
                }
            }
            annualLeaveDto.setEmpQuotaId((Integer) row.get("emp_quota_id"));
            annualLeaveDto.setEmpid((Long) row.get("empid"));
            annualLeaveDto.setEmpName((String) row.get("emp_name"));
            annualLeaveDto.setWorkno((String) row.get("workno"));
            annualLeaveDto.setHireDate((Long) row.get("hire_date"));
            annualLeaveDto.setFirstWorkDate((Long) row.get("first_work_date"));
            annualLeaveDto.setFullPath((String) row.get("fullPath"));
            annualLeaveDto.setStartDate((Long) row.get("start_date"));
            annualLeaveDto.setLastDate((Long) row.get("last_date"));
            annualLeaveDto.setValidityUnit((Integer) row.get("validity_unit"));
            annualLeaveDto.setLeaveTypeId((Integer) row.get("leaveTypeId"));
            annualLeaveDto.setTerminationDate((Long) row.get("termination_date"));
            Integer acctTimeType = (Integer) row.get("acct_time_type");
            // 单位
            annualLeaveDto.setAcctTimeType(acctTimeType);
            // 假期类型
            annualLeaveDto.setLeaveName((String) row.get("leaveName"));
            // 年份
            annualLeaveDto.setPeriodYear((Integer) row.get("period_year"));
            // 发放周期开启日期
            annualLeaveDto.setDisCycleStart((Long) row.get("dis_cycle_start"));
            // 发放周期结束日期
            annualLeaveDto.setDisCycleEnd((Long) row.get("dis_cycle_end"));
            // 有效期
            annualLeaveDto.setValidityDuration((Float) row.get("validity_duration"));
            // 有效期单位
            annualLeaveDto.setValidityUnit((Integer) row.get("validity_unit"));
            // 可否预支
            annualLeaveDto.setIfAdvance((Integer) row.get("if_advance"));
            // 本年已用
            annualLeaveDto.setUsedDay((Float) row.get("used_day"));
            // 本年额度
            BigDecimal quotaDay = row.get("quota_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("quota_day"));
            // 当前额度
            BigDecimal nowQuota = row.get("now_quota") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("now_quota"));
            BigDecimal deductionDay = row.get("deduction_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("deduction_day"));
            annualLeaveDto.setDeductionDay(deductionDay.floatValue());
            BigDecimal adjustQuota = row.get("adjust_quota") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("adjust_quota"));
            // 调整额度
            annualLeaveDto.setAdjustQuota(adjustQuota.floatValue());
            BigDecimal fixUsedDay = row.get("fix_used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("fix_used_day"));
            // 单位
            annualLeaveDto.setTimeUnitName(PreTimeUnitEnum.getName(acctTimeType));
            // 分钟转小时 进位规则 1 四舍五入
            Integer carryRule = 1;
            // 本年额度
            annualLeaveDto.setQuotaDay(formatFloat(acctTimeType, quotaDay.floatValue(), carryRule));
            BigDecimal usedDay = row.get("used_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("used_day"));
            // 本年已用
            annualLeaveDto.setUsedDay(formatFloat(acctTimeType, usedDay.floatValue(), carryRule));
            // 当前额度
            annualLeaveDto.setNowQuota(formatFloat(acctTimeType, nowQuota.floatValue(), carryRule));
            // 调整额度
            annualLeaveDto.setAdjustQuota(formatFloat(acctTimeType, adjustQuota.floatValue(), carryRule));
            // 调整本年已用
            annualLeaveDto.setFixUsedDay(formatFloat(acctTimeType, fixUsedDay.floatValue(), carryRule));
            //结转额度
            BigDecimal remainDay = row.get("remain_day") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("remain_day"));
            annualLeaveDto.setRemainDay(formatFloat(acctTimeType, remainDay.floatValue(), carryRule));
            //上年留存配额
            BigDecimal retainDay = row.get("retainDay") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("retainDay"));
            annualLeaveDto.setRetainDay(formatFloat(acctTimeType, retainDay.floatValue(), carryRule));
            //上年留存配额流程中
            Float retainInTransitQuota = row.get("retainInTransitQuota") == null ? 0f : (Float) row.get("retainInTransitQuota");
            annualLeaveDto.setRetainInTransitQuota(retainInTransitQuota);
            //上年留存已使用
            BigDecimal retainUsedDay = row.get("retainUsedDay") == null ? BigDecimal.ZERO : BigDecimal.valueOf((Float) row.get("retainUsedDay"));
            annualLeaveDto.setRetainUsedDay(formatFloat(acctTimeType, retainUsedDay.floatValue(), carryRule));
            //留存有效期至
            annualLeaveDto.setRetainValidDate((Long) row.get("retainValidDate"));
            // 在途 = 流程中 + 留存流程中
            Float inTransitQuota = 0f;
            if (row.containsKey("inTransitQuota") && row.get("inTransitQuota") != null) {
                inTransitQuota = (Float) row.get("inTransitQuota");
            }
            annualLeaveDto.setInTransitQuota(formatFloat(acctTimeType, inTransitQuota, carryRule));
            // 失效日期 >= 当期日期就是生效，否则就是失效
            annualLeaveDto.checkAnnualLeaveStatus();
            // 预支年假 = 本年已用 + 流程中 - 当前额度，老逻辑
            // 已预支年假 = 调整本年已用 + 本年已用 + 流程中 - 当前额度 - 调整额度，新逻辑
            annualLeaveDto.calAdvanceAnnualLeave();
            // 本年可用 = 可预支 ？本年额度 ：当前额度
            annualLeaveDto.calAvailableYear();
            if (row.get("terminationDate") != null) {
                // 本年余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用 - 流程中
                annualLeaveDto.calCurRemain2();
            } else {
                // 本年余额 = 本年可用 - 本年已用 + 调整额度 - 调整本年已用 - 流程中
                annualLeaveDto.calCurRemain();
            }
            // 设置备注
            annualLeaveDto.setRemarks((String) row.get("remarks"));
            if (null != row.get("stats")) {
                annualLeaveDto.setEmpStatus((Integer) row.get("stats"));
                if (empStatusMap.containsKey(row.get("stats"))) {
                    annualLeaveDto.setEmpStatusName(empStatusMap.get(row.get("stats")));
                } else {
                    String empStatusName = textAspect.getEmpStatusEnumText(row.get("stats").toString(), userInfo.getTenantId());
                    annualLeaveDto.setEmpStatusName(empStatusName);
                    empStatusMap.put(row.get("stats"), empStatusName);
                }
            } else {
                annualLeaveDto.setEmpStatusName("-");
            }
            // 当前余额 = 当前额度 + 调整额度 - 本年已用 - 调整本年已用
            // 当前余额 = 当前额度 + 调整额度 + 留存 - 本年已用 - 调整本年已用 - 留存已用
            annualLeaveDto.calCurrentQuota();
            for (LeaveTypeInfoDto leaveType : leaveTypes) {
                if (leaveType.getLeaveTypeId().equals(annualLeaveDto.getLeaveTypeId())) {
                    val leaveDef = leaveDefList.stream().filter(it ->
                            leaveType.getLeaveType().equals(it.getLeaveTypeDefId())
                    ).findFirst().orElse(null);
                    if ("探亲假".equals(leaveDef.getLeaveTypeDefCode())) {
                        annualLeaveDto.setHomeLeave(true);
                    }
                }
            }
            list.add(annualLeaveDto);
        }
        return list;
    }

    @Transactional
    public String saveOrUpdateAnnualLeave(AnnualLeaveUpdateDto dto) {
        WaEmpQuota waEmpQuota = ObjectConverter.convert(dto, WaEmpQuota.class);
        if (null != waEmpQuota.getEmpQuotaId()) {
            WaEmpQuota quota = waEmpQuotaMapper.selectByPrimaryKey(waEmpQuota.getEmpQuotaId());
            if (null != quota && quota.getIfCarryForward() != null && quota.getIfCarryForward()) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_ALREADY_CARRIED, null).getMsg();
            }
        }
        waEmpQuota.setEmpid(dto.getEmpInfo().getEmpId());
        UserInfo userInfo = this.getUserInfo();
        waEmpQuota.setBelongOrgId(userInfo.getTenantId());
        //查询额度配置信息
        LeaveQuotaConfigDo quotaConfigDo = leaveQuotaConfigDo.getConfigById(userInfo.getTenantId(), dto.getConfigId());
        if (quotaConfigDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_RULE_DOES_NOT_EXIST, null).getMsg();
        }
        //查询假期配置信息
        WaLeaveTypeDo leaveTypeDo = waLeaveTypeDo.selectById(quotaConfigDo.getLeaveTypeId());
        if (leaveTypeDo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null).getMsg();
        }
        LogRecordContext.putVariable("content", leaveTypeDo.getLeaveName());
        waEmpQuota.setLeaveTypeId(quotaConfigDo.getLeaveTypeId());
        int year = dto.getPeriodYear();
        SysEmpInfo sysEmpInfo = sysEmpInfoDo.getEmpInfoById(userInfo.getTenantId(), waEmpQuota.getEmpid());
        if (sysEmpInfo == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg();
        }
        if (QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(leaveTypeDo.getQuotaType())) {
            List<LeaveQuotaDo> quotas = quotaMapper.getNotCarryAnnualLeaveList(userInfo.getTenantId(), dto.getEmpQuotaId(), sysEmpInfo.getEmpid(), leaveTypeDo.getLeaveTypeId(),
                    quotaConfigDo.getConfigId(), leaveTypeDo.getQuotaType(), year);
            if (CollectionUtils.isNotEmpty(quotas)) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_ALREADY_EXIST, null).getMsg();
            }
        }
        //计算发放周期
        if (quotaConfigDo.getDistributionCycle() == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.QUOTA_ISSUING_CYCLE_IS_NOT_SET, null).getMsg();
        }
        //发放周期：1 自然年、2 入职年、3 自定义周期
        Integer distributionCycle = quotaConfigDo.getDistributionCycle();
        //发放周期开始日
        Long disCycleStart;
        //发放周期结束日
        Long disCycleEnd = null;
        if (distributionCycle.equals(DistributionCycleEnum.NATURAL_YEAR.getIndex())) {
            //自然年
            Calendar calendar = Calendar.getInstance();
            calendar.set(year, 0, 1, 0, 0, 0);
            disCycleStart = calendar.getTime().getTime() / 1000;
            try {
                disCycleEnd = DateUtilExt.getYearsEndTime(disCycleStart);
            } catch (ParseException e) {
                log.error("AnnualLeaveService.saveOrUpdateAnnualLeave error msg {}", e.getMessage(), e);
                return ResponseWrap.wrapResult(AttendanceCodes.DATE_PARSE_ERROR, null).getMsg();
            }
        } else if (distributionCycle.equals(DistributionCycleEnum.CUSTOM_CYCLE.getIndex())) {
            //自定义周期
            if (quotaConfigDo.getDisCycleStart() == null) {
                return ResponseWrap.wrapResult(AttendanceCodes.QUOTA_ISSUING_CYCLE_IS_NOT_SET, null).getMsg();
            }
            Calendar startCal = Calendar.getInstance();
            startCal.setTimeInMillis(quotaConfigDo.getDisCycleStart() * 1000);
            startCal.set(Calendar.YEAR, year);
            disCycleStart = startCal.getTime().getTime() / 1000;
            disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 1, 0, -1);
        } else if (distributionCycle.equals(DistributionCycleEnum.CHILD_YEAR.getIndex())) {
            disCycleStart = waEmpQuota.getStartDate();
            disCycleEnd = waEmpQuota.getLastDate();
        } else {
            //入职年
            Long hireDate = sysEmpInfo.getHireDate();
            if (hireDate == null) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_HIRE_DATE_ISNULL, null).getMsg();
            }
            Calendar cal = Calendar.getInstance();
            cal.setTimeInMillis(hireDate * 1000);
            Calendar cal1 = Calendar.getInstance();
            cal1.set(year, cal.get(Calendar.MONTH), cal.get(Calendar.DATE), 0, 0, 0);
            disCycleStart = cal1.getTime().getTime() / 1000;
            disCycleEnd = DateUtilExt.addMonthDate(disCycleStart * 1000, 12, -1);
        }
        waEmpQuota.setDisCycleStart(disCycleStart);
        waEmpQuota.setDisCycleEnd(disCycleEnd);
        waEmpQuota.setIfAdvance(quotaConfigDo.getIfAdvance());
        if (waEmpQuota.getEmpQuotaId() == null) {
            //初始化默认值
            if (waEmpQuota.getRemainDay() == null) {
                waEmpQuota.setRemainDay(0f);
            }
            if (waEmpQuota.getRemainUsedDay() == null) {
                waEmpQuota.setRemainUsedDay(0f);
            }
            if (waEmpQuota.getDeductionDay() == null) {
                waEmpQuota.setDeductionDay(0f);
            }
            if (waEmpQuota.getQuotaDay() == null) {
                waEmpQuota.setQuotaDay(0f);
            }
            if (waEmpQuota.getUsedDay() == null) {
                waEmpQuota.setUsedDay(0f);
            }
        }
        if (leaveTypeDo.getAcctTimeType() != null && 2 == leaveTypeDo.getAcctTimeType()) {
            waEmpQuota.setQuotaDay(waEmpQuota.getQuotaDay() * 60);
            waEmpQuota.setUsedDay(waEmpQuota.getUsedDay() * 60);
            waEmpQuota.setAdjustQuota(waEmpQuota.getAdjustQuota() * 60);
            waEmpQuota.setFixUsedDay(waEmpQuota.getFixUsedDay() * 60);
        }
        waEmpQuota.setOriginalQuotaDay(waEmpQuota.getQuotaDay());
        //当前额度折算
        if (waEmpQuota.getQuotaDay() > 0) {
            EmpLeaveQuotaDto leaveQuotaDto = new EmpLeaveQuotaDto();
            leaveQuotaDto.setEmpId(waEmpQuota.getEmpid());
            leaveQuotaDto.setBelongOrgId(waEmpQuota.getBelongOrgId());
            leaveQuotaDto.setHireDate(sysEmpInfo.getHireDate());
            leaveQuotaDto.setProdeadLine(sysEmpInfo.getProdeadLine());
            leaveQuotaDto.setTerminationDate(sysEmpInfo.getTerminationDate());
            leaveQuotaDto.setLeaveTypeId(quotaConfigDo.getLeaveTypeId());
            leaveQuotaDto.setAcctTimeType(leaveTypeDo.getAcctTimeType());
            leaveQuotaDto.setQuotaVal(BigDecimal.valueOf(waEmpQuota.getQuotaDay()));
            leaveQuotaDto.setDisCycleStart(disCycleStart);
            leaveQuotaDto.setDisCycleEnd(disCycleEnd);
            leaveQuotaDto.setOriginalQuotaDay(BigDecimal.valueOf(waEmpQuota.getQuotaDay()));
            if (leaveTypeDo.getAcctTimeType() != null && 2 == leaveTypeDo.getAcctTimeType()) {
                leaveQuotaDto.setOriginalQuotaDay(leaveQuotaDto.getOriginalQuotaDay().divide(BigDecimal.valueOf(60), 4, RoundingMode.DOWN));
            }
            if (quotaConfigDo.getIfAdvance() == null) {
                leaveQuotaDto.setIfAdvance(0);
            } else {
                leaveQuotaDto.setIfAdvance(quotaConfigDo.getIfAdvance());
            }
            leaveQuotaDto.setNowDistributeRule(quotaConfigDo.getNowDistributeRule());
            leaveQuotaDto.setNowRoundingRule(quotaConfigDo.getNowRoundingRule());
            leaveQuotaDto.setEmpStatus(sysEmpInfo.getStats());
            leaveQuotaDto.setWaEmpQuotaQuotaDay(waEmpQuota.getQuotaDay());
            leaveQuotaDto.setCalNowQuotaByCurrentYearQuota(true);
            leaveQuotaDto.setQuotaDistributeRule(quotaConfigDo.getQuotaDistributeRule());
            leaveQuotaDto.setDayOfHireMonthDist(quotaConfigDo.getDayOfHireMonthDist());
            waEmpQuota.setNowQuota(quotaService.calNowQuota(leaveQuotaDto));
        } else {
            waEmpQuota.setNowQuota(0f);
        }
        if (waEmpQuota.getEmpQuotaId() == null) {
            waEmpQuota.setCrtuser(userInfo.getUserId());
            waEmpQuota.setCrttime(DateUtil.getCurrentTime(true));
            waEmpQuota.setUpduser(userInfo.getUserId());
            waEmpQuota.setUpdtime(DateUtil.getCurrentTime(true));
            waEmpQuotaMapper.insertSelective(waEmpQuota);
        } else {
            waEmpQuota.setUpduser(userInfo.getUserId());
            waEmpQuota.setUpdtime(DateUtil.getCurrentTime(true));
            waEmpQuotaMapper.updateByPrimaryKeySelective(waEmpQuota);
        }
        return "";
    }

    public AnnualLeaveDto getAnnualLeaveDetails(Integer empQuotaId) {
        AnnualLeaveSearchDto dto = new AnnualLeaveSearchDto();
        dto.setPageNo(0);
        dto.setPageSize(1);
        List<AnnualLeaveDto> list = pageList(dto, empQuotaId, getUserInfo());

        if (list == null || list.isEmpty()) {
            return null;
        }
        AnnualLeaveDto leaveDto = list.get(0);
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(getUserInfo().getTenantId(), leaveDto.getEmpid());
        EmpInfoDTO empInfoDTO = ObjectConverter.convert(empInfo, EmpInfoDTO.class);
        empInfoDTO.setEmpId(empInfo.getEmpid());
        empInfoDTO.setName(empInfo.getEmpName());
        leaveDto.setEmpInfo(empInfoDTO);
        return leaveDto;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteEmpQuota(Integer id) {
        String tenantId = sessionService.getUserInfo().getTenantId();
        WaEmpQuota waEmpQuota = this.waEmpQuotaMapper.selectByPrimaryKey(id);
        if (null == waEmpQuota) {
            return;
        }
        LogRecordContext.putVariable("empId", waEmpQuota.getEmpid());
        WaLeaveTypeDo typeDo = this.waLeaveTypeDo.selectById(waEmpQuota.getLeaveTypeId());
        LogRecordContext.putVariable("content", typeDo.getLeaveName());
        boolean deleteFlag = (null != waEmpQuota.getInTransitQuota() && waEmpQuota.getInTransitQuota() > 0) || (null != waEmpQuota.getUsedDay() && waEmpQuota.getUsedDay() > 0);
        if (deleteFlag) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_DELETE_NOT_ALLOW, null).getMsg());
        }
        WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
        empQuotaExample.createCriteria().andEmpidEqualTo(waEmpQuota.getEmpid()).andMergeEmpQuotaIdEqualTo(waEmpQuota.getEmpQuotaId());
        List<WaEmpQuota> carries = waEmpQuotaMapper.selectByExample(empQuotaExample);
        for (WaEmpQuota carry : carries) {
            if ((null != carry.getInTransitQuota() && carry.getInTransitQuota() > 0) || (null != carry.getUsedDay() && carry.getUsedDay() > 0)) {
                throw new ServerException("该配额的留存配额已被使用，不可删除");
            }
            waEmpQuotaMapper.deleteByPrimaryKey(carry.getEmpQuotaId());
        }
        this.waEmpQuotaMapper.deleteByPrimaryKey(id);
        WaEmpQuotaDetailExample detailExample = new WaEmpQuotaDetailExample();
        detailExample.createCriteria().andEmpQuotaIdEqualTo(id).andBelongOrgIdEqualTo(tenantId);
        List<WaEmpQuotaDetail> quotaDetailList = this.waEmpQuotaDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isEmpty(quotaDetailList)) {
            return;
        }
        List<Integer> idList = new ArrayList<>();
        for (WaEmpQuotaDetail detail : quotaDetailList) {
            idList.add(detail.getEmpQuotaDetailId());
        }
        if (!idList.isEmpty()) {
            WaEmpQuotaUseExample useExample = new WaEmpQuotaUseExample();
            useExample.createCriteria().andEmpQuotaDetailIdIn(idList);
            this.waEmpQuotaUseMapper.deleteByExample(useExample);
        }
        this.waEmpQuotaDetailMapper.deleteByExample(detailExample);
    }

    @Transactional(rollbackFor = Exception.class)
    public Result<String> deleteEmpQuotas(List<Integer> ids) {
        UserInfo userInfo = getUserInfo();
        String belongOrgId = userInfo.getTenantId();
        WaEmpQuotaExample empQuotaExample = new WaEmpQuotaExample();
        empQuotaExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpQuotaIdIn(ids);
        List<WaEmpQuota> list = waEmpQuotaMapper.selectByExample(empQuotaExample);
        if (CollectionUtils.isEmpty(list)) {
            return Result.success();
        }
        List<WaEmpQuota> deletedEmpQuotas = list.stream().filter(waEmpQuota -> !((null != waEmpQuota.getInTransitQuota() && waEmpQuota.getInTransitQuota() > 0) || (null != waEmpQuota.getUsedDay() && waEmpQuota.getUsedDay() > 0))).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(deletedEmpQuotas)) {
            return Result.ok(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deletedEmpQuotas.size(), ids.size()));
        }
        // 删除员工配额
        List<Integer> deletedEmpQuotaIds = deletedEmpQuotas.stream().map(WaEmpQuota::getEmpQuotaId).distinct().collect(Collectors.toList());
        for (WaEmpQuota deletedEmpQuota : deletedEmpQuotas) {
            WaEmpQuotaExample quotaExample = new WaEmpQuotaExample();
            quotaExample.createCriteria().andEmpidEqualTo(deletedEmpQuota.getEmpid()).andMergeEmpQuotaIdEqualTo(deletedEmpQuota.getEmpQuotaId());
            List<WaEmpQuota> carries = waEmpQuotaMapper.selectByExample(quotaExample);
            for (WaEmpQuota carry : carries) {
                if ((null != carry.getInTransitQuota() && carry.getInTransitQuota() > 0) || (null != carry.getUsedDay() && carry.getUsedDay() > 0)) {
                    return Result.ok(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deletedEmpQuotas.size(), ids.size()));
                }
                deletedEmpQuotaIds.add(carry.getEmpQuotaId());
            }
        }
        dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_QUOTA.name(), ModuleTypeEnum.EMP_QUOTA.getTable(), deletedEmpQuotas, userInfo));
        WaEmpQuotaExample delEmpQuotaExample = new WaEmpQuotaExample();
        delEmpQuotaExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpQuotaIdIn(deletedEmpQuotaIds);
        waEmpQuotaMapper.deleteByExample(delEmpQuotaExample);
        // 查询员工配额详情
        WaEmpQuotaDetailExample detailExample = new WaEmpQuotaDetailExample();
        detailExample.createCriteria().andBelongOrgIdEqualTo(belongOrgId).andEmpQuotaIdIn(deletedEmpQuotaIds);
        List<WaEmpQuotaDetail> quotaDetailList = waEmpQuotaDetailMapper.selectByExample(detailExample);
        if (CollectionUtils.isNotEmpty(quotaDetailList)) {
            List<Integer> idList = quotaDetailList.stream().map(WaEmpQuotaDetail::getEmpQuotaDetailId).distinct().collect(Collectors.toList());
            WaEmpQuotaUseExample useExample = new WaEmpQuotaUseExample();
            useExample.createCriteria().andEmpQuotaDetailIdIn(idList);
            List<WaEmpQuotaUse> quotaUsedList = waEmpQuotaUseMapper.selectByExample(useExample);
            if (CollectionUtils.isNotEmpty(quotaUsedList)) {
                dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_QUOTA_USE.name(), ModuleTypeEnum.EMP_QUOTA_USE.getTable(), quotaUsedList, userInfo));
                // 删除员工配额使用详情
                waEmpQuotaUseMapper.deleteByExample(useExample);
            }
            // 删除员工配额详情
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_QUOTA_DETAIL.name(), ModuleTypeEnum.EMP_QUOTA_DETAIL.getTable(), quotaDetailList, userInfo));
            waEmpQuotaDetailMapper.deleteByExample(detailExample);
        }
        LogRecordContext.putVariable("num", deletedEmpQuotas.size());
        return Result.ok(String.format(ResponseWrap.wrapResult(AttendanceCodes.DELETE_COMPENSATORY_REMINDER, null).getMsg(), deletedEmpQuotas.size(), ids.size() - deletedEmpQuotas.size()));
    }

    public List<AnnualLeaveRetainDto> getAnnualLeaveRetainList(Integer empQuotaId) {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        WaEmpQuota mergeEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(empQuotaId);
        //查询假期配置信息
        Integer leaveTypeId = mergeEmpQuota.getLeaveTypeId();
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(leaveTypeId);
        if (leaveType == null) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null).getMsg());
        }
        //非年假配额校验
        if (leaveType.getQuotaType() != null && leaveType.getQuotaType() == 4) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_CARRY_NOT_ALLOW, null).getMsg());
        }
        //假期额度规则
        Long configId = mergeEmpQuota.getConfigId();
        LeaveQuotaConfigDo config = leaveQuotaConfigDo.getConfigById(tenantId, configId);
        if (config == null) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_RULE_DOES_NOT_EXIST, null).getMsg());
        }
        //是否可结转配额
        boolean carryMerge = null != config.getCarryOverTo() && config.getCarryOverTo() == -1 && null != config.getCarryToType() && config.getCarryToType() == 2;
        if (!carryMerge) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_CARRY_NOT_ALLOW, null).getMsg());
        }
        List<WaEmpQuotaDo> quotas = quotaMapper.queryAnnualLeaveRetainQuota(tenantId, empQuotaId);
        if (CollectionUtils.isEmpty(quotas)) {
            return Lists.newArrayList();
        }
        List<Long> originalEmpQuotaIds = quotas.stream().map(WaEmpQuotaDo::getOriginalEmpQuotaId).map(Long::valueOf).collect(Collectors.toList());
        Map<Integer, String> empQuotaNameMap = getOriginalEmpQuotaName(tenantId, originalEmpQuotaIds);
        List<AnnualLeaveRetainDto> list = ObjectConverter.convertList(quotas, AnnualLeaveRetainDto.class);
        for (AnnualLeaveRetainDto item : list) {
            Integer acctTimeType = item.getAcctTimeType();
            item.setTimeUnitName(PreTimeUnitEnum.getName(acctTimeType));
            Integer carryRule = 1;
            // 额度
            item.setQuotaDay(formatFloat(acctTimeType, item.getQuotaDay(), carryRule));
            // 已使用
            item.setUsedDay(formatFloat(acctTimeType, item.getUsedDay(), carryRule));
            // 流程中
            item.setInTransitQuota(formatFloat(acctTimeType, Optional.ofNullable(item.getInTransitQuota()).orElse(0f), carryRule));
            item.setQuotaName(Optional.ofNullable(empQuotaNameMap.get(item.getOriginalEmpQuotaId())).orElse(""));
        }
        return list;
    }

    private Map<Integer, String> getOriginalEmpQuotaName(String tenantId, List<Long> empQuotaIds) {
        Map<Integer, String> empQuotaNameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(empQuotaIds)) {
            return empQuotaNameMap;
        }
        List<WaEmpQuotaDo> empQuotas = quotaMapper.queryEmpQuotaList(tenantId, empQuotaIds);
        if (CollectionUtils.isEmpty(empQuotas)) {
            return empQuotaNameMap;
        }
        List<Long> configIds = empQuotas.stream().map(WaEmpQuotaDo::getConfigId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(configIds)) {
            return empQuotaNameMap;
        }
        List<LeaveQuotaConfigDo> configs = leaveQuotaConfigDo.getLeaveQuotaConfigByIds(tenantId, configIds);
        if (CollectionUtils.isEmpty(configs)) {
            return empQuotaNameMap;
        }
        Map<Long, LeaveQuotaConfigDo> configMap = configs.stream().collect(Collectors.toMap(LeaveQuotaConfigDo::getConfigId, Function.identity(), (v1, v2) -> v2));
        empQuotaNameMap = empQuotas.stream().collect(Collectors.toMap(WaEmpQuotaDo::getEmpQuotaId, quota -> {
            LeaveQuotaConfigDo config = configMap.get(quota.getConfigId());
            return null != config ? LangParseUtil.getI18nLanguage(config.getI18nRuleName(), config.getRuleName()) : "";
        }));
        return empQuotaNameMap;
    }

    public AnnualLeaveRetainDto getAnnualLeaveRetainDetail(Integer empQuotaId) {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        //留存配额
        WaEmpQuota quota = waEmpQuotaMapper.selectByPrimaryKey(empQuotaId);
        //假期类型
        Integer leaveTypeId = quota.getLeaveTypeId();
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(leaveTypeId);
        String leaveTypeName = LangParseUtil.getI18nLanguage(leaveType.getI18nLeaveName(), leaveType.getLeaveName());
        //假期额度
        Long configId = quota.getConfigId();
        LeaveQuotaConfigDo config = leaveQuotaConfigDo.getConfigById(tenantId, configId);
        String ruleName = LangParseUtil.getI18nLanguage(config.getI18nRuleName(), config.getRuleName());
        Integer acctTimeType = leaveType.getAcctTimeType();
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(tenantId, quota.getEmpid());
        AnnualLeaveRetainDto item = ObjectConverter.convert(quota, AnnualLeaveRetainDto.class);
        item.setEmpName(empInfo.getEmpName());
        item.setWorkno(empInfo.getWorkno());
        Integer carryRule = 1;
        // 额度
        item.setQuotaDay(formatFloat(acctTimeType, item.getQuotaDay(), carryRule));
        // 已使用
        item.setUsedDay(formatFloat(acctTimeType, item.getUsedDay(), carryRule));
        // 流程中
        item.setInTransitQuota(formatFloat(acctTimeType, item.getInTransitQuota(), carryRule));
        item.setTimeUnitName(PreTimeUnitEnum.getName(acctTimeType));
        item.setQuotaName(ruleName);
        item.setLeaveTypeId(leaveTypeId);
        item.setLeaveTypeName(leaveTypeName);
        return item;
    }

    @Transactional
    public Result<Boolean> saveOrUpdateAnnualLeaveRetain(AnnualLeaveRetainRequest dto) {
        UserInfo userInfo = getUserInfo();
        String tenantId = userInfo.getTenantId();
        //查询假期配置信息
        Integer leaveTypeId = dto.getLeaveTypeId();
        WaLeaveTypeDo leaveType = waLeaveTypeDo.selectById(leaveTypeId);
        if (leaveType == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, Boolean.FALSE);
        }
        //假期额度规则
        Long configId = dto.getConfigId();
        LeaveQuotaConfigDo config = leaveQuotaConfigDo.getConfigById(tenantId, configId);
        if (config == null) {
            return ResponseWrap.wrapResult(AttendanceCodes.LEAVE_QUOTA_RULE_DOES_NOT_EXIST, Boolean.FALSE);
        }
        Integer acctTimeType = leaveType.getAcctTimeType();
        Float retainDay = dto.getQuotaDay();
        if (PreTimeUnitEnum.HOUR.getIndex().equals(acctTimeType)) {
            retainDay = retainDay * 60;
        }
        WaEmpQuota carryQuota = new WaEmpQuota();
        carryQuota.setQuotaDay(retainDay);
        carryQuota.setNowQuota(retainDay);
        carryQuota.setRemarks(Optional.ofNullable(dto.getRemarks()).orElse(""));
        carryQuota.setUpdtime(DateUtil.getCurrentTime(true));
        carryQuota.setUpduser(userInfo.getUserId());
        Integer mergeEmpQuotaId = dto.getMergeEmpQuotaId();
        WaEmpQuota mergeEmpQuota = waEmpQuotaMapper.selectByPrimaryKey(mergeEmpQuotaId);
        if (dto.getLastDate() > mergeEmpQuota.getLastDate()) {
            return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_RETAIN_VALIDATE_GREATER_THAN_TO, Boolean.FALSE);
        }
        carryQuota.setLastDate(dto.getLastDate());
        if (null == dto.getEmpQuotaId()) {
            if (CollectionUtils.isNotEmpty(quotaMapper.queryAnnualLeaveRetainQuota(tenantId, mergeEmpQuotaId))) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_CARRY_ALREADY_EXIST, Boolean.FALSE);
            }
            carryQuota.setEmpid(dto.getEmpid());
            carryQuota.setBelongOrgId(tenantId);
            carryQuota.setUsedDay(0f);
            carryQuota.setFixUsedDay(0f);
            carryQuota.setAdjustQuota(0f);
            carryQuota.setIfAdvance(1);
            carryQuota.setRemainDay(0f);
            carryQuota.setCrttime(DateUtil.getCurrentTime(true));
            carryQuota.setCrtuser(userInfo.getUserId());
            carryQuota.setLeaveTypeId(leaveTypeId);
            carryQuota.setConfigId(dto.getConfigId());
            carryQuota.setCarryMerge(true);
            carryQuota.setValidityDuration(config.getCarryOverValidityDuration());
            carryQuota.setValidityUnit(config.getCarryOverValidityUnit());
            carryQuota.setStartDate(mergeEmpQuota.getStartDate());
            carryQuota.setPeriodYear(mergeEmpQuota.getPeriodYear());
            carryQuota.setDisCycleStart(mergeEmpQuota.getDisCycleStart());
            carryQuota.setDisCycleEnd(mergeEmpQuota.getDisCycleEnd());
            carryQuota.setMergeEmpQuotaId(mergeEmpQuota.getEmpQuotaId());
            //记录源配额ID
            List<WaEmpQuotaDo> expiredNotCarryAnnual = quotaMapper.getExpiredNotCarryAnnualLeaveList(tenantId, dto.getEmpid(), leaveTypeId, configId, mergeEmpQuota.getPeriodYear() - 1, DateUtil.getCurrentTime(true));
            if (CollectionUtils.isEmpty(expiredNotCarryAnnual)) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_RETAIN_SOURCE_NOT_EXIST, Boolean.FALSE);
            }
            WaEmpQuotaDo originalEmpQuota = expiredNotCarryAnnual.get(0);
            if (originalEmpQuota.getIfCarryForward() != null && originalEmpQuota.getIfCarryForward()) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_CARRY_ALREADY_EXIST, Boolean.FALSE);
            }
            carryQuota.setOriginalEmpQuotaId(originalEmpQuota.getEmpQuotaId());
            waEmpQuotaMapper.insertSelective(carryQuota);
            WaEmpQuota originalQuota = new WaEmpQuota();
            originalQuota.setEmpQuotaId(originalEmpQuota.getEmpQuotaId());
            originalQuota.setRemainDay(carryQuota.getQuotaDay());
            originalQuota.setUpdtime(DateUtil.getCurrentTime(true));
            originalQuota.setUpduser(userInfo.getUserId());
            originalQuota.setIfCarryForward(true);
            waEmpQuotaMapper.updateByPrimaryKeySelective(originalQuota);
        } else {
            //留存配额
            WaEmpQuota oldQuota = waEmpQuotaMapper.selectByPrimaryKey(dto.getEmpQuotaId());
            if (null == oldQuota) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_NOT_EXIST, Boolean.FALSE);
            }
            Float usedDay = Optional.ofNullable(oldQuota.getUsedDay()).orElse(0f);
            Float inTransitQuota = Optional.ofNullable(oldQuota.getInTransitQuota()).orElse(0f);
            if (retainDay < usedDay + inTransitQuota) {
                return ResponseWrap.wrapResult(AttendanceCodes.EMP_QUOTA_RETAIN_LESS_THAN_USED, Boolean.FALSE);
            }
            carryQuota.setEmpQuotaId(oldQuota.getEmpQuotaId());
            waEmpQuotaMapper.updateByPrimaryKeySelective(carryQuota);
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 导入留存配额
     *
     * @param file 导入文件
     * @return
     */
    public Result<String> importEmpAnnualLeaveQuotaRetain(MultipartFile file) {
        try {
            log.info("Start to import emp annual leave quota retain");
            Workbook wb = quotaService.importExcelFile(file);
            Sheet sheet = wb.getSheetAt(0);
            int lastRowNum = sheet.getLastRowNum();
            UserInfo userInfo = sessionService.getUserInfo();
            String tenantId = userInfo.getTenantId();
            List<ImportRetainQuotaDto> list = new ArrayList<>();
            for (int i = 1; i <= lastRowNum; i++) {
                Row row = sheet.getRow(i);
                if (row != null) {
                    Cell workNoCell = row.getCell(0);
                    Cell leaveNameCell = row.getCell(1);
                    Cell quotaNameCell = row.getCell(2);
                    Cell periodYearCell = row.getCell(3);
                    Cell retainCell = row.getCell(4);
                    Cell retainValidDateCell = row.getCell(5);
                    Cell remarkCell = row.getCell(6);
                    //账号，必填
                    String workNo = "";
                    if (workNoCell != null) {
                        workNoCell.setCellType(CellType.STRING);
                        workNo = workNoCell.getStringCellValue().trim();
                    }
                    //假期名称，必填
                    String leaveName = "";
                    if (leaveNameCell != null) {
                        leaveNameCell.setCellType(CellType.STRING);
                        leaveName = leaveNameCell.getStringCellValue().trim();
                    }
                    //额度名称，必填
                    String quotaName = "";
                    if (quotaNameCell != null) {
                        quotaNameCell.setCellType(CellType.STRING);
                        quotaName = quotaNameCell.getStringCellValue().trim();
                    }
                    //年份，必填
                    String periodYear = "";
                    if (periodYearCell != null) {
                        periodYearCell.setCellType(CellType.STRING);
                        periodYear = periodYearCell.getStringCellValue().trim();
                    }
                    //上年留存，必填
                    String retainDay = "";
                    if (retainCell != null) {
                        retainCell.setCellType(CellType.STRING);
                        retainDay = retainCell.getStringCellValue().trim();
                    }
                    //上年留存有效期至，必填
                    String retainValidDateStr = getCellDateString(retainValidDateCell, "yyyy-MM-dd");
                    //备注，非必填
                    String remark = null;
                    if (remarkCell != null) {
                        remarkCell.setCellType(CellType.STRING);
                        remark = remarkCell.getStringCellValue().trim();
                    }
                    Integer rowNum = i + 1;
                    //必填校验
                    String checkCellErr = checkCellValue(workNo, leaveName, quotaName, periodYear, retainDay, retainValidDateStr, rowNum);
                    if (StringUtil.isNotBlank(checkCellErr)) {
                        return Result.fail(checkCellErr);
                    }
                    list.add(new ImportRetainQuotaDto(workNo, leaveName, quotaName, periodYear, retainDay, retainValidDateStr, remark, rowNum));
                }
            }
            if (CollectionUtils.isEmpty(list)) {
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_EMPTY, null);
            }
            log.info("End to parse excel file, data:[size:{}]", list.size());
            //校验数据重复
            Map<String, Long> workNoCountMap = list.stream().collect(Collectors.groupingBy(o ->
                    String.join("-", o.getWorkNo(), o.getLeaveName(), o.getQuotaName(), o.getPeriodYear()), Collectors.counting()));
            Optional<String> optional = workNoCountMap.keySet().stream().filter(workNo -> workNoCountMap.get(workNo) > 1).findFirst();
            if (optional.isPresent()) {
                return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_DATA_REPEAT, null).getMsg(), optional.get()));
            }
            List<String> workNos = list.stream().map(ImportRetainQuotaDto::getWorkNo).distinct().collect(Collectors.toList());
            List<SysEmpInfo> empList = sysEmpInfo.getEmpInfoByWorkNos(tenantId, workNos);
            if (CollectionUtils.isEmpty(empList)) {
                return ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_ERR, null);
            }
            List<WaEmpQuota> addList = Lists.newArrayList();
            List<WaEmpQuota> updList = Lists.newArrayList();
            Map<String, Long> workNoAndEmpIdMap = empList.stream().collect(Collectors.toMap(SysEmpInfo::getWorkno, SysEmpInfo::getEmpid));
            Map<Integer, List<LeaveTypeInfoDto>> leaveTypeGroupBy = new HashMap<>();
            Map<Long, GroupDetailDto> empGroupMap = new HashMap<>();
            List<LeaveQuotaConfigDo> configDoList = leaveQuotaConfigDo.listByTenantId(tenantId);
            for (ImportRetainQuotaDto dto : list) {
                String workNo = dto.getWorkNo();
                if (!workNoAndEmpIdMap.containsKey(workNo) || workNoAndEmpIdMap.get(workNo) == null) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_NOT_EXIST, null).getMsg(), dto.getRowNum(), workNo));
                }
                Long empId = workNoAndEmpIdMap.get(workNo);
                GroupDetailDto groupDetail;
                if (empGroupMap.containsKey(empId)) {
                    groupDetail = empGroupMap.get(empId);
                } else {
                    groupDetail = groupService.getEmpGroup(tenantId, empId, DateUtil.getCurrentTime(true));
                    if (null == groupDetail) {
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.EMP_NO_VALID_GROUP, null).getMsg(), dto.getRowNum(), workNo));
                    }
                    empGroupMap.put(empId, groupDetail);
                }
                if (null == groupDetail.getLeaveTypeIds()) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_GROUP_LEAVE_EMPTY, null).getMsg(), dto.getRowNum(), workNo));
                }
                List<LeaveTypeInfoDto> leaveTypeList;
                if (leaveTypeGroupBy.containsKey(groupDetail.getWaGroupId())) {
                    leaveTypeList = leaveTypeGroupBy.get(groupDetail.getWaGroupId());
                } else {
                    leaveTypeList = leaveTypeService.getLeaveTypes(groupDetail.getWaGroupId(), QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex(), QuotaTypeEnum.ISSUED_ANNUALLY.getIndex());
                    if (CollectionUtils.isEmpty(leaveTypeList)) {
                        return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_GROUP_LEAVE_EMPTY, null).getMsg(), dto.getRowNum(), workNo));
                    }
                    leaveTypeGroupBy.put(groupDetail.getWaGroupId(), leaveTypeList);
                }
                LeaveTypeInfoDto leaveType = leaveTypeList.stream().filter(l -> l.getLeaveName().equals(dto.getLeaveName()) && l.getStatus() == 1).findFirst().orElse(null);
                if (null == leaveType) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_GROUP_LEAVE_EMPTY, null).getMsg(), dto.getRowNum(), dto.getWorkNo(), dto.getLeaveName()));
                }
                LeaveQuotaConfigDo configDo = configDoList.stream().filter(config -> config.getRuleName().equals(dto.getQuotaName())
                        && config.getLeaveTypeId().equals(leaveType.getLeaveTypeId())).findFirst().orElse(null);
                if (null == configDo) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_NAME_ERR, null).getMsg(), dto.getRowNum(), dto.getQuotaName()));
                }
                //是否可结转配额
                boolean carryMerge = null != configDo.getCarryOverTo() && configDo.getCarryOverTo() == -1 && null != configDo.getCarryToType() && configDo.getCarryToType() == 2;
                if (!carryMerge) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_TYPE_NOT_ALLOWED, null).getMsg(), dto.getRowNum(), dto.getQuotaName()));
                }
                Long lastDate = DateUtil.getTimesampByDateStr2(dto.getRetainValidDate()) + 86399;
                Integer periodYear = ConvertHelper.intConvert(dto.getPeriodYear());
                List<LeaveQuotaDo> mergeQuotas = quotaMapper.getNotCarryAnnualLeaveList(userInfo.getTenantId(), null, empId, leaveType.getLeaveTypeId(),
                        configDo.getConfigId(), leaveType.getQuotaType(), periodYear);
                if (CollectionUtils.isEmpty(mergeQuotas)) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_CAN_NOT_CARRY, null).getMsg(), dto.getRowNum(), periodYear, dto.getQuotaName()));
                }
                LeaveQuotaDo mergeEmpQuota = mergeQuotas.get(0);
                if (CollectionUtils.isNotEmpty(quotaMapper.queryAnnualLeaveRetainQuota(tenantId, ConvertHelper.intConvert(mergeEmpQuota.getEmpQuotaId())))) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_QUOTA_CARRY_ALREADY_EXIST, null).getMsg(), dto.getRowNum(), dto.getQuotaName(), periodYear));
                }
                if (lastDate > mergeEmpQuota.getLastDate()) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_QUOTA_RETAIN_VALIDATE_GREATER_THAN_TO, null).getMsg(), dto.getRowNum(), periodYear, dto.getQuotaName()));
                }
                if (lastDate < mergeEmpQuota.getStartDate()) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_VALID_DATE_LESS_THAN_START_DATE, null).getMsg(), dto.getRowNum(), DateUtil.getDateStrByTimesamp(lastDate), DateUtil.getDateStrByTimesamp(mergeEmpQuota.getStartDate())));
                }
                BigDecimal retainDay = new BigDecimal(dto.getRetainDay());
                Integer acctTimeType = (Integer) leaveType.getAcctTimeType();
                //单位为小时
                if (LeaveTypeUnitEnum.HOUR.getIndex().equals(acctTimeType)) {
                    retainDay = retainDay.multiply(BigDecimal.valueOf(60));
                }
                //记录源配额ID
                List<WaEmpQuotaDo> expiredNotCarryAnnual = quotaMapper.getExpiredNotCarryAnnualLeaveList(tenantId, empId, leaveType.getLeaveTypeId(), configDo.getConfigId(), periodYear - 1, DateUtil.getCurrentTime(true));
                if (CollectionUtils.isEmpty(expiredNotCarryAnnual)) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_QUOTA_SOURCE_NOT_EXIST, null).getMsg(), dto.getRowNum(), periodYear - 1, dto.getQuotaName()));
                }
                WaEmpQuotaDo originalEmpQuota = expiredNotCarryAnnual.get(0);
                if (originalEmpQuota.getIfCarryForward() != null && originalEmpQuota.getIfCarryForward()) {
                    return Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_EMP_QUOTA_CARRY_ALREADY_EXIST, null).getMsg(), dto.getRowNum(), dto.getQuotaName(), periodYear));
                }
                WaEmpQuota carryQuota = new WaEmpQuota();
                carryQuota.setBelongOrgId(tenantId);
                carryQuota.setQuotaDay(retainDay.floatValue());
                carryQuota.setNowQuota(retainDay.floatValue());
                carryQuota.setRemarks(Optional.ofNullable(dto.getRemark()).orElse(""));
                carryQuota.setUpduser(userInfo.getUserId());
                carryQuota.setUpdtime(DateUtil.getCurrentTime(true));
                carryQuota.setLastDate(lastDate);
                carryQuota.setStartDate(mergeEmpQuota.getStartDate());
                carryQuota.setEmpid(empId);
                carryQuota.setFixUsedDay(0f);
                carryQuota.setUsedDay(0f);
                carryQuota.setAdjustQuota(0f);
                carryQuota.setIfAdvance(1);
                carryQuota.setRemainDay(0f);
                carryQuota.setCrttime(DateUtil.getCurrentTime(true));
                carryQuota.setCrtuser(userInfo.getUserId());
                carryQuota.setLeaveTypeId(leaveType.getLeaveTypeId());
                carryQuota.setConfigId(configDo.getConfigId());
                carryQuota.setCarryMerge(true);
                carryQuota.setValidityDuration(configDo.getCarryOverValidityDuration());
                carryQuota.setValidityUnit(configDo.getCarryOverValidityUnit());
                carryQuota.setPeriodYear(periodYear.shortValue());
                carryQuota.setDisCycleStart(mergeEmpQuota.getDisCycleStart());
                carryQuota.setDisCycleEnd(mergeEmpQuota.getDisCycleEnd());
                carryQuota.setMergeEmpQuotaId(ConvertHelper.intConvert(mergeEmpQuota.getEmpQuotaId()));
                carryQuota.setOriginalEmpQuotaId(originalEmpQuota.getEmpQuotaId());
                addList.add(carryQuota);
                WaEmpQuota originalQuota = new WaEmpQuota();
                originalQuota.setEmpQuotaId(originalEmpQuota.getEmpQuotaId());
                originalQuota.setRemainDay(carryQuota.getQuotaDay());
                originalQuota.setUpdtime(DateUtil.getCurrentTime(true));
                originalQuota.setUpduser(userInfo.getUserId());
                originalQuota.setIfCarryForward(true);
                updList.add(originalQuota);
            }
            if (CollectionUtils.isNotEmpty(addList)) {
                importService.fastInsertList(WaEmpQuota.class, "empQuotaId", addList);
            }
            if (CollectionUtils.isNotEmpty(updList)) {
                for (WaEmpQuota quota : updList) {
                    waEmpQuotaMapper.updateByPrimaryKeySelective(quota);
                }
            }
        } catch (CDException cde) {
            log.error("Parse file error {}", cde.getMessage(), cde);
            return Result.fail(cde.getMessage());
        } catch (FileNotFoundException fne) {
            log.error("File not exists {}", fne.getMessage(), fne);
            return Result.fail("File not exists");
        } catch (IOException ie) {
            log.error("File parsing failed {}", ie.getMessage(), ie);
            return Result.fail("File parsing failed");
        } catch (Exception e) {
            log.error("An exception occurred while importing the file, {}", e.getMessage(), e);
            return Result.fail("An exception occurred while importing the file");
        }
        return Result.ok("success");
    }

    /**
     * 校验必填项
     *
     * @param workNo     工号
     * @param leaveName  假期名称
     * @param quotaName  额度名称
     * @param periodYear 年份
     * @param retainDay  留存额度
     * @param validDate  留存有效期
     * @param rowNum     行数
     * @return
     */
    private String checkCellValue(String workNo, String leaveName, String quotaName, String periodYear, String retainDay, String validDate, Integer rowNum) {
        //账号不能为空
        if (StringUtils.isBlank(workNo)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_FILE_WORK_NO_EMPTY, null).getMsg(), rowNum);
        }
        //假期名称不能为空
        if (StringUtils.isBlank(leaveName)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_LEAVE_NAME_EMPTY, null).getMsg(), rowNum);
        }
        //额度名称不能为空
        if (StringUtils.isBlank(quotaName)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_NAME_EMPTY, null).getMsg(), rowNum);
        }
        //年份不能为空
        if (StringUtils.isBlank(periodYear)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_PERIOD_YEAR_EMPTY, null).getMsg(), rowNum);
        } else {
            boolean flag = NumberUtils.isCreatable(periodYear);
            if (!flag) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_PERIOD_YEAR_FORMAT_ERR, null).getMsg(), rowNum);
            }
        }
        //上年留存不能为空
        if (StringUtils.isBlank(retainDay)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_EMPTY, null).getMsg(), rowNum);
        } else {
            boolean flag = NumberUtils.isCreatable(retainDay);
            if (!flag) {
                return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_QUOTA_FORMAT_ERR, null).getMsg(), rowNum);
            }
        }
        //留存有效期至不能为空
        if (StringUtils.isBlank(validDate)) {
            return String.format(ResponseWrap.wrapResult(AttendanceCodes.IMPORT_RETAIN_VALID_DATE_EMPTY, null).getMsg(), rowNum);
        }
        return "";
    }

    private String getCellDateString(Cell dateCell, String pattern) {
        String datePatternStr = "";
        if (dateCell != null) {
            short dataFormat = dateCell.getCellStyle().getDataFormat();
            SimpleDateFormat sdf = new SimpleDateFormat(pattern);
            if (dataFormat == 14 || dataFormat == 31 || dataFormat == 57 || dataFormat == 58) {
                if (dateCell.getCellType() == CellType.STRING) {
                    String cellValue = dateCell.getStringCellValue().trim();
                    if (StringUtils.isNotBlank(cellValue)) {
                        long value = 0;
                        if (cellValue.contains("-")) {
                            value = DateUtil.convertStringToDateTime(cellValue, "yyyy-MM-dd", true);
                        } else if (cellValue.contains("/")) {
                            value = DateUtil.convertStringToDateTime(cellValue, "yyyy/MM/dd", true);
                        }
                        datePatternStr = DateUtil.getDateStrByTimesamp(value);
                    }
                } else if (dateCell.getCellType() == CellType.NUMERIC) {
                    double value = dateCell.getNumericCellValue();
                    Date date = HSSFDateUtil.getJavaDate(value);
                    datePatternStr = sdf.format(date);
                } else {
                    if (dateCell.toString().contains("-") && checkDate(dateCell.toString())) {
                        datePatternStr = sdf.format(dateCell.getDateCellValue());
                    }
                }
            } else if (HSSFDateUtil.isCellDateFormatted(dateCell) && dataFormat == HSSFDataFormat.getBuiltinFormat(pattern)) {
                Date date = dateCell.getDateCellValue();
                datePatternStr = sdf.format(date);
            } else {
                dateCell.setCellType(CellType.STRING);
                datePatternStr = dateCell.getStringCellValue().trim();
            }
        }
        return datePatternStr;
    }

    private boolean checkDate(String str) {
        String[] dataArr = str.split("-");
        try {
            if (dataArr.length == 3) {
                int day = Integer.parseInt(dataArr[0]);
                String month = dataArr[1];
                int year = Integer.parseInt(dataArr[2]);
                if (day > 0 && day < 32 && year > 0 && year < 10000 && month.endsWith("月")) {
                    return true;
                }
            }
        } catch (Exception e) {
            return false;
        }
        return false;
    }
}
