package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.service.domain.entity.EmpCompensatoryQuotaDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCase;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryQuotaPo;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/7/27
 */
public interface IEmpCompensatoryQuotaRepository {
    int save(EmpCompensatoryQuotaDo quotaDo);

    int update(EmpCompensatoryQuotaDo quotaDo);

    PageList<EmpCompensatoryQuotaDo> getEmpCompensatoryQuotaList(CompensatoryQuotaSearchDto dto, String tenantId);

    List<EmpCompensatoryQuotaDo> getQuotaList(String tenantId, List<Integer> overtimeDetailIds);

    int save(List<EmpCompensatoryQuotaDo> list);

    EmpCompensatoryQuotaDo getByQuotaId(Long quotaId);

    void delete(Long quotaId);

    void deleteByIds(String tenantId, List<Long> quotaIds);

    void batchUpdate(List<WaEmpCompensatoryQuotaPo> models);

    List<Map> queryEmpQuotaList(Map params);

    List<WaEmpCompensatoryQuotaPo> getQuotaListByIds(String tenantId, List<Long> quotaIds);

    int updateByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId, EmpCompensatoryQuotaDo quotaDo);

    List<WaEmpCompensatoryQuotaPo> getQuotaListByEmpIdAndLeaveType(String tenantId, Long empId, Integer leaveTypeId);

    List<EmpCompensatoryQuotaDo> getQuotaListByDate(String tenantId, List<Long> empIds, Long startDate, Long endDate, String dataSource, List<Integer> status);

    WaEmpCompensatoryQuotaPo getByOverTimeDate(String tenantId, Long empId, Long overTimeDate);

    List<EmpCompensatoryQuotaDo> getEmpQuotas(EmpCompensatoryQuotaDo dto);

    int updateWaEmpCompensatoryQuota(Long userId, Long currentTime, List<Long> idList, Integer status);

    List<WaEmpCompensatoryQuotaPo> getApplyCompensatoryQuotaList(String tenantId, List<Long> quotaIds, Integer status, Long currentTime);

    List<WaEmpCompensatoryQuotaPo> getEmpCompensatoryQuotaList(List<Long> quotaIds);

    List<WaEmpCompensatoryQuotaPo> groupCompensatoryQuotaByEmpIdsAndTime(String tenantId, List<String> empIds, long startTime, long endTime);
}
