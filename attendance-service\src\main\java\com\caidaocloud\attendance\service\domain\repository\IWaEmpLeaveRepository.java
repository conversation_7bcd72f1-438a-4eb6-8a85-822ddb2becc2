package com.caidaocloud.attendance.service.domain.repository;

import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.service.domain.entity.WaEmpLeaveDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveDaytimeDo;
import com.caidaocloud.dto.BasePage;
import com.caidaocloud.dto.PageResult;

import java.util.List;

public interface IWaEmpLeaveRepository {
    PageResult<WaEmpLeaveDo> getEmpLeavePageList(BasePage basePage, String belongOrgId, Long empId, Integer wfFuncId, Integer status, String keywords);

    List<WaLeaveDaytimeDo> getEmpLeaveDaytimeList(Long empid, Long daytime, List<Integer> statusList);

    List<WaLeaveDaytimeDo> getEmpLeaveDay(Long empid, Long startDate, Long endDate, List<Integer> statusList);

    List<WaLeaveDaytimeDo> getLeaveDayTimeByDay(Integer leaveId, Long date);

    int updateDayTime(WaLeaveDaytimeDo waLeaveDaytimeDo);

    PageResult<WaLeaveDaytimeDo> selectDayTimePage(AttendanceBasePage basePage, Long startTime, Long endTime, List<Integer> statusList);
}
