package com.caidaocloud.attendance.core.wa.dto;

import lombok.Data;

@Data
public class EmpLeaveInfo {

	private Integer leave_id;
	private Long empid;
	private String time_slot;
	private Float total_time_duration;
	private Integer leave_type_id;
	private Long leave_date;
	private Integer start_time;
	private Integer end_time;
	private Float time_duration;
	private Short period_type;
	private Integer date_type;
	private Integer time_unit;
	private Long real_date;
	private String shalf_day;
	private String ehalf_day;
	private Integer province;
	private Integer city;
	private Integer leave_type;
	private Boolean link_outside_sign;
	private Float before_adjust_time_duration;
	private String leave_type_def_code;
	private Boolean is_rest_day;
	private Boolean is_legal_holiday;

	/**
	 * 每天的请假对应的班次信息
	 */
	private EmpShiftInfo empShiftInfo;

	private Boolean isFlexibleWorking;

	private Long createTime;

	private Integer status;
	private Integer useShiftDefId;
}
