package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.application.enums.ShiftBelongModuleEnum;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaShiftPo;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;

import java.util.List;

public interface IWaShiftRepository {

    void save(WaShiftPo shift, String tenantId);

    void saveList(List<WaShiftPo> list, String tenantId);

    WaShiftPo selectWaShiftPoByDate(String belongOrgid, Long empId, Long workDate, String tenantId);

    AttendancePageResult<WaShiftDo> getShiftDefList(ShiftPageDto shiftPageDto);

    List<WaShiftDo> selectListByIds(List<Integer> shiftDefIds);

    void deleteShift(String belongOrgid, String tenantId);

    WaShiftPo getShiftById(Integer shiftDefId);

    int saveOrUpdateWaShiftDef(WaShiftDef waShiftDef);

    List<WaShiftDo> getWaShiftDefList(String belongOrgId, ShiftBelongModuleEnum belongModule);

    boolean checkShiftDefReCode(String tenantId, String defCode, Integer shiftDefId, String belongModule);

    boolean checkShiftDefReName(String tenantId, String defName, Integer shiftDefId, String belongModule);

    List<WaShiftDo> getWaShiftDefList(String tenantId, List<Integer> shiftIds);

    List<WaShiftDo> selectListByStTime(String tenantId, ShiftBelongModuleEnum belongModule,
                                       Boolean temporaryShift, Integer startTime, Integer endTime,
                                       Integer startTimeBelong, Integer endTimeBelong);
}
