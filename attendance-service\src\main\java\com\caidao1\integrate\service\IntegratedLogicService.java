package com.caidao1.integrate.service;

import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.script.GroovyScriptEngine;
import com.caidao1.commons.utils.*;
import com.caidao1.integrate.bean.DatabaseDefineBean;
import com.caidao1.integrate.bean.TableConfigDto;
import com.caidao1.integrate.datasource.CusDataSource;
import com.caidao1.integrate.entity.GaiYa.GyReadSource;
import com.caidao1.integrate.entity.TieTong.DoorSourceInput;
import com.caidao1.integrate.entity.TieTong.IsoTimeUtil;
import com.caidao1.integrate.entity.dto.DoorDetail;
import com.caidao1.integrate.entity.dto.RecordDto;
import com.caidao1.integrate.mybatis.mapper.IntegrateMapper;
import com.caidao1.integrate.mybatis.mapper.SysDataInputMapper;
import com.caidao1.integrate.mybatis.mapper.SysDataOutputMapper;
import com.caidao1.integrate.mybatis.model.*;
import com.caidao1.integrate.reader.SourceReader;
import com.caidao1.integrate.util.IntegrateUtil;
import com.caidao1.ioc.mybatis.mapper.ImportOtherMapper;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.MapUtil;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.imports.service.application.interceptor.writer.OutputWriter;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.dto.*;
import com.caidao1.ioc.mybatis.mapper.IocImportMapper;
import com.caidao1.ioc.service.ImportDatasService;
import com.caidao1.ioc.util.IocEnum;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.report.mybatis.mapper.ReportConfigMapper;
import com.caidao1.system.mybatis.model.SysParmDict;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.imports.service.application.service.ioc.ImportCacheService;
import com.caidaocloud.util.FastjsonUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.googlecode.totallylazy.Maps;
import com.weibo.api.motan.core.extension.ExtensionLoader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;
import java.io.IOException;
import java.text.MessageFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Created by darren on 16/4/26.
 */
@Slf4j
@Service
public class IntegratedLogicService {
    @Autowired
    private CusDataSource cusDataSource;
    @Autowired
    private NamedParameterJdbcTemplate cusJdbcTemplate;
    @Autowired
    private IntegrateMapper integrateMapper;
    @Autowired
    private ReportConfigMapper reportConfigMapper;
    @Autowired
    private SysDataInputMapper dataInputMapper;
    @Autowired
    private SysDataOutputMapper dataOutputMapper;
    @Autowired
    private IocImportMapper iocImportMapper;
    @Autowired
    private RemoteImportService importService;
    @Autowired
    private ImportCacheService importCacheService;
    @Autowired
    private RedisService redisService;
    @Autowired
    private GroovyScriptEngine groovyScriptEngine;
    @Autowired
    private ImportDatasService importDatasService;
    @Resource
    private ImportOtherMapper importOtherMapper;
    @Resource
    private GyReadSource gyReadSource;

    public static final String PARAM_ROWMIN = ":ROWMIN@([^@]+)@+?";
    public static final String PARAM_ROWMAX = ":ROWMAX@([^@]+)@+?";

    private static ConcurrentHashMap<String, Object> checkCache = new ConcurrentHashMap<String, Object>();

    private String formatRowMinMaxKey(String exp, String table, List<Map<String, Object>> convertRows, Map<String, String> typeMap) {
        Pattern pattern = Pattern.compile(PARAM_ROWMIN, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(exp);
        boolean searchMinMax = false;
        Long min = DateUtil.MAX_DATE;
        Long max = 0l;
        while (matcher.find()) {
            for (int rowCnt = 0; rowCnt < convertRows.size(); rowCnt++) {
                Map<String, Object> row = convertRows.get(rowCnt);
                Long itemValue = (Long) IocUtil.getDateValue(row.get(matcher.group(1)), typeMap.get(table + "_" + matcher.group(1)));
                min = Math.min(min, itemValue);
                max = Math.max(max, itemValue);
            }
            searchMinMax = true;

            exp = exp.replace(matcher.group(0), min.toString());
        }
        pattern = Pattern.compile(PARAM_ROWMAX, Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(exp);
        while (matcher.find()) {
            if (!searchMinMax) {
                for (int rowCnt = 0; rowCnt < convertRows.size(); rowCnt++) {
                    Map<String, Object> row = convertRows.get(rowCnt);
                    Long itemValue = (Long) IocUtil.getDateValue(row.get(matcher.group(1)), typeMap.get(matcher.group(1)));
                    min = Math.min(min, itemValue);
                    max = Math.max(max, itemValue);
                }
            }
            exp = exp.replace(matcher.group(0), max.toString());
        }
        return exp;
    }

    public Map<String, Integer> getDbKeyCache(String[] tableExp, Long corpId, String belongId, Long now, List<Map<String, Object>> rows, Map<String, String> typeMap) {
        List<String> keyList = IocUtil.getKeyListFromSql(tableExp[1], "");
        String idName = keyList.get(keyList.size() - 1);

        String idSql = IocUtil.formatExp(corpId, belongId, tableExp[1], now);
        idSql = formatRowMinMaxKey(idSql, tableExp[0], rows, typeMap);

        List<Map<String, Object>> cacheList = iocImportMapper.queryKeyListBySql(idSql);
        Map<String, Integer> keyCacheMap = new HashMap<String, Integer>();
        if (CollectionUtils.isNotEmpty(cacheList)) {
            for (Map cache : cacheList) {
                String key = tableExp[0] + "#";
                for (String item : keyList) {
                    if (!item.equals(idName)) {
                        key += "_";
                        if (cache.containsKey(item)) {
                            key += cache.get(item);
                        }
                    }
                }
                keyCacheMap.put(key, (Integer) cache.get(idName));
            }
        }
        return keyCacheMap;
    }

    /**
     * 数据流入接口
     *
     * @throws Exception
     */
    public List<Map<String, Object>> getDataInputSourceResult(Integer id, Long corpId, String belongId, List<String> logList, Map exparams, Map returnMap) throws Exception {
        SysDataInput dataInput = dataInputMapper.selectByPrimaryKey(id);
        PGobject pgobject = (PGobject) dataInput.getSourceConfig();
        List<Map<String, Object>> sourceResult = new ArrayList<>();
        if (pgobject != null) {
            String jsonStr = pgobject.getValue();
            if (StringUtils.isNotEmpty(jsonStr)) {
                Map<String, Object> mapJson = (Map<String, Object>) JacksonJsonUtil.jsonToBean(jsonStr, Map.class);

                ExtensionLoader<SourceReader> extensionLoader = ExtensionLoader.getExtensionLoader(SourceReader.class);
                SourceReader workflowService = extensionLoader.getExtension(dataInput.getSourceType());
                sourceResult = workflowService.getSourceResult(corpId, belongId, dataInput, mapJson, exparams, logList, returnMap);
                logList.add("取得数据条数" + sourceResult.size());
            }
        }
        return sourceResult;
    }

    public CheckRuleDto empCheckRule(){
        CheckRuleDto checkRuleDto = new CheckRuleDto();
        checkRuleDto.setCheckExp(":EMPID");
        checkRuleDto.setCheckPattern("FK");
        checkRuleDto.setFieldCode("empid");
        checkRuleDto.setMessage("工号{0}不存在");
        return checkRuleDto;
    }

    private List<String> getZkSnList(String urlAndPost, String secretKey) throws Exception {
        HttpResponse response = null;
        try (CloseableHttpClient http = HttpClients.createDefault()) {
            HttpPost httppost = new HttpPost(urlAndPost + "/api/v2/iclock/get/?key=" + secretKey);
            Map map = new HashMap<>();
            StringEntity body = new StringEntity(FastjsonUtil.toJson(map), "UTF-8");
            body.setContentType("application/json");
            httppost.setEntity(body);
            response = http.execute(httppost);
            if (null != response && response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String content = EntityUtils.toString(response.getEntity(), "UTF-8");
                Map result = new ObjectMapper().readValue(content, Map.class);
                log.info("api={} RequestResult:{}", httppost.getURI().getPath(), result);
                List<Map<String, Object>> snList = (List<Map<String, Object>>) MapUtil.getValue(result, "data.items");
                return snList.stream().map(item -> (String) item.get("sn")).collect(Collectors.toList());
            }
        }
        return null;
    }

    public List<RecordDto> getKqjRecord(Long startDate, Long endDate, String workNoFilter, Boolean haveQuitEmp, String belongId, Map<Long, Integer> empStatsMap) {
        try {
            SysDataInputExample example = new SysDataInputExample();
            example.createCriteria().andBelongOrgIdEqualTo(belongId)
                    .andNoteEqualTo("远景考勤机");
            List<SysDataInput> sysDataInputs = dataInputMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(sysDataInputs)) {
                Map returnMap = Maps.map();
                List<RecordDto> recordDtoList = new ArrayList<>();
                List<Map<String, Object>> sourceResult = null;
                SysDataInput dataInput = sysDataInputs.get(0);
                PGobject pgobject = (PGobject) dataInput.getSourceConfig();
                Map<String, Object> mapJson = (Map<String, Object>) JacksonJsonUtil.jsonToBean(pgobject.getValue(), Map.class);
                List<Map> paramsConfig = (List<Map>) mapJson.get("params");
                if (CollectionUtils.isNotEmpty(paramsConfig)) {
                    if (StringUtils.isNotBlank(workNoFilter)) {
                        Map pinFilter = new HashMap<>();
                        pinFilter.put("name", "pin");
                        pinFilter.put("value", workNoFilter);
                        paramsConfig.add(pinFilter);
                    }
                    Boolean nullSnFilter = false;
                    for (Map param : paramsConfig) {
                        String name = param.get("name").toString();
                        if ("starttime".equals(name)) {
                            param.put("value", DateUtil.convertDateTimeToStr(startDate, "yyyy-MM-dd 00:00:00", true));
                            param.remove("format");
                        }
                        if ("endtime".equals(name)) {
                            param.put("value", DateUtil.convertDateTimeToStr(endDate, "yyyy-MM-dd 23:59:59", true));
                            param.remove("format");
                        }
                    }
                    for (Map param : paramsConfig) {
                        String name = param.get("name").toString();
                        String value = param.get("value").toString();
                        if ("sn".equals(name)) {
                            if (StringUtils.isBlank(value)) {
                                nullSnFilter = true;
                            } else {
                                sourceResult = getDataInputSourceResult(dataInput.getSysDataInputId(), Long.valueOf(belongId), belongId, Lists.newArrayList(), null, returnMap);
                            }
                            break;
                        }
                    }
                    if (nullSnFilter) {
                        List<String> zkSnList = getZkSnList(mapJson.get("urlAndPost").toString(), mapJson.get("secretKey").toString());
                        for (String sn : zkSnList) {
                            for (Map param : paramsConfig) {
                                String name = param.get("name").toString();
                                if ("sn".equals(name)) {
                                    param.put("value", sn);
                                    break;
                                }
                            }
                            log.info("远景考勤机循环获取设备号打卡信息:{}", paramsConfig);
                            sourceResult = Optional.ofNullable(sourceResult).orElse(new ArrayList<>());

                            ExtensionLoader<SourceReader> extensionLoader = ExtensionLoader.getExtensionLoader(SourceReader.class);
                            SourceReader workflowService = extensionLoader.getExtension(dataInput.getSourceType());
                            List<Map<String, Object>> snResult = workflowService.getSourceResult(Long.valueOf(belongId), belongId, dataInput, mapJson, null, Lists.newArrayList(), returnMap);
                            if (snResult != null) {
                                sourceResult.addAll(snResult);
                            }
                        }
                    }
                    if(CollectionUtils.isEmpty(sourceResult)){
                        return recordDtoList;
                    }
                    Jedis jedis = RedisService.getResource();
                    try {
                        for (Map<String, Object> map : sourceResult) {
                            String key = BaseConst.EMP_ + belongId + "_" + map.get("pin");
                            String empInfo = jedis.get(key);
                            if (empInfo == null) {
                                continue;
                            }
                            Long empId = Long.parseLong(String.valueOf(empInfo.split(",")[0]).replace("\"", ""));
                            if(!haveQuitEmp){
                                if(empStatsMap.get(empId) == 1){
                                    continue;
                                }
                            }
                            String workNo = map.get("empid").toString();
                            String checkTime = map.get("checktime").toString();
                            Long regTime = DateUtil.convertStringToDateTime(checkTime, "yyyy-MM-dd HH:mm:ss", true);
                            RecordDto recordDto = new RecordDto();
                            recordDto.setEmpId(empId);
                            recordDto.setWorkNo(workNo);
                            recordDto.setName(empInfo.split(",")[1]);
                            recordDto.setRegisterTime(regTime);
                            recordDto.setRegTimeStr(checkTime);
                            recordDto.setDeviceNumber(map.get("sn").toString());
                            recordDtoList.add(recordDto);
                        }
                    } finally {
                        jedis.close();
                    }
                    return recordDtoList;
                }
            }
        } catch (Exception e) {
            log.error("getKqjRecordErr:{}", e.getMessage(), e);
        }
        return Lists.newArrayList();
    }


    public List<RecordDto> getGyRecord(Long startDate, Long endDate, String workNoFilter, Boolean haveQuitEmp, String belongId, Map<Long, Integer> empStatsMap) {
        try {
            SysDataInputExample example = new SysDataInputExample();
            example.createCriteria().andBelongOrgIdEqualTo(belongId)
                    .andNoteEqualTo("盖亚打卡记录接入");
            List<SysDataInput> sysDataInputs = dataInputMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(sysDataInputs)) {
                SysDataInput dataInput = sysDataInputs.get(0);
                PGobject pgobject = (PGobject) dataInput.getSourceConfig();
                Map<String, Object> apiConfig = (Map<String, Object>) JacksonJsonUtil.jsonToBean(pgobject.getValue(), Map.class);

                Map queryDbParam = (HashMap) apiConfig.get("readDbParam");
                String sqlFilter = (String) queryDbParam.get("sqlFilter");
                sqlFilter = sqlFilter.replaceAll("昨天", DateUtil.convertDateTimeToStr(startDate, "yyyy-MM-dd", true))
                        .replaceAll("今天", DateUtil.convertDateTimeToStr(endDate, "yyyy-MM-dd", true));
                queryDbParam.put("sqlFilter", sqlFilter);
                if (workNoFilter != null) {
                    queryDbParam.put("workNoList", workNoFilter);
                }
                List<RecordDto> recordDtoList = new ArrayList<>();
                List<Map<String, Object>> sourceResult = new GyReadSource().getGyOtReadSourceResult(apiConfig, belongId);
                for (Map<String, Object> map : sourceResult) {
                    String workno = map.get("EMPLOYEEID").toString();
                    String regTime = map.get("regTime").toString();
                    RecordDto recordDto = new RecordDto();
                    recordDto.setWorkNo(workno);
                    recordDto.setName(map.get("name").toString());
                    Long empId = (Long) map.get("empId");
                    recordDto.setEmpId(empId);
                    if(!haveQuitEmp){
                        if(empStatsMap.get(empId) == 1){
                            continue;
                        }
                    }
                    recordDto.setRegisterTime(DateUtil.convertStringToDateTime(regTime, "yyyy-MM-dd HH:mm:ss", true));
                    recordDto.setRegTimeStr(regTime);
                    recordDto.setDeviceNumber(map.get("MACHINECODE").toString());
                    recordDtoList.add(recordDto);
                }
                return recordDtoList;
            }
        } catch (Exception e) {
            log.error("getKqjRecordErr:{}", e.getMessage(),e);
        }
        return Lists.newArrayList();
    }

    /**
     * 数据流入接口
     *
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public LogDataInput execDataInput(LogDataInput dataInputLog, SysDataInput dataInput, Long corpId, String belongId, boolean clearCache, Map exparams) throws
            Exception {
        PGobject pgobject = (PGobject) dataInput.getSourceConfig();
        if (pgobject != null) {
            Map<String, String> typeMap = null;
            List<Map<String, Object>> sourceAllResult = new ArrayList<>();
            List<Map<String, Object>> sourceResult = null;
            List<String> logList = new ArrayList<>();
            List<Map> checkList = new ArrayList<>();
            Map returnMap = new HashMap();//获取数据的时候返回操作结果
            String jsonStr = pgobject.getValue();
            if (StringUtils.isNotEmpty(jsonStr)) {
                Map<String, Object> mapJson = (Map<String, Object>) JacksonJsonUtil.jsonToBean(jsonStr, Map.class);

                Boolean flushEmpCache = false;
                //数据强制更新配置，默认强制更新  Forced update of data
                Boolean forcedupdate = true;
                String forcedupdateKey = "data.forcedupdate";
                if (mapJson.containsKey(forcedupdateKey) && mapJson.get(forcedupdateKey) != null) {
                    forcedupdate = Boolean.valueOf(mapJson.get(forcedupdateKey).toString());
                }
                dataInputLog.setStartTime(DateUtil.getCurrentTime(true));
                Boolean tieTongAttendanceInPut = dataInput.getNote().startsWith("铁通门禁数据接入");
                Boolean yuanJingAttendanceInPut = dataInput.getNote().startsWith("远景考勤机");
                Boolean gaiYaAttendanceRecordInPut = dataInput.getNote().startsWith("盖亚打卡记录");
                Boolean gaiYaAttendanceOtInPut = dataInput.getNote().startsWith("盖亚加班");
                Boolean gaiYaAttendanceLeaveInPut = dataInput.getNote().startsWith("盖亚请假");
                log.info("gaiYaAttendanceRecordInPut:{}", gaiYaAttendanceRecordInPut);
                log.info("gaiYaAttendanceOtInPut:{}", gaiYaAttendanceOtInPut);
                log.info("gaiYaAttendanceLeaveInPut:{}", gaiYaAttendanceLeaveInPut);
                if (tieTongAttendanceInPut) {
                    sourceResult = dataInputTieTongDoorRecord(belongId,
                            (String) mapJson.get("startDay"),
                            (String) mapJson.get("endDay"),
                            mapJson.getOrDefault("filterWorkNo", "").toString(),
                            mapJson.getOrDefault("accessDateDuration", "").toString(),
                            mapJson.getOrDefault("excludedAddress", "").toString());
                } else if (yuanJingAttendanceInPut) {
                    List<Map> paramsConfig = (List<Map>) mapJson.get("params");
                    if (CollectionUtils.isNotEmpty(paramsConfig)) {
                        Boolean nullSnFilter = false;
                        for (Map param : paramsConfig) {
                            String name = param.get("name").toString();
                            String value = param.get("value").toString();
                            if ("sn".equals(name)) {
                                if (StringUtils.isBlank(value)) {
                                    nullSnFilter = true;
                                } else {
                                    sourceResult = getDataInputSourceResult(dataInput.getSysDataInputId(), corpId, belongId, logList, exparams, returnMap);
                                }
                                break;
                            }
                        }
                        if (nullSnFilter) {
                            List<String> zkSnList = getZkSnList(mapJson.get("urlAndPost").toString(), mapJson.get("secretKey").toString());
                            for (String sn : zkSnList) {
                                for (Map param : paramsConfig) {
                                    String name = param.get("name").toString();
                                    if ("sn".equals(name)) {
                                        param.put("value", sn);
                                        break;
                                    }
                                }
                                log.info("远景考勤机循环获取设备号打卡信息:{}", paramsConfig);
                                sourceResult = Optional.ofNullable(sourceResult).orElse(new ArrayList<>());

                                ExtensionLoader<SourceReader> extensionLoader = ExtensionLoader.getExtensionLoader(SourceReader.class);
                                SourceReader workflowService = extensionLoader.getExtension(dataInput.getSourceType());
                                List<Map<String, Object>> snResult = workflowService.getSourceResult(corpId, belongId, dataInput, mapJson, exparams, logList, returnMap);
                                if(snResult != null){
                                    sourceResult.addAll(snResult);
                                }
                                logList.add("取得数据条数sn:" + sn + "条数:" + sourceResult.size());
                            }
                        }
                    }
                    // 获取设备号列表 循环替换参数设备号读取数据
                } else if (gaiYaAttendanceOtInPut || gaiYaAttendanceRecordInPut) {
                    log.info("flushEmpCache1..");
                    this.initEmp(dataInput.getBelongOrgId());
                    sourceResult = new GyReadSource().getGyOtReadSourceResult(mapJson, belongId);
                } else if (gaiYaAttendanceLeaveInPut) {
                    log.info("flushEmpCache2..");
                    this.initEmp(dataInput.getBelongOrgId());
                    gyReadSource.syncGyLeaveInput(dataInput, dataInputLog, mapJson);
                    return dataInputLog;
                } else {
                    sourceResult = getDataInputSourceResult(dataInput.getSysDataInputId(), corpId, belongId, logList, exparams, returnMap);
                }
                logList.add("取得数据条数:" + (sourceResult == null ? 0 : sourceResult.size()));
                if (CollectionUtils.isNotEmpty(sourceResult)) {
                    log.info("dataInputFlushEmpCache..");
                    this.initEmp(dataInput.getBelongOrgId());

                    Map<String, List<Map>> fkInfoMap = new HashMap<>();

                    SysDataInput upd = new SysDataInput();
                    upd.setSysDataInputId(dataInput.getSysDataInputId());
                    upd.setWorkTime(DateUtil.getCurrentTime(true));
                    dataInputMapper.updateByPrimaryKeySelective(upd);

                    if (Optional.ofNullable(dataInput.getSourceSave()).orElse(false)) {
                        dataInputLog.setSourceResult(new ObjectMapper().writeValueAsString(sourceResult));
                    }

                    if (CollectionUtils.isNotEmpty(sourceResult) && (Boolean) mapJson.getOrDefault("parseSourceResult", false)) {
                        sourceResult = IntegrateUtil.parseSourceResultSourceExp(dataInput, Lists.newArrayList(), sourceResult);
                    }

                    if (StringUtils.isNotEmpty(dataInput.getBeforeTrigger())) {
                        Map<String, Object> binding = new HashMap<String, Object>();
                        binding.put("belongId", belongId);
                        binding.put("sourceResult", sourceResult);
                        binding.put("corpId", corpId);
                        binding.put("checkList", checkList);
                        binding.put("dataInput", dataInput);
                        binding.put("fkInfoMap", fkInfoMap);
                        sourceResult = (List<Map<String, Object>>) groovyScriptEngine.executeObject(dataInput.getBeforeTrigger(), binding);
                        logList.add("处理后数据条数" + sourceResult.size());
                    }
                    sourceAllResult.addAll(sourceResult);
                    Map<String, List<UpdRowDto>> addList = new HashMap<String, List<UpdRowDto>>();
                    Map<String, List<UpdRowDto>> updList = new HashMap<String, List<UpdRowDto>>();

                    List<Map<String, Object>> successResult = new ArrayList<Map<String, Object>>();
                    successResult.addAll(sourceResult);

                    Map<String, TableConfigDto> tableConfigMap = new HashMap<String, TableConfigDto>();

                    Map<String, List<CheckRuleDto>> checkRuleMap = new HashMap<String, List<CheckRuleDto>>();
                    typeMap = new HashMap<>();

                    if (clearCache) {
                        checkCache.clear();
                        importCacheService.empStrucList.clear();
                    }

                    if ("db".equalsIgnoreCase(dataInput.getTargetType())) {
                        String[] colList = dataInput.getTargetCol().split(";\r?\n?");
                        String[] expList = dataInput.getTargetExp().split(";\r?\n?");

                        //先校验
                        for (int i = 0; i < expList.length; i++) {
                            String exp = expList[i];
                            String[] tableExp = exp.split("#");
                            String[] cols = colList[i].split(",");
                            Map<String, Integer> fieldIdxMap = new HashMap<>();
                            int idx = 0;
                            for (String col : cols) {
                                fieldIdxMap.put(col.split("#")[0], idx);
                                idx++;
                            }
                            logList.add("开始校验" + tableExp[0] + "的数据：");

                            //取得校验定义规则
                            List<CheckRuleDto> checkRuleList = integrateMapper.selectCheckRuleListByTable(tableExp[0], belongId);
                            for (CheckRuleDto checkRule : checkRuleList) {
                                List<CheckRuleDto> crList = new ArrayList<CheckRuleDto>();
                                if (checkRuleMap.containsKey(tableExp[0] + "_" + checkRule.getFieldCode())) {
                                    crList = checkRuleMap.get(tableExp[0] + "_" + checkRule.getFieldCode());
                                }
                                crList.add(checkRule);
                                checkRuleMap.put(tableExp[0] + "_" + checkRule.getFieldCode(), crList);
                            }

                            for (int j = 0; j < cols.length; j++) {
                                String[] item = cols[j].split("#");
                                typeMap.put(item[0], item[1]);
                            }

                            Jedis jedis = redisService.getResource();

                            try {
                                Map<String, List<SysParmDict>> systemMap = (Map) SerializeUtil.unserialize(jedis.get(SerializeUtil.serialize("SYS_PARM_DICT_MAP")));
                                Map<String, List<SysParmDict>> map2 = (Map) SerializeUtil.unserialize(jedis.get(SerializeUtil.serialize("SYS_CUST_PARM_DICT_MAP")));
                                List<List<String>> rows = new ArrayList<>();
                                for (Map<String, Object> map : sourceResult) {
                                    List<String> row = new ArrayList<>();
                                    fieldIdxMap.forEach((k, v) -> {
                                        Object data = map.get(k);
                                        row.add(Optional.ofNullable(data).orElse("").toString());
                                    });
                                    rows.add(row);
                                }
                                for (int rowcnt = 0; rowcnt < sourceResult.size(); rowcnt++) {
                                    Map<String, Object> row = sourceResult.get(rowcnt);
                                    List<String> nowRow = new ArrayList<>();
                                    fieldIdxMap.forEach((k, v) -> {
                                        Object data = row.get(k);
                                        nowRow.add(Optional.ofNullable(data).orElse("").toString());
                                    });
                                    try {
                                        for (String col : cols) {
                                            String[] item = col.split("#");
                                            List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(tableExp[0] + "_" + item[0]);
                                            if ("empid".equals(col)) {
                                                // 2.0 租户可能没有这个校验配置
                                                if (CollectionUtils.isEmpty(fieldCheckRuleList)) {
                                                    fieldCheckRuleList.add(empCheckRule());
                                                } else {
                                                    Boolean empCacheFind = false;
                                                    for (CheckRuleDto checkRuleDto : fieldCheckRuleList) {
                                                        if ("empid".equals(checkRuleDto.getFieldCode())) {
                                                            if (!":EMPID".equals(checkRuleDto.getCheckExp())) {
                                                                checkRuleDto.setCheckExp(":EMPID");
                                                            } else {
                                                                empCacheFind = true;
                                                            }
                                                        }
                                                    }
                                                    if (!empCacheFind) {
                                                        fieldCheckRuleList.add(empCheckRule());
                                                    }
                                                }
                                            }
                                            if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                                                Object cellValue = MapUtils.getObject(row, StringUtils.trim(item[0]), "");
                                                String cellValueStr = cellValue.toString();
                                                for (CheckRuleDto crDto : fieldCheckRuleList) {
                                                    String message = crDto.getMessage();
                                                    switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                                                        //非空校验
                                                        case Required:
                                                            if (StringUtils.isEmpty(cellValueStr)) {
                                                                throw new RuntimeException(message);
                                                            }
                                                            break;
//                                                        //格式校验
//                                                        case Regex:
//                                                            if (StringUtils.isNotEmpty(cellValueStr) && !cellValueStr.matches(crDto.getCheckExp())) {
//                                                                throw new RuntimeException(MessageFormat.format(message, cellValueStr));
//                                                            }
//                                                            break;
//                                                        //枚举
//                                                        case Enum:
//                                                            if (StringUtils.isNotEmpty(cellValueStr)) {
//                                                                String enumExp = crDto.getCheckExp();
//                                                                if (StringUtils.isNotEmpty(enumExp)) {
//                                                                    Class<?> clazz = Class.forName("com.caidao1.commons.enums." + enumExp);
//                                                                    Method method = clazz.getMethod("getValue", String.class);
//                                                                    Object res = method.invoke(null, cellValueStr);
//                                                                    if (res == null) {
//                                                                        throw new RuntimeException(MessageFormat.format(message, cellValueStr));
//                                                                    }
//                                                                }
//                                                            }
//                                                            break;
//                                                        //范围校验
//                                                        case Range:
//                                                            if (StringUtils.isNotEmpty(cellValue)) {
//                                                                String range = crDto.getCheckExp();
//                                                                if (StringUtils.isNotEmpty(range)) {
//                                                                    Pattern pattern = Pattern.compile("^(\\(|\\[)(.*),(.*)(\\)|\\])$", Pattern.CASE_INSENSITIVE);
//                                                                    Matcher matcher = pattern.matcher(range);
//                                                                    message = MessageFormat.format(message, cellValue);
//                                                                    if (matcher.find()) {
//                                                                        int c1 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(2)));
//                                                                        int c2 = new BigDecimal(cellValue).compareTo(new BigDecimal(matcher.group(3)));
//                                                                        if (c1 < 0) {
//                                                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
//                                                                            checkError = true;
//                                                                        } else if (c1 == 0 && "(".equals(matcher.group(1))) {
//                                                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
//                                                                            checkError = true;
//                                                                        }
//                                                                        if (c2 > 0) {
//                                                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
//                                                                            checkError = true;
//                                                                        } else if (c2 == 0 && ")".equals(matcher.group(4))) {
//                                                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
//                                                                            checkError = true;
//                                                                        }
//                                                                    } else {
//                                                                        if (!range.contains(cellValue)) {
//                                                                            errorList.add(new CheckMessage(i + 1, j + 1, message));
//                                                                            checkError = true;
//                                                                        }
//                                                                    }
//                                                                }
//                                                            }
//                                                            break;
                                                        case SQL:
                                                        case FK:
                                                        case FKC:
                                                            if ("sys_emp_info".equals(tableExp[0]) && "orgid".equals(item[0])) {
                                                                Long existFk = importDatasService.createFKObject(jedis, null, item[0], cellValueStr, true, null, belongId, null);
                                                                break;
                                                            }
                                                            if (StringUtils.isNotEmpty(cellValueStr) && !(cellValue instanceof Integer)) {
                                                                String fksql = IocUtil.formatExp(corpId, belongId, crDto.getCheckExp(), cellValueStr.toString(), dataInput.getWorkTime());
                                                                if (fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                                    Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                                    Matcher matcher = pattern.matcher(fksql);
                                                                    if (matcher.find()) {
                                                                        String[] params = matcher.group(1).split(",");

                                                                        SysParmDict dic = getDictByName(systemMap, map2, ConvertHelper.longConvert(params[0].trim()), params[1].trim(), params[2].trim());
                                                                        if (dic == null) {
                                                                            throw new RuntimeException(MessageFormat.format(message, cellValueStr));
                                                                        }
                                                                    }
                                                                } else if (fksql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                                    String key = BaseConst.EMP_ + belongId + "_" + cellValueStr;
                                                                    if (checkCache.containsKey(key)) {
                                                                        Object existFk = checkCache.get(key);
                                                                        if (existFk == null || ConvertHelper.longConvert(existFk) == -1) {
                                                                            throw new RuntimeException(MessageFormat.format(message, cellValueStr));
                                                                        }
                                                                    } else {
                                                                        String empInfo = jedis.get(key);
                                                                        if (empInfo == null) {
                                                                            throw new RuntimeException(MessageFormat.format(message, cellValueStr));
                                                                        } else {
                                                                            checkCache.put(key, Long.parseLong(String.valueOf(empInfo.split(",")[0]).replace("\"", "")));
                                                                        }
                                                                    }
                                                                } else {
                                                                    fksql = formatRowKey(fksql, nowRow, fieldIdxMap, typeMap);
                                                                    Object existFk = getFkByExpCache(fksql);
                                                                    if (existFk == null || ConvertHelper.longConvert(existFk) == -1) {
                                                                        throw new RuntimeException(MessageFormat.format(message, cellValueStr));
                                                                    }
                                                                }
                                                            }
                                                            break;
                                                        case Java:
                                                            String javaScript = crDto.getCheckExp();
                                                            if (StringUtils.isNotEmpty(javaScript) && StringUtils.isNotEmpty(cellValueStr)) {
                                                                Map<String, Object> binding = new HashMap<String, Object>();
                                                                binding.put("rowIdx", i);
                                                                binding.put("fieldCode", crDto.getFieldCode());
                                                                binding.put("belongId", belongId);
                                                                binding.put("corpId", corpId);
                                                                List<String> row2 = new ArrayList<>();
                                                                for (String key : row.keySet()) {
                                                                    row2.add(String.valueOf(row.get(key)));
                                                                }
                                                                binding.put("row", nowRow);
                                                                binding.put("rows", rows);
                                                                binding.put("fieldIdxMap", fieldIdxMap);
                                                                String javaExp = IocUtil.formatExp(corpId, belongId, javaScript, cellValueStr, dataInput.getWorkTime());
                                                                boolean result = groovyScriptEngine.executeBoolean(javaExp, binding);
                                                                if (!result) {
                                                                    throw new RuntimeException(MessageFormat.format(message, cellValueStr));
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                            }
                                        }
                                    } catch (Exception e) {
                                        e.printStackTrace();
                                        Map checkRow = new HashMap();
                                        checkRow.putAll(row);
                                        checkRow.put("msg", e.getMessage());
                                        checkList.add(checkRow);
//                                        logList.add("数据校验不通过:" + row);
//                                        logList.add("异常原因为:" + e.getMessage());
                                        successResult.remove(row);
                                    }
                                }
                            } catch (Exception e) {
                                e.printStackTrace();
                            } finally {
                                jedis.close();
                            }
                        }

                        //后处理有效数据
                        for (int i = 0; i < expList.length; i++) {
                            String exp = expList[i];
                            String[] tableExp = exp.split("#");
                            String[] cols = colList[i].split(",");
                            List<UpdRowDto> insRows = new ArrayList<UpdRowDto>();
                            List<UpdRowDto> updRows = new ArrayList<UpdRowDto>();

                            List<String> keyList = IocUtil.getKeyListFromSql(tableExp[1], "");
                            String idName = keyList.get(keyList.size() - 1);

                            Map<String, Integer> fieldIdxMap = new HashMap<>();
                            int idx = 0;
                            for (String col : cols) {
                                fieldIdxMap.put(col.split("#")[0], idx);
                                idx++;
                            }

                            Map<String, Integer> keyCacheMap = getDbKeyCache(tableExp, corpId, belongId, dataInput.getWorkTime(), successResult, typeMap);
                            List<String> fieldList = new ArrayList<String>();
                            for (int j = 0; j < cols.length; j++) {
                                String col = cols[j];
                                String[] item = col.split("#");
                                if (item[0].startsWith(BaseConst.FIX_EXT_COLUMN)) {
                                    if (!fieldList.contains(BaseConst.EXT_CUSTOM_COLOUMN_NAME)) {
                                        fieldList.add(BaseConst.EXT_CUSTOM_COLOUMN_NAME);
                                    }
                                } else {
                                    fieldList.add(item[0]);
                                }
                            }

                            String table = tableExp[0];
                            List<FieldMetaInfo> otherFields = integrateMapper.getOtherFieldMetaInfo(table);

                            Jedis jedis = redisService.getResource();

                            try {
                                Map<String, List<SysParmDict>> systemMap = (Map) SerializeUtil.unserialize(jedis.get(SerializeUtil.serialize("SYS_PARM_DICT_MAP")));
                                Map<String, List<SysParmDict>> map2 = (Map) SerializeUtil.unserialize(jedis.get(SerializeUtil.serialize("SYS_CUST_PARM_DICT_MAP")));
                                Map<String, Integer> rowKeyList = new HashMap<>();
                                List<List<String>> rows = new ArrayList<>();
                                for (Map<String, Object> map : successResult) {
                                    List<String> row = new ArrayList<>();
                                    fieldIdxMap.forEach((k, v) -> {
                                        Object o = map.get(k);
                                        row.add(Optional.ofNullable(o).orElse("").toString());
                                    });
                                    rows.add(row);
                                }
                                for (int rowcnt = 0; rowcnt < successResult.size(); rowcnt++) {
                                    Map<String, Object> row = successResult.get(rowcnt);
                                    List<String> nowRow = new ArrayList<>();
                                    fieldIdxMap.forEach((k, v) -> {
                                        Object o = row.get(k);
                                        nowRow.add(Optional.ofNullable(o).orElse("").toString());
                                    });
                                    //重复项校验
                                    String rowKeys = "";
                                    for (String key : keyList) {
                                        rowKeys += row.getOrDefault(key, "") + "_";
                                    }
                                    if (rowKeyList.containsKey(rowKeys)) {
                                        Map checkRow = new HashMap();
                                        checkRow.putAll(row);
                                        checkRow.put("msg", MessageFormat.format("第{0}行与第{1}行存在重复数据", rowcnt + 1, rowKeyList.get(rowKeys) + 1));
                                        checkList.add(checkRow);
                                        continue;
                                    } else {
                                        rowKeyList.put(rowKeys, rowcnt);
                                    }

                                    List<SaveItemDto> saveItemDtoList = new ArrayList<SaveItemDto>();
                                    for (String col : cols) {
                                        String[] item = col.split("#");
                                        String itemCode = item[0];
                                        //自定义字段
                                        if (itemCode.startsWith(BaseConst.FIX_EXT_COLUMN)) {
                                            String cellValue = MapUtils.getString(row, StringUtils.trim(item[0]), "");
                                            SaveItemDto itemDto = null;
                                            Map<String, Object> json = new HashMap<String, Object>();
                                            List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(table + "_" + itemCode);
                                            if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                                                for (CheckRuleDto crDto : fieldCheckRuleList) {
                                                    switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                                                        case FK:
                                                        case FKC:
                                                            if (cellValue != null && StringUtils.isNotEmpty(cellValue.toString())) {
                                                                String fksql = IocUtil.formatExp(corpId, belongId, crDto.getCheckExp(), cellValue.toString(), dataInput.getWorkTime());
                                                                if (fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                                    Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                                    Matcher matcher = pattern.matcher(fksql);
                                                                    if (matcher.find()) {
                                                                        String[] params = matcher.group(1).split(",");
                                                                        SysParmDict dic = getDictByName(systemMap, map2, ConvertHelper.longConvert(params[0].trim()), params[1].trim(), params[2].trim());
                                                                        if (dic != null) {
                                                                            cellValue = dic.getDictId().toString();
                                                                        }
                                                                    }
                                                                } else if (fksql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                                    String key = BaseConst.EMP_ + belongId + "_" + cellValue;
                                                                    if (checkCache.containsKey(key)) {
                                                                        cellValue = checkCache.get(key).toString();
                                                                    } else {
                                                                        String empInfo = jedis.get(key);
                                                                        if (empInfo != null) {
                                                                            checkCache.put(key, Long.parseLong(String.valueOf(empInfo.split(",")[0]).replace("\"", "")));
                                                                            cellValue = checkCache.get(key).toString();
                                                                        }
                                                                    }
                                                                } else {
                                                                    fksql = formatRowKey(fksql, nowRow, fieldIdxMap, typeMap);
                                                                    Object existFk = getFkByExpCache(fksql);
                                                                    if (existFk != null && ConvertHelper.longConvert(existFk) != -1) {
                                                                        cellValue = existFk.toString();
                                                                    }
                                                                }
                                                            } else {
                                                                for (FieldMetaInfo field : otherFields) {
                                                                    if (field.getField().equals(itemCode)) {
                                                                        String defaultExp = field.getDefaultExp();
                                                                        if (StringUtils.isNotEmpty(defaultExp)) {
                                                                            cellValue = IocUtil.formatExp(corpId, belongId, defaultExp, dataInput.getUpdtime());
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                            }
                                            for (SaveItemDto saveItemDto : saveItemDtoList) {
                                                if (saveItemDto.getItemCode().equals(BaseConst.EXT_CUSTOM_COLOUMN_NAME)) {
                                                    itemDto = saveItemDto;
                                                    if (saveItemDto.getItemValue() != null) {
                                                        json = new ObjectMapper().readValue((String) saveItemDto.getItemValue(), Map.class);
                                                    }
                                                }
                                            }
                                            if (itemDto == null) {
                                                itemDto = new SaveItemDto();
                                                itemDto.setItemCode(BaseConst.EXT_CUSTOM_COLOUMN_NAME);
                                                itemDto.setItemType("JSONB");
                                                saveItemDtoList.add(itemDto);
                                            }
                                            if (StringUtils.isNotEmpty(cellValue)) {
                                                String type = typeMap.get(table + "_" + itemCode);
                                                json.put(itemCode, IocUtil.getItemValue(cellValue, itemCode, type));
                                                itemDto.setItemValue(new ObjectMapper().writeValueAsString(json));
                                            }
                                        } else {
                                            SaveItemDto saveItemDto = new SaveItemDto();
                                            saveItemDto.setItemCode(item[0]);
                                            saveItemDto.setItemType(item[1]);
                                            Object cellValue = row.get(StringUtils.trim(item[0]));
                                            List<CheckRuleDto> fieldCheckRuleList = checkRuleMap.get(table + "_" + saveItemDto.getItemCode());
                                            if (CollectionUtils.isNotEmpty(fieldCheckRuleList)) {
                                                for (CheckRuleDto crDto : fieldCheckRuleList) {
                                                    switch (IocEnum.valueOf(crDto.getCheckPattern())) {
                                                        case FK:
                                                        case FKC:
                                                            if (cellValue != null && StringUtils.isNotEmpty(cellValue.toString()) && !(cellValue instanceof Integer)) {
                                                                String fksql = IocUtil.formatExp(corpId, belongId, crDto.getCheckExp(), cellValue.toString(), dataInput.getWorkTime());
                                                                if (fksql.startsWith(IocUtil.REDIS_DICT_CACHE)) {
                                                                    Pattern pattern = Pattern.compile(IocUtil.REDIS_DICT_CACHE + "\\((.*)\\)+?", Pattern.CASE_INSENSITIVE);
                                                                    Matcher matcher = pattern.matcher(fksql);
                                                                    if (matcher.find()) {
                                                                        String[] params = matcher.group(1).split(",");
                                                                        SysParmDict dic = getDictByName(systemMap, map2, ConvertHelper.longConvert(params[0].trim()), params[1].trim(), params[2].trim());
                                                                        if (dic != null) {
                                                                            saveItemDto.setItemValue(dic.getDictId());
                                                                        }
                                                                    }
                                                                } else if (fksql.equals(IocUtil.REDIS_EMPID_CACHE)) {
                                                                    String key = BaseConst.EMP_ + belongId + "_" + cellValue;
                                                                    if (checkCache.containsKey(key)) {
                                                                        saveItemDto.setItemValue(checkCache.get(key));
                                                                    } else {
                                                                        String empInfo = jedis.get(key);
                                                                        if (empInfo != null) {
                                                                            checkCache.put(key, Long.parseLong(String.valueOf(empInfo.split(",")[0]).replace("\"", "")));
                                                                            saveItemDto.setItemValue(checkCache.get(key));
                                                                        }
                                                                    }
                                                                } else {
                                                                    fksql = formatRowKey(fksql, nowRow, fieldIdxMap, typeMap);
                                                                    Object existFk = getFkByExpCache(fksql);
                                                                    if (existFk != null && ConvertHelper.longConvert(existFk) != -1) {
                                                                        saveItemDto.setItemValue(existFk);
                                                                    }
                                                                }
                                                            } else {
                                                                for (FieldMetaInfo field : otherFields) {
                                                                    if (field.getField().equals(saveItemDto.getItemCode())) {
                                                                        String defaultExp = field.getDefaultExp();
                                                                        if (StringUtils.isNotEmpty(defaultExp)) {
                                                                            saveItemDto.setItemValue(IocUtil.formatExp(corpId, belongId, defaultExp, dataInput.getUpdtime()));
                                                                        }
                                                                    }
                                                                }
                                                            }
                                                            break;
                                                    }
                                                }
                                                if (saveItemDto.getItemValue() == null) {
                                                    saveItemDto.setItemValue(cellValue);
                                                }
                                            } else {
                                                saveItemDto.setItemValue(cellValue);
                                            }
                                            saveItemDtoList.add(saveItemDto);
                                        }
                                    }

                                    //多个表时外键刷新
                                    if (expList.length > 1) {
                                        for (String tableKey : tableConfigMap.keySet()) {
                                            if (!tableKey.equals(table) && keyList.contains(tableConfigMap.get(tableKey).getIdName())) {
                                                SaveItemDto saveItemDto = new SaveItemDto();
                                                saveItemDto.setItemCode(tableConfigMap.get(tableKey).getIdName());
                                                saveItemDto.setItemType("INTEGER");
                                                String rowKey = getRowKey(tableKey, tableConfigMap.get(tableKey).getKeyList(), row, typeMap);
                                                saveItemDto.setItemValue(tableConfigMap.get(tableKey).getKeyCacheMap().get(rowKey));

                                                AtomicBoolean exist = new AtomicBoolean(false);
                                                saveItemDtoList.stream().forEach(r -> {
                                                    if (r.getItemCode().equals(saveItemDto.getItemCode())) {
                                                        exist.set(true);
                                                        return;
                                                    }
                                                });

                                                if (!exist.get()) {
                                                    saveItemDtoList.add(saveItemDto);
                                                    row.put(saveItemDto.getItemCode(), saveItemDto.getItemValue());
                                                }
                                                if (!fieldList.contains(saveItemDto.getItemCode())) {
                                                    fieldList.add(saveItemDto.getItemCode());
                                                }

                                            }
                                        }
                                    }

                                    String key = getRowKey(table, keyList, row, typeMap);
                                    if (keyCacheMap.get(key) != null) {
                                        UpdRowDto updRowDto = new UpdRowDto();
                                        updRowDto.setIdName(idName);
                                        updRowDto.setId(ConvertHelper.longConvert(keyCacheMap.get(key)));
                                        updRowDto.setRow(saveItemDtoList);
                                        updRowDto.setRowCnt(rowcnt);
                                        updRows.add(updRowDto);

//                                            logList.add("开始处理数据更新:" + row);
                                    } else {
                                        for (FieldMetaInfo field : otherFields) {
                                            if (!fieldList.contains(field.getField())) {
                                                String defaultExp = field.getDefaultExp();
                                                SaveItemDto saveItemDto = new SaveItemDto();
                                                saveItemDto.setItemCode(field.getField());
                                                saveItemDto.setItemType(field.getJdbcType());
                                                if (StringUtils.isNotEmpty(defaultExp)) {
                                                    saveItemDto.setItemValue(IocUtil.formatExp(corpId, belongId, defaultExp, dataInput.getUpdtime()));
                                                } else {
                                                    if ("crtuser".equals(field.getField()) || "upduser".equals(field.getField())) {
                                                        Long userId = Optional.ofNullable(UserContext.getUserId()).orElse(0L);
                                                        saveItemDto.setItemValue(userId != null ? userId : 9999);
                                                    } else if ("crttime".equals(field.getField()) || "updtime".equals(field.getField())) {
                                                        saveItemDto.setItemValue(System.currentTimeMillis() / 1000);
                                                    } else {
                                                        saveItemDto.setItemValue("");
                                                        //多个表时外键刷新
                                                        if (i > 0) {
                                                            for (String tableKey : tableConfigMap.keySet()) {
                                                                if (!tableKey.equals(table) && field.getField().equals(tableConfigMap.get(tableKey).getIdName())) {
                                                                    String rowKey = getRowKey(tableKey, tableConfigMap.get(tableKey).getKeyList(), row, typeMap);
                                                                    saveItemDto.setItemValue(tableConfigMap.get(tableKey).getKeyCacheMap().get(rowKey));
                                                                }
                                                            }
                                                        }
                                                    }
                                                }
                                                saveItemDtoList.add(saveItemDto);
                                            }
                                        }

                                        UpdRowDto updRowDto = new UpdRowDto();
                                        updRowDto.setIdName(idName);
                                        updRowDto.setRow(saveItemDtoList);
                                        updRowDto.setRowCnt(rowcnt);
                                        insRows.add(updRowDto);

//                                            logList.add("开始处理数据新增:" + row);
                                    }
                                }
                            } catch (Exception e) {
                               log.error("inputErr:{}",e.getMessage(),e);
                            } finally {
                                jedis.close();
                            }

                            if (insRows.size() > 0) {
                                logList.add("开始处理数据新增:" + insRows.size());
                                for (FieldMetaInfo field : otherFields) {
                                    if (!fieldList.contains(field.getField())) {
                                        fieldList.add(field.getField());
                                    }
                                }

                                AddTableDto addTableDto = new AddTableDto();
                                addTableDto.setTableName(tableExp[0]);
                                addTableDto.setFields(fieldList);
                                addTableDto.setRows(insRows);
                                importService.fastInsertList(addTableDto);

                                addList.put(tableExp[0], insRows);

                                //刷新ID
                                if (StringUtils.isNotEmpty(dataInput.getAfterTrigger())) {
                                    keyCacheMap = getDbKeyCache(tableExp, corpId, belongId, dataInput.getWorkTime(), sourceResult, typeMap);
                                    for (UpdRowDto updRowDto : insRows) {
                                        updRowDto.setIdName(idName);
                                        String key = getRowKey(tableExp[0], keyList, successResult.get(updRowDto.getRowCnt()), typeMap);
                                        if (keyCacheMap.get(key) == null) {
                                            System.out.print(MessageFormat.format("table{0}key{1}row{2}", tableExp[0], key, updRowDto.getRowCnt()));
                                        } else {
                                            updRowDto.setId(ConvertHelper.longConvert(keyCacheMap.get(key)));
                                        }
                                    }
                                }
                            }

                            if (updRows.size() > 0) {
                                if (!forcedupdate) {//数据软更新，排除值为null的数据
                                    for (UpdRowDto updRow : updRows) {
                                        List<SaveItemDto> itemDtoList = updRow.getRow();
                                        if (CollectionUtils.isNotEmpty(itemDtoList)) {
                                            itemDtoList = itemDtoList.stream().filter(saveItemDto -> saveItemDto.getItemValue() != null).collect(Collectors.toList());
                                            updRow.setRow(itemDtoList);
                                        }
                                    }
                                }
                                logList.add("开始处理数据更新:" + updRows.size());
                                UpdTableDto updTableDto = new UpdTableDto();
                                updTableDto.setTableName(tableExp[0]);
                                updTableDto.setRows(updRows);
                                importService.fastUpdateList(updTableDto);

                                updList.put(tableExp[0], updRows);
                            }

                            TableConfigDto tableConfigDto = new TableConfigDto();
                            tableConfigDto.setIdName(idName);
                            tableConfigDto.setKeyExp(tableExp[1]);
                            tableConfigDto.setKeyList(keyList);
                            tableConfigDto.setKeyCacheMap(keyCacheMap);
                            tableConfigMap.put(tableExp[0], tableConfigDto);
                        }
                    }

                    if (StringUtils.isNotEmpty(dataInput.getAfterTrigger())) {
                        if (dataInput.getTargetType().equals("no")) {
                            logList.add(DateUtil.getCurTimeStamp() + "开始执行后置触发器:" + dataInput.getAfterTrigger());
                            Map<String, Object> params = new HashMap<String, Object>();
                            params.put("belongId", belongId);
                            params.put("corpId", corpId);
                            params.put("sourceResult", sourceResult);
                            params.put("checkList", checkList);

                            if (dataInput.getAfterTrigger().contains("sourceResult")) {
                                groovyScriptEngine.execute(dataInput.getAfterTrigger(), params);
                            } else {
                                for (int rowcnt = 0; rowcnt < sourceResult.size(); rowcnt++) {
                                    Map<String, Object> row = sourceResult.get(rowcnt);
                                    for (String col : dataInput.getTargetCol().split(",")) {
                                        params.put(col, row.get(col));
                                    }
                                    groovyScriptEngine.execute(dataInput.getAfterTrigger(), params);
                                }
                            }
                            logList.add(DateUtil.getCurTimeStamp() + "结束执行后置触发器");
                        } else {
                            Map<String, Object> params = new HashMap<String, Object>();
                            params.put("belongId", belongId);
                            params.put("corpId", corpId);
                            params.put("addList", addList);
                            params.put("updList", updList);
                            params.put("checkList", checkList);
                            params.put("successResult", successResult);
                            params.put("fkInfoMap", fkInfoMap);
                            logList.add(DateUtil.getCurTimeStamp() + "开始执行后置触发器:" + dataInput.getAfterTrigger());
                            groovyScriptEngine.execute(dataInput.getAfterTrigger(), params);
                            logList.add(DateUtil.getCurTimeStamp() + "结束执行后置触发器");
                        }
                    }

                    if (StringUtils.isNotEmpty(dataInput.getCallbackExp())) {
                        logList.add(DateUtil.getCurTimeStamp() + "开始执行回调方法:" + dataInput.getCallbackExp());
                        if (dataInput.getCallbackType().equals("db")) {
                            DatabaseDefineBean databaseDefineBean = new DatabaseDefineBean((String) mapJson.get("database.username"), (String) mapJson.get("database.password"), (String) mapJson.get("database.url"), (String) mapJson.get("database.driver"));
                            cusDataSource.setDatabaseDefineBean(databaseDefineBean);
                            for (Map row : successResult) {
                                cusJdbcTemplate.update(dataInput.getCallbackExp(), row);
                            }
                        } else if (dataInput.getCallbackType().equals("sftp")) {
                            Map<String, Object> params = new HashMap<String, Object>();
                            params.put("belongId", belongId);
                            params.put("corpId", corpId);
                            params.put("mapJson", mapJson);
                            //接入失败文件
                            if (returnMap != null && returnMap.get("failFiles") != null) {
                                List<String> failFiles = (List<String>) returnMap.get("failFiles");
                                params.put("failFiles", failFiles);
                            }
                            params.put("successResult", successResult);
                            logList.add(DateUtil.getCurTimeStamp() + "开始执行回调方法:" + dataInput.getCallbackExp());
                            groovyScriptEngine.execute(dataInput.getCallbackExp(), params);
                        }
                        logList.add(DateUtil.getCurTimeStamp() + "执行了回调方法，共" + successResult.size() + "个");
                    }
                }
                SysDataInput upd = new SysDataInput();
                upd.setSysDataInputId(dataInput.getSysDataInputId());
                upd.setUpdtime(DateUtil.getCurrentTime(true));
                dataInputMapper.updateByPrimaryKeySelective(upd);
            }

            //最终校验表达式
            if (StringUtils.isNotEmpty(dataInput.getFinalCheckExp())) {
                logList.add(DateUtil.getCurTimeStamp() + "开始执行最终校验表达式:" + dataInput.getCallbackExp());
                Map<String, Object> binding = new HashMap<>();
                binding.put("corpId", corpId);
                binding.put("belongId", belongId);
                binding.put("sourceResult", sourceAllResult);
                binding.put("dataInput", dataInput);
                binding.put("dataInputLog", dataInputLog);
                binding.put("typeMap", typeMap);
                binding.put("returnMap", returnMap);
                groovyScriptEngine.executeObject(dataInput.getFinalCheckExp(), binding);
                logList.add(DateUtil.getCurTimeStamp() + "最终校验表达式执行结束:" + dataInput.getCallbackExp());
            }
            dataInputLog.setEndTime(DateUtil.getCurrentTime(true));
            //保存执行历史
            dataInputLog.setLogList(StringUtils.join(logList, "<br/>"));
            if (CollectionUtils.isNotEmpty(checkList)) {
                dataInputLog.setStatus("warning");
                dataInputLog.setCheckResult(new ObjectMapper().writeValueAsString(checkList));
            }
        }
        return dataInputLog;
    }

    private String formatRowKey(String exp, List<String> row, Map<String, Integer> keyIndxMap, Map<String, String> typeMap) {
        Pattern pattern = Pattern.compile(IocUtil.PARAM_ROW, Pattern.CASE_INSENSITIVE);
        Matcher matcher = pattern.matcher(exp);
        while (matcher.find()) {
            Object itemValue = IocUtil.getDateValue(row.get(keyIndxMap.get(matcher.group(1))), typeMap.get(matcher.group(1)));
            if (itemValue == null) {
                itemValue = "";
            } else if (exp.startsWith("SELECT") || exp.startsWith("select")) {
                itemValue = itemValue.toString().replace("'", "''");
            }
            exp = exp.replace(matcher.group(0), itemValue.toString());
        }
        pattern = Pattern.compile(IocUtil.PARAM_EMP_STRUC, Pattern.CASE_INSENSITIVE);
        matcher = pattern.matcher(exp);
        while (matcher.find()) {
            String workno = matcher.group(1);
            Long structId = getPayStructId(workno, keyIndxMap, row);
            if (structId == null) {
                throw new CDException("[" + workno + "]未匹配到薪资结构");
            }
            exp = exp.replace(matcher.group(0), structId.toString());
        }
        return exp;
    }

    private String replaceExp(String exp) {
        String patternStr = "belong_org_id\\s*=\\s*(\\d+)";
        Pattern pattern = Pattern.compile(patternStr);
        Matcher matcher = pattern.matcher(exp);
        StringBuffer sb = new StringBuffer();

        // 替换匹配到的内容，给数字加上单引号
        while (matcher.find()) {
            matcher.appendReplacement(sb, "belong_org_id = '" + matcher.group(1) + "'");
        }
        matcher.appendTail(sb);
        exp = sb.toString();
        return exp;
    }


    private Long getPayStructId(String workno, Map<String, Integer> fieldIdxMap, List<String> row) {
        String structName = null;
        if (fieldIdxMap.get("def_struct_main_id") != null) {
            structName = row.get(fieldIdxMap.get("def_struct_main_id"));
        }
        Long strucId;
        if (StringUtil.isNullOrEmpty(structName)) {
            strucId = importCacheService.getPayStrucMainByEmp(UserContext.getCorpId(), UserContext.getTenantId(), workno);
        } else {
            strucId = 0L;
        }
        return strucId;
    }

    private Object getFkByExpCache(String sqlExp) {
        Object existFk = null;
        if (checkCache.containsKey(sqlExp)) {
            existFk = checkCache.get(sqlExp);
        } else {
            existFk = iocImportMapper.queryFKBySql(sqlExp);
            checkCache.put(sqlExp, existFk == null ? -1 : existFk);
        }
        return existFk;
    }

    public String getRowKey(String table, List<String> keyList, Map<String, Object> row, Map<String, String> typeMap) {
        String key = table + "#";
        for (int i = 0; i < keyList.size() - 1; i++) {
            String item = keyList.get(i);
            key += "_";
            if (row.containsKey(item)) {
                String type = typeMap.get(StringUtils.trim(item));
                if ("BIGINT".equals(type) || "DATE".equalsIgnoreCase(type)) {
                    key += IocUtil.getDateValue(row.get(StringUtils.trim(item)), "BIGINT");
                } else {
                    key += row.get(StringUtils.trim(item));
                }
            }
        }
        return key;
    }

    /**
     * 数据流出接口
     *
     * @param id
     * @throws Exception
     */
    @Transactional
    public void execDataOutput(Integer id, Long corpId, String belongId, Map<String, Object> outputParams, LogDataOutput integrateLog) throws Exception {
        SysDataOutput dataOutput = dataOutputMapper.selectByPrimaryKey(id);
        PGobject pgobject = (PGobject) dataOutput.getTargetConfig();
        if (pgobject != null) {
            integrateLog.setSysDataOutputId(dataOutput.getSysDataOutputId());
            integrateLog.setBelongOrgId(belongId);
            integrateLog.setName(dataOutput.getNote());
            integrateLog.setStartTime(DateUtil.getCurrentTime(true));
            List<String> logList = new ArrayList<>();

            String jsonStr = pgobject.getValue();
            if (StringUtils.isNotEmpty(jsonStr)) {
                Map mapJson = (Map) JacksonJsonUtil.jsonToBean(jsonStr, Map.class);

                List<Map<String, Object>> sourceResult = new ArrayList<>();
                if ("db".equalsIgnoreCase(dataOutput.getSourceType())) {
                    String sql = IocUtil.formatExp(corpId, belongId, dataOutput.getSourceExp(), dataOutput.getWorkTime());
                    if (StringUtils.isNotBlank(sql)) {
                        if (MapUtils.isNotEmpty(outputParams)) {
                            sql = sql.replaceAll(":KEY", String.valueOf(outputParams.get(":KEY")));
                        }
                        sql = sql.replaceAll(":CORPID", String.valueOf(corpId)).replaceAll(":BELONGORGID", String.valueOf(belongId));
                    }
                    sourceResult = reportConfigMapper.getChartListBySql(sql);
                }
                logList.add("取得数据条数:" + sourceResult.size());

                // 前置处理
                if (StringUtils.isNotEmpty(dataOutput.getBeforeTrigger())) {
                    Map<String, Object> params = new HashMap<>();
                    params.put("belongId", belongId);
                    params.put("corpId", corpId);
                    params.put("sourceResult", sourceResult);
                    logList.add(DateUtil.getCurTimeStamp() + "开始执行前置触发器:" + dataOutput.getBeforeTrigger());
                    Object retObj = groovyScriptEngine.executeObject(dataOutput.getBeforeTrigger(), params);
                    logList.add("前置触发器调用结果:" + JSONUtils.ObjectToJson(retObj));
                    logList.add(DateUtil.getCurTimeStamp() + "结束执行前置触发器");
                }
                if (CollectionUtils.isNotEmpty(sourceResult) || MapUtils.isNotEmpty(outputParams)) {
                    SysDataOutput upd = new SysDataOutput();
                    upd.setSysDataOutputId(id);
                    upd.setWorkTime(DateUtil.getCurrentTime(true));
                    dataOutputMapper.updateByPrimaryKeySelective(upd);

                    integrateLog.setSourceResult(new ObjectMapper().writeValueAsString(sourceResult));
                    logList.add("output参数:" + new ObjectMapper().writeValueAsString(outputParams));

                    Map<String, Object> responseResult = null;
                    String isOnlyTrigger = (String) mapJson.get("isOnlyTrigger");
                    if (isOnlyTrigger == null || "false".equals(isOnlyTrigger)) {//是否只走触发器参数标识
                        OutputWriter outputWriter = ExtensionLoader.getExtensionLoader(OutputWriter.class).getExtension(dataOutput.getTargetType());
                        responseResult = outputWriter.handleSourceResult(sourceResult, dataOutput, mapJson, outputParams, logList);
                    }

                    // 后置处理
                    if (StringUtils.isNotEmpty(dataOutput.getAfterTrigger())) {
                        Map<String, Object> params = new HashMap<>();
                        params.put("belongId", belongId);
                        params.put("corpId", corpId);
                        params.put("sourceResult", sourceResult);
                        params.put("responseResult", responseResult);
                        params.put("mapJson", mapJson);
                        logList.add(DateUtil.getCurTimeStamp() + "开始执行后置触发器:" + dataOutput.getAfterTrigger());
                        Object retObj = groovyScriptEngine.executeObject(dataOutput.getAfterTrigger(), params);
                        logList.add("后置回调返回结果:" + JSONUtils.ObjectToJson(retObj));
                        logList.add(DateUtil.getCurTimeStamp() + "结束执行后置触发器");
                    }
                }
            }

            integrateLog.setEndTime(DateUtil.getCurrentTime(true));
            integrateLog.setLogList(StringUtils.join(logList, "<br/>"));

            SysDataOutput upd = new SysDataOutput();
            upd.setSysDataOutputId(dataOutput.getSysDataOutputId());
            upd.setUpdtime(DateUtil.getCurrentTime(true));
            dataOutputMapper.updateByPrimaryKeySelective(upd);
        }
    }

    private SysParmDict getDictByName(Map<String, List<SysParmDict>> map, Map<String, List<SysParmDict>> map2, Long corpId, String typeCode, String dictName) {
        if (corpId != null && typeCode != null && dictName != null) {
            SysParmDict dict = null;
            if (map != null) {
                List<SysParmDict> dicts = (List) map.get(typeCode);
                if (dicts != null && dicts.size() > 0) {
                    label68:
                    {
                        Iterator var7 = dicts.iterator();

                        SysParmDict sysParmDict;
                        do {
                            if (!var7.hasNext()) {
                                break label68;
                            }

                            sysParmDict = (SysParmDict) var7.next();
                        } while (!dictName.equals(sysParmDict.getDictChnName()) && !dictName.equals(sysParmDict.getDictEngName()) && !dictName.equals(sysParmDict.getDictCode()));

                        dict = sysParmDict;
                    }
                }
            }

            if (dict == null) {
                if (map2 != null) {
                    List<SysParmDict> dicts2 = (List) map2.get(typeCode + "_" + corpId);
                    if (dicts2 != null && dicts2.size() > 0) {
                        Iterator var12 = dicts2.iterator();

                        SysParmDict sysParmDict;
                        do {
                            if (!var12.hasNext()) {
                                return dict;
                            }

                            sysParmDict = (SysParmDict) var12.next();
                        } while (!dictName.equals(sysParmDict.getDictChnName()) && !dictName.equals(sysParmDict.getDictEngName()) && !dictName.equals(sysParmDict.getDictCode()));

                        dict = sysParmDict;
                    }
                }
            }

            return dict;
        } else {
            return null;
        }
    }

    public Map<Long, Integer> initEmp(@NotNull String belongOrgId) {
        Jedis jedis = RedisService.getResource();
        Map<Long, Integer> empStatsMap = new HashMap<>();
        try {
            final Integer pageCount = 10000;
            Long count = ConvertHelper.longConvert(iocImportMapper.queryFKBySql("SELECT COUNT(*) FROM sys_emp_info WHERE deleted = 0 and belong_org_id ='" + belongOrgId + "'"));
            Long num = count / pageCount;
            if (count % pageCount != 0) {
                num++;
            }
            for (int i = 0; i < num; i++) {
                int offset = i * pageCount;
                String sql = "SELECT empid,workno,emp_name,tm_type,belong_org_id,stats from sys_emp_info WHERE belong_org_id = '%s' and deleted = 0  ORDER BY empid LIMIT %s OFFSET %s";
                List<Map<String, Object>> mapList = iocImportMapper.queryKeyListBySql(String.format(sql, belongOrgId, pageCount, offset));
                if (CollectionUtils.isNotEmpty(mapList)) {
                    String key = BaseConst.EMP_ + belongOrgId + "_%s";
                    for (Map<String, Object> map : mapList) {
                        Long empId = ConvertHelper.longConvert(map.get("empid"));
                        String workNo = ConvertHelper.stringConvert(map.get("workno"));
                        String empName = ConvertHelper.stringConvert(map.get("emp_name"));
                        Integer tmType = ConvertHelper.intConvert(map.get("tm_type"));
                        tmType = Optional.ofNullable(tmType).orElse(1);
                        String site = "";
                        String value = empId + "," + empName + "," + tmType + "," + site;
                        jedis.set(String.format(key, workNo), value);
                        empStatsMap.put(empId, ConvertHelper.intConvert(map.get("stats")));
                    }
                }
            }
        } catch (Exception e) {
            log.error("接入接口刷新员工缓存失败 err:{}", e.getMessage(), e);
        } finally {
            jedis.close();
        }
        return empStatsMap;
    }

    public List<Map<String, Object>> dataInputTieTongDoorRecord(String belongId, String startTime, String endTime, String filterWorkNo, String accessDateDuration,String excludedAddress) throws Exception {
        List<DoorDetail> doorSourceResult = new DoorSourceInput().getDoorSourceResult(startTime, endTime, filterWorkNo, accessDateDuration, excludedAddress);
        List<Map<String, Object>> recordList = new ArrayList<>();
        for (DoorDetail doorDetail : doorSourceResult) {
            String key = BaseConst.EMP_ + belongId + "_" + doorDetail.getJobNo();
            String empCache = CDCacheUtil.getValue(key);
            if (StringUtils.isBlank(empCache)) {
                log.info("tieToneJobNo={}-{} notExists", doorDetail.getJobNo(), doorDetail.getPersonName());
                continue;
            }
            String eventTime = doorDetail.getEventTime();
            Map<String, Object> record = new HashMap<>(5);
            record.put("empid", doorDetail.getJobNo());
            record.put("reg_date_time", IsoTimeUtil.isoTimeStrToTime(eventTime));
            record.put("reg_addr", doorDetail.getDoorName());
            record.put("belong_date", IsoTimeUtil.isoTimeStrToTimeDayBegin(eventTime));
            record.put("source_from_type", 4);
            recordList.add(record);
        }
        return recordList;
    }
}