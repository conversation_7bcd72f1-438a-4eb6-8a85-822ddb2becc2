package com.caidaocloud.attendance.service.application.service.user;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.common.utils.MapUtils;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.exception.MobileException;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.employee.service.EmployeeService;
import com.caidao1.mobile.service.MobileV18Service;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.wa.dto.shift.MyWorkDateShiftDto;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.core.wa.vo.MultiShiftSimpleVo;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.CompensatoryDataDto;
import com.caidaocloud.attendance.service.application.dto.clock.BdkTimeInfoDto;
import com.caidaocloud.attendance.service.application.dto.clock.ClockListShiftDefDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.application.service.impl.WaEmpLeaveCancelService;
import com.caidaocloud.attendance.service.application.service.impl.WaShiftApplyService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.domain.service.WaRegisterRecordDomainService;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WorkOvertimeMapper;
import com.caidaocloud.attendance.service.infrastructure.util.BeanMapUtils;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.group.GroupDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.LeaveTypeInfoDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensotaryDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveDetailDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.LeaveQuotaDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ApplyShiftRecordDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ChangeShiftReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ClockTimeLimitDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.user.*;
import com.caidaocloud.attendance.service.interfaces.vo.EmpTravelVo;
import com.caidaocloud.attendance.service.interfaces.vo.WaEmpApplyRecordVo;
import com.caidaocloud.attendance.service.interfaces.vo.register.WaRegisterRecordOfPortalVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ChangeShiftVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ShiftInfoDto;
import com.caidaocloud.attendance.service.interfaces.vo.user.MyWorkEventVo;
import com.caidaocloud.attendance.service.interfaces.vo.user.MyWorkOvertimeVo;
import com.caidaocloud.cache.CacheService;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.SysConfigsEnum;
import com.caidaocloud.excption.APIException;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SnowflakeUtil;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfBeginWorkflowDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import com.caidaocloud.workflow.feign.IWfRegisterFeign;
import com.caidaocloud.workflow.util.FuncUtil;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.DefaultTransactionDefinition;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.MessageFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.caidaocloud.attendance.service.infrastructure.common.QuotaTimeFormat.formatFloat;

@Slf4j
@Service
public class MyCenterService {
    private SnowflakeUtil snowflakeUtil = new SnowflakeUtil(1, 1);
    @Resource
    private IWorkCalendarService workCalendarService;
    @Resource
    private WaLeaveDaytimeDo waLeaveDaytimeDo;
    @Resource
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Resource
    private WaClockPlan waClockPlanService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Resource
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Resource
    private MobileV18Service mobileV18Service;
    @Resource
    private WaRegisterRecordDo waRegisterRecordDo;
    @Resource
    private WaSobService waSobService;
    @Resource
    private IEmpTravelService empTravelService;
    @Resource
    private WaAnalyzeDo waAnalyzeDo;
    @Resource
    private WaTravelTypeDo waTravelTypeDo;
    @Resource
    private WaAnalyzeStatisticsReportDo waAnalyzeStatisticsReportDo;
    @Resource
    private LeaveQuotaDo leaveQuotaDo;
    @Resource
    private EmployeeService employeeService;
    @Resource
    private EmpTransitAppLyDo empTransitAppLyDo;
    @Resource
    private WaShiftApplyService waShiftApplyService;
    @Resource
    private WaEmpLeaveCancelService waEmpLeaveCancelService;
    @Resource
    private ISessionService sessionService;
    @Resource
    private CacheService cacheService;
    @Resource
    private WorkOvertimeMapper workOvertimeMapper;
    @Resource
    private WaRegisterRecordBdkDo registerRecordBdkDo;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private IWfRegisterFeign wfRegisterFeign;
    @Autowired
    private IConfigService configService;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private WorkFlowApprovalProcessorService workFlowApprovalProcessorService;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaEmpGroupMapper waEmpGroupMapper;
    @Autowired
    private WaRegisterRecordDomainService waRegisterRecordDomainService;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Resource
    private WaMapper waMapper;
    @Autowired
    private ILeaveTypeService leaveTypeService;
    @Autowired
    private IGroupService groupService;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private IScheduleQueryService scheduleQueryService;
    @Autowired
    private IShiftService shiftService;
    @Autowired
    private PlatformTransactionManager platformTransactionManager;

    public List<MyCalendarDateDto> getMyShopCalendar(Integer searchMonth) {
        return workCalendarService.getMyShopCalendar(searchMonth);
    }

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    public MyWorkEventDto getMyAttendance(Integer ymd, Integer clockType, WaParseGroup parseGroup, Long empid, UserInfo userInfo) {
        userInfo = userInfo == null ? getUserInfo() : userInfo;
        String belongOrgId = userInfo.getTenantId();
        if (empid == null) {
            empid = userInfo.getStaffId();
        }
        Long daytime = DateUtil.convertStringToDateTime(ymd.toString(), "yyyyMMdd", true);

        MyWorkEventDto workEventDto = new MyWorkEventDto();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
        workEventDto.setEmpName(empInfo.getWorkno() + "(" + Optional.ofNullable(empInfo.getEmpName()).orElse("-") + ")");

        // 查询当日排班数据
        MyWorkDateShiftDto firstShift = null;
        List<Map> shiftMapList = waCommonService.getShiftListForEmpPortal(empid, daytime);
        if (CollectionUtils.isNotEmpty(shiftMapList)) {
            List<MyWorkDateShiftDto> shiftDtoList = BeanMapUtils.mapToBean(shiftMapList, MyWorkDateShiftDto.class);
            shiftDtoList.forEach(e -> e.setShiftDefName(LangParseUtil.getI18nLanguage(e.getI18nShiftDefName(), e.getShiftName())));
            MyWorkDateShiftDto.doSetShiftTimeTxt(shiftDtoList);
            firstShift = shiftDtoList.get(0);
            workEventDto.setShifts(shiftDtoList);
        }

        // 查询当日考勤分析结果
        MyPageBounds pageBounds = new MyPageBounds();
        Map params = new HashMap();
        params.put("belongid", belongOrgId);
        params.put("startDate", daytime);
        params.put("endDate", daytime + 86399);
        params.put("empId", empid);
        PageList<Map> pageList = waAnalyzeDo.selectWaAnalyseListByWaGroup(pageBounds, params);
        if (CollectionUtils.isNotEmpty(pageList)) {
            Map map = pageList.get(0);
            Float lateTime = map.get("late_time") == null ? 0f : (Float) map.get("late_time");//迟到
            Float earlyTime = map.get("early_time") == null ? 0f : (Float) map.get("early_time");//早退
            Integer kgWorkTime = map.get("kg_work_time") == null ? 0 : (Integer) map.get("kg_work_time");//旷工
            workEventDto.setEarlyTime(earlyTime);
            workEventDto.setLateTime(lateTime);
            workEventDto.setKgWorkTime(kgWorkTime);
        } else {
            workEventDto.setEarlyTime(0f);
            workEventDto.setLateTime(0f);
            workEventDto.setKgWorkTime(0);
        }
        // 查询当日其他考勤数据
        workEventDto.setRecords(getRegisterList(empid, daytime, clockType, firstShift, parseGroup));
        workEventDto.setLts(getEmpLeaveDtoList(belongOrgId, empid, daytime, firstShift));
        workEventDto.setOts(getOtList(belongOrgId, empid, daytime));
        workEventDto.setBdkRecords(getBdkListByDate(daytime, empid, belongOrgId));
        workEventDto.setTts(empTravelService.getTravelTimeList(belongOrgId, empid, daytime, daytime + 86399, firstShift));//daytime+86399 加上23:59:59
        return workEventDto;
    }

    public List<MyLeaveTimeDto> getEmpLeaveDtoList(String belongOrgId, Long empid, Long daytime, MyWorkDateShiftDto firstShift) {
        if (null == firstShift) {
            // 查询当日排班数据
            List<Map> shiftMapList = waCommonService.getShiftListForEmpPortal(empid, daytime);
            if (CollectionUtils.isNotEmpty(shiftMapList)) {
                List<MyWorkDateShiftDto> shiftDtoList = BeanMapUtils.mapToBean(shiftMapList, MyWorkDateShiftDto.class);
                shiftDtoList.forEach(e -> e.setShiftDefName(LangParseUtil.getI18nLanguage(e.getI18nShiftDefName(), e.getShiftName())));
                MyWorkDateShiftDto.doSetShiftTimeTxt(shiftDtoList);
                firstShift = shiftDtoList.get(0);
            }
        }
        return getLtList(belongOrgId, empid, daytime, firstShift);
    }

    public List<RegisterRecordBdkDto> getBdkListByDate(Long date, Long empId, String belongOrgId) {
        List<WaRegisterRecordBdkDo> recordList = registerRecordBdkDo.getEmpRegisterRecordList(empId, date, date, null);
        if (CollectionUtils.isNotEmpty(recordList)) {
            List<RegisterRecordBdkDto> recordDtoList = ObjectConverter.convertList(recordList, RegisterRecordBdkDto.class);
            recordDtoList.forEach(record -> {
                record.setBusinessKey(String.format("%s_%s", record.getRecordId(), BusinessCodeEnum.REGISTER.getCode()));
                if (record.getApprovalStatus() != null) {
                    record.setApprovalStatusName(ApprovalStatusEnum.getName(record.getApprovalStatus()));
                }
                record.setApplyTime(record.getCrttime());
                record.setStartDate(DateUtil.getTimeStrByTimesamp(record.getRegDateTime()));
                record.setStatus(record.getApprovalStatus());
                record.setStatusName(ApprovalStatusEnum.getName(record.getApprovalStatus()));
                record.setApplyName(CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
            });
            return recordDtoList;
        }
        return new ArrayList<>();
    }

    private List<MyOverTimeDto> getOtList(String belongOrgId, Long empid, Long daytime) {
        List<WaEmpOvertimeDo> ots = waEmpOvertimeDo.getEmpOvertimeListByYmdDate(empid, daytime, daytime + 86399);
        if (CollectionUtils.isNotEmpty(ots)) {
            List<MyOverTimeDto> overTimeDtoList = ObjectConverter.convertList(ots, MyOverTimeDto.class);
            overTimeDtoList.forEach(row -> {
                if (row.getStatus() != null) {
                    row.setStatusName(ApprovalStatusEnum.getName(row.getStatus().intValue()));
                }
                row.setBusinessKey(String.format("%s_%s", row.getOtId(), BusinessCodeEnum.OVERTIME.getCode()));
                Float duration = row.getDuration();
                //小时
                if (duration % 60 > 0) {
                    row.setTimeDurationTxt(String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR_AND_MINUTE, null).getMsg(), duration.intValue() / 60, duration % 60));
                } else {
                    row.setTimeDurationTxt(String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR, null).getMsg(), (duration.intValue() / 60)));
                }
                row.setStartDate(DateUtil.getTimeStrByTimesamp(row.getStartTime()));
                row.setEndDate(DateUtil.getTimeStrByTimesamp(row.getEndTime()));
                row.setApplyName(CalendarStatusEnum.getName(CalendarStatusEnum.OVERTIME.getIndex()));
            });
            return overTimeDtoList;
        }
        return new ArrayList<>();
    }

    /**
     * 指定日期查询员工休假数据
     *
     * @param belongOrgId
     * @param empId
     * @param daytime
     * @param shiftDto
     * @return
     */
    private List<MyLeaveTimeDto> getLtList(String belongOrgId, Long empId, Long daytime, MyWorkDateShiftDto shiftDto) {
        List<WaLeaveDaytimeDo> ltDoList = waLeaveDaytimeDo.getEmpLeaveDaytimeList(empId, daytime,
                Lists.newArrayList(ApprovalStatusEnum.IN_APPROVAL.getIndex(), ApprovalStatusEnum.PASSED.getIndex()));
        if (CollectionUtils.isEmpty(ltDoList)) {
            return new ArrayList<>();
        }
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongOrgId);
        List<MyLeaveTimeDto> dtoList = Lists.newArrayList();
        Map<Integer, List<WaLeaveDaytimeDo>> ltDoMap = ltDoList.stream().collect(Collectors.groupingBy(WaLeaveDaytimeDo::getLeaveId));
        ltDoMap.forEach((leaveId, itemLtDoList) -> {
            WaLeaveDaytimeDo leaveDaytimeDo = itemLtDoList.get(0);
            MyLeaveTimeDto leaveTimeDto = ObjectConverter.convert(leaveDaytimeDo, MyLeaveTimeDto.class);
            MyLeaveTimeDto.doSetInfo(leaveTimeDto, shiftDto, itemLtDoList, shiftDefMap);
            dtoList.add(leaveTimeDto);
        });
        return dtoList;
    }

    /**
     * 查询员工当天有效的打卡记录
     *
     * @param empId
     * @param daytime
     * @param clockType
     * @return
     */
    public List<RegisterRecordDto> getRegisterList(Long empId, Long daytime, Integer clockType,
                                                   MyWorkDateShiftDto workDateShiftDto, WaParseGroup parseGroup) {
        boolean includeOutReg = false;
        // 外勤联动班次打卡
        if (null != parseGroup && null != parseGroup.getFieldClockLinkShift() && parseGroup.getFieldClockLinkShift()) {
            includeOutReg = true;
        }
        List<Map> originalRecordList = waRegisterRecordDo.getEmpWorkTimeRecordByDay(empId, daytime, includeOutReg);
        if (CollectionUtils.isEmpty(originalRecordList)) {
            return new ArrayList<>();
        }
        List<Map> recordList = new ArrayList<>();
        if (ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(clockType)) {
            //一次卡
            originalRecordList.sort(Comparator.comparing(o -> Long.valueOf(o.get("reg_date_time").toString())));

            //查找有效的打卡记录
            if (workDateShiftDto != null && parseGroup != null && parseGroup.getClockRule() != null) {
                JSONObject json = JSONObject.parseObject(parseGroup.getClockRule());
                Integer clockRule = Integer.valueOf(json.get("clockRule").toString());

                //计算正常取卡区间
                Integer normalRegStartTime;
                Integer normalRegEndTime;

                //班次跨夜或者打卡区间跨夜
                boolean isKy;

                if (DateTypeEnum.DATE_TYP_1.getIndex().equals(workDateShiftDto.getDateType())) {
                    //工作日，跨夜
                    isKy = CdWaShiftUtil.checkCrossNight(workDateShiftDto.getStartTime(), workDateShiftDto.getEndTime(), workDateShiftDto.getDateType())
                            || workDateShiftDto.getOffDutyStartTime() > workDateShiftDto.getOffDutyEndTime();
                    if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(clockRule)) {
                        //在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = workDateShiftDto.getOnDutyStartTime();
                        normalRegEndTime = workDateShiftDto.getOffDutyEndTime();
                    } else {
                        //在班次上班时间-下班时间内存在打卡、补卡记录
                        normalRegStartTime = workDateShiftDto.getStartTime();
                        normalRegEndTime = workDateShiftDto.getEndTime();
                    }
                } else {
                    isKy = workDateShiftDto.getOffDutyStartTime() > workDateShiftDto.getOffDutyEndTime();
                    if (ParseGroupClockRuleEnum.SIGN_TIME_RANGE.getIndex().equals(clockRule)) {
                        //在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = workDateShiftDto.getOnDutyStartTime();
                        normalRegEndTime = workDateShiftDto.getOffDutyEndTime();
                    } else {
                        //在班次打卡时段内存在打卡、补卡记录
                        normalRegStartTime = workDateShiftDto.getOnDutyStartTime();
                        normalRegEndTime = workDateShiftDto.getOffDutyEndTime();
                    }
                }

                Map normalRegisterRecord = new HashMap();

                for (Map registerRecord : originalRecordList) {
                    Long belongDte = (Long) registerRecord.get("belong_date");
                    Long regTime = (Long) registerRecord.get("reg_date_time");

                    boolean flag = isMissClock(belongDte, regTime, normalRegStartTime, normalRegEndTime, isKy);
                    if (flag) {
                        normalRegisterRecord.putAll(registerRecord);
                        break;
                    }
                }

                //以前是通过registerType=4来标记一次卡，后面不再使用此字段来标记，使用clockType来标记，regMap put registerType = 4 目的时为了兼容之前逻辑，后面上限稳定后可以去掉
                normalRegisterRecord.put("register_type", ClockTypeEnum.SIGN_ONCE.getIndex());
                recordList.add(normalRegisterRecord);
            } else {
                log.info("MyCenterService.getRegisterList workDateShiftDto is null or parseGroup is null");
            }
        } else {
            //二次卡 取最早的签到和最晚的签退
            originalRecordList.sort(Comparator.comparing(o -> Long.valueOf(o.get("reg_date_time").toString())));
            //签到
            Map sign = originalRecordList.get(0);
            sign.put("register_type", ClockTypeEnum.SIGN_IN.getIndex());
            recordList.add(sign);
            //签退
            if (originalRecordList.size() > 1) {
                Map signOff = originalRecordList.get(originalRecordList.size() - 1);
                signOff.put("register_type", ClockTypeEnum.SIGN_OUT.getIndex());
                recordList.add(signOff);
            }
        }

        List<RegisterRecordDto> recordDtoList = new ArrayList<>();
        recordList.forEach(row -> recordDtoList.add(convertRegData(row)));
        return recordDtoList;
    }

    /**
     * 一次卡判断打卡是否有效
     *
     * @param belongDate
     * @param regTime
     * @param startTime
     * @param endTime
     * @param isKy
     * @return
     */
    public boolean isMissClock(Long belongDate, Long regTime, Integer startTime, Integer endTime, boolean isKy) {
        if (belongDate == null || regTime == null || startTime == null || endTime == null) {
            return false;
        }
        long s = belongDate + (startTime * 60);
        long e = belongDate + (endTime * 60);
        if (isKy) {
            e = DateUtil.addDate(belongDate * 1000, 1) + (endTime * 60);
        }
        return regTime >= s && regTime <= e;
    }

    public RegisterRecordDto convertRegData(Map map) {
        RegisterRecordDto registerRecordDto = new RegisterRecordDto();
        registerRecordDto.setRecordId((Integer) map.get("record_id"));
        registerRecordDto.setRegisterType((Integer) map.get("register_type"));
        registerRecordDto.setRegDateTime((Long) map.get("regedTime"));
        registerRecordDto.setRegAddr((String) map.get("regedAddr"));
        registerRecordDto.setResultType((Integer) map.get("result_type"));
        registerRecordDto.setResultDesc((String) map.get("result_desc"));
        registerRecordDto.setApprovalStatus((Integer) map.get("approval_status"));
        registerRecordDto.setType((Integer) map.get("regtype"));
        return registerRecordDto;
    }

    public WaClockPlanDto getWaClockPlanDto() {
        UserInfo userInfo = workCalendarService.checkSession();
        long currentTime = DateUtil.getOnlyDate();
        WaClockPlan waClockPlan = waClockPlanService.getMyWaClockPlan(ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId(), userInfo.getStaffId(), currentTime);
        if (null == waClockPlan) {
            return null;
        }
        return ObjectConverter.convert(waClockPlan, WaClockPlanDto.class);
    }

    public void checkForApplyBdk(SaveBdkDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getRegDateTimes())
                && dto.getRegDateTimes().stream().anyMatch(r -> r > DateUtil.getCurrentTime(true))) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.PUNCH_IN_DATE_GE_CURRENT, null).getMsg());
        }
        // 打卡方案规则校验
        WaClockPlanDto planDto = getWaClockPlanDto();
        if (null == planDto) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.NO_CHECK_IN_PLAN_SET_UP, null).getMsg());
        }
        if (null != planDto.getEnclosureRequired() && planDto.getEnclosureRequired() && StringUtil.isEmpty(dto.getFile())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.PLEASE_UPLOAD_FILE, null).getMsg());
        }
        if (BooleanUtils.isFalse(planDto.getIsSupplement())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.NO_PERMISSION_CARD_REPLACEMENT, null).getMsg());
        }
        if (planDto.getReasonMust() != null && planDto.getReasonMust() && StringUtil.isBlank(dto.getReason())) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.PUNCH_IN_REASON_MUST, null).getMsg());
        }
        if (null != planDto.getReasonWordNum() && dto.getReason().length() < planDto.getReasonWordNum()) {
            throw new ServerException(Result.fail(String.format(ResponseWrap.wrapResult(AttendanceCodes.BDK_REASON_LESS_LIMIT, null).getMsg(), planDto.getReasonWordNum())).getMsg());
        }
        if (CollectionUtils.isNotEmpty(dto.getRegDateTimes()) && planDto.getSupplementNumber() != null && dto.getRegDateTimes().size() > planDto.getSupplementNumber()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.NUMBER_EXCEEDED_UPPER_LIMIT, null).getMsg());
        }
        // 重复打卡时间校验
        List<Long> oriRegDateTimes = dto.getRegDateTimes();
        List<Long> regDateTimes = oriRegDateTimes.stream().distinct().sorted().collect(Collectors.toList());
        if (oriRegDateTimes.size() != regDateTimes.size()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.DUPLICATE_REG_DATE_TIMES, Boolean.FALSE).getMsg());
        }
    }

    /**
     * 前一日班次补卡数据校验
     *
     * @param regDateTimes
     * @param preDate
     * @param yesterdayShiftDo
     * @return
     */
    private List<BdkTimeInfoDto> checkBelongPreDateForBdk(List<Long> regDateTimes, Long preDate, WaShiftDo yesterdayShiftDo) {
        List<BdkTimeInfoDto> preBdkList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regDateTimes) || null == yesterdayShiftDo) {
            return preBdkList;
        }
        List<WaShiftDo> shiftDoList = yesterdayShiftDo.doGetShiftDoList();
        if (shiftDoList.size() > 1) {// 一天多个班
            WaShiftDef lastShiftDef = yesterdayShiftDo.doGetLastShiftDef();
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(lastShiftDef);
            if (null == lastShiftWorkTime.getOffDutyEndTime()) {
                return preBdkList;
            }
            long lastOffDutyEndTimeForLastShift = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                    ? preDate + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : preDate + lastShiftWorkTime.getOffDutyEndTime() * 60L;
            List<Long> regList = regDateTimes.stream()
                    .filter(bdkTime -> bdkTime <= lastOffDutyEndTimeForLastShift).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(regList)) {
                return preBdkList;
            }
            List<BdkTimeInfoDto> allBdkTimeInfoDtoList = new ArrayList<>();
            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(regList)) {
                    break;
                }

                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                // 当前班次最班下班打卡时间
                if (null == shiftWorkTime.getOffDutyEndTime()) {
                    continue;
                }
                long lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                        ? preDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : preDate + shiftWorkTime.getOffDutyEndTime() * 60L;

                // 当前班次内的打卡记录
                List<Long> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {// 最晚一个班次
                    currentShiftRegList = new ArrayList<>(regList);
                } else {
                    currentShiftRegList = regList.stream()
                            .filter(wrrd -> wrrd <= lastOffDutyEndTime).collect(Collectors.toList());
                    regList.removeIf(wrrd -> wrrd <= lastOffDutyEndTime);
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }
                List<BdkTimeInfoDto> bdkTimeInfoDtoList = currentShiftRegList.stream().map(regTime -> {
                    BdkTimeInfoDto bdkTimeInfoDto = new BdkTimeInfoDto();
                    bdkTimeInfoDto.setRegDateTime(regTime).setShiftDefId(shiftDef.getShiftDefId());
                    return bdkTimeInfoDto;
                }).collect(Collectors.toList());
                allBdkTimeInfoDtoList.addAll(bdkTimeInfoDtoList);
            }
            return allBdkTimeInfoDtoList;
        } else {// 一天排一个班
            WaShiftDef shiftDef = ObjectConverter.convert(yesterdayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
            boolean crossNightForSignOff = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType());
            if (!crossNightForSignOff) {
                return preBdkList;
            }
            if (null == shiftWorkTime.getOffDutyEndTime()) {
                return preBdkList;
            }
            long offDutyEndTime = preDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L;
            List<Long> regList = regDateTimes.stream()
                    .filter(bdkTime -> bdkTime <= offDutyEndTime).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(regList)) {
                return preBdkList;
            }
            return regList.stream().map(regTime -> {
                BdkTimeInfoDto bdkTimeInfoDto = new BdkTimeInfoDto();
                bdkTimeInfoDto.setRegDateTime(regTime).setShiftDefId(shiftDef.getShiftDefId());
                return bdkTimeInfoDto;
            }).collect(Collectors.toList());
        }
    }

    /**
     * 当日班次补卡数据校验
     *
     * @param regDateTimes
     * @param bdkDate
     * @param todayShiftDo
     * @return
     */
    private List<BdkTimeInfoDto> checkBelongTodayForBdk(List<Long> regDateTimes, Long bdkDate, WaShiftDo todayShiftDo) {
        List<BdkTimeInfoDto> todayBdkList = Lists.newArrayList();
        if (CollectionUtils.isEmpty(regDateTimes) || null == todayShiftDo) {
            return todayBdkList;
        }
        List<WaShiftDo> shiftDoList = todayShiftDo.doGetShiftDoList();
        if (shiftDoList.size() > 1) {// 一天多个班
            WaShiftDef lastShiftDef = todayShiftDo.doGetLastShiftDef();
            WaShiftDef lastShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(lastShiftDef);
            if (null == lastShiftWorkTime.getOffDutyEndTime()) {
                return todayBdkList;
            }
            long lastOffDutyEndTimeForLastShift = CdWaShiftUtil.checkCrossNightForSignOffEndTime(lastShiftWorkTime, lastShiftWorkTime.getDateType())
                    ? bdkDate + (lastShiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : bdkDate + lastShiftWorkTime.getOffDutyEndTime() * 60L;
            List<Long> regList = regDateTimes.stream()
                    .filter(bdkTime -> bdkTime <= lastOffDutyEndTimeForLastShift).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(regList)) {
                return todayBdkList;
            }
            List<BdkTimeInfoDto> allBdkTimeInfoDtoList = new ArrayList<>();
            for (WaShiftDo shiftDo : shiftDoList) {
                if (CollectionUtils.isEmpty(regList)) {
                    break;
                }

                WaShiftDef shiftDef = ObjectConverter.convert(shiftDo, WaShiftDef.class);
                WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);

                // 当前班次最班下班打卡时间
                if (null == shiftWorkTime.getOffDutyEndTime()) {
                    continue;
                }
                long lastOffDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                        ? bdkDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                        : bdkDate + shiftWorkTime.getOffDutyEndTime() * 60L;

                // 当前班次内的打卡记录
                List<Long> currentShiftRegList;
                if (lastShiftWorkTime.getShiftDefId().equals(shiftWorkTime.getShiftDefId())) {// 最晚一个班次
                    currentShiftRegList = new ArrayList<>(regList);
                } else {
                    currentShiftRegList = regList.stream()
                            .filter(wrrd -> wrrd <= lastOffDutyEndTime).collect(Collectors.toList());
                    regList.removeIf(wrrd -> wrrd <= lastOffDutyEndTime);
                }
                if (CollectionUtils.isEmpty(currentShiftRegList)) {
                    continue;
                }
                List<BdkTimeInfoDto> bdkTimeInfoDtoList = currentShiftRegList.stream().map(regTime -> {
                    BdkTimeInfoDto bdkTimeInfoDto = new BdkTimeInfoDto();
                    bdkTimeInfoDto.setRegDateTime(regTime).setShiftDefId(shiftDef.getShiftDefId());
                    return bdkTimeInfoDto;
                }).collect(Collectors.toList());
                allBdkTimeInfoDtoList.addAll(bdkTimeInfoDtoList);
            }
            return allBdkTimeInfoDtoList;
        } else {// 一天一个班
            WaShiftDef shiftDef = ObjectConverter.convert(todayShiftDo, WaShiftDef.class);
            WaShiftDef shiftWorkTime = CdWaShiftUtil.getShiftWorkTime(shiftDef);
            if (null == shiftWorkTime.getOffDutyEndTime()) {
                return todayBdkList;
            }
            long offDutyEndTime = CdWaShiftUtil.checkCrossNightForSignOffEndTime(shiftWorkTime, shiftWorkTime.getDateType())
                    ? bdkDate + (shiftWorkTime.getOffDutyEndTime() + 1440) * 60L
                    : bdkDate + shiftWorkTime.getOffDutyEndTime() * 60L;
            List<Long> regList = regDateTimes.stream()
                    .filter(bdkTime -> bdkTime <= offDutyEndTime).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(regList)) {
                return todayBdkList;
            }
            return regList.stream().map(regTime -> {
                BdkTimeInfoDto bdkTimeInfoDto = new BdkTimeInfoDto();
                bdkTimeInfoDto.setRegDateTime(regTime).setShiftDefId(shiftDef.getShiftDefId());
                return bdkTimeInfoDto;
            }).collect(Collectors.toList());
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public String saveBdkRegister(SaveBdkDto dto) throws Exception {
        UserInfo userInfo = workCalendarService.checkSession();
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(userInfo.getStaffId());
        if (null == empInfo) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_EXIST, null).getMsg());
        }
        checkForApplyBdk(dto);

        // 补卡时间
        List<Long> regDateTimes = dto.getRegDateTimes().stream().distinct().sorted().collect(Collectors.toList());
        dto.setRegDateTimes(regDateTimes);

        // 查询排班
        Long startDate = DateUtil.getOnlyDate(new Date(regDateTimes.stream().min(Long::compare).orElse(0L) * 1000L));
        Long endDate = DateUtil.getOnlyDate(new Date(regDateTimes.stream().max(Long::compare).orElse(0L) * 1000L));
        Long preDate = DateUtil.addDate(startDate * 1000, -1);
        Map<String, WaShiftDo> empShiftDoMap = scheduleQueryService.getEmpCalendarShiftMap(userInfo.getTenantId(),
                preDate, endDate, Lists.newArrayList(empInfo.getEmpid()));
        if (MapUtils.isEmpty(empShiftDoMap)) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, Boolean.FALSE).getMsg());
        }

        // 计算并校验补卡归属日期
        Long belongDate;
        WaShiftDo currentShiftDo;
        List<BdkTimeInfoDto> bdkTimeInfoDtoList;

        // 前一日班次补卡数据校验
        WaShiftDo yesterdayShiftDo = empShiftDoMap.get(empInfo.getEmpid() + "_" + preDate);
        List<BdkTimeInfoDto> preBdkList = checkBelongPreDateForBdk(regDateTimes, preDate, yesterdayShiftDo);
        if (CollectionUtils.isNotEmpty(preBdkList)) {
            if (preBdkList.size() < regDateTimes.size()) {
                throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_MULTI_DAY_NOT_ALLOWED, Boolean.FALSE).getMsg());
            }
            belongDate = preDate;
            currentShiftDo = yesterdayShiftDo;
            bdkTimeInfoDtoList = preBdkList;
        } else {// 当日的补卡数据校验
            WaShiftDo shiftDo = empShiftDoMap.get(empInfo.getEmpid() + "_" + startDate);
            List<BdkTimeInfoDto> todayBdkList = checkBelongTodayForBdk(regDateTimes, startDate, shiftDo);
            if (todayBdkList.size() < regDateTimes.size()) {
                throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.APPLY_MULTI_DAY_NOT_ALLOWED, Boolean.FALSE).getMsg());
            }
            belongDate = startDate;
            currentShiftDo = shiftDo;
            bdkTimeInfoDtoList = todayBdkList;
        }

        // 补打卡申请单据
        WaRegisterRecordBdkDo recordBdkDo = ObjectConverter.convert(dto, WaRegisterRecordBdkDo.class);
        recordBdkDo.setRegDateTimes(StringUtils.join(dto.getRegDateTimes(), ","));
        recordBdkDo.setRegDateTime(dto.getRegDateTimes().stream().findFirst().get());
        recordBdkDo.setBelongDate(belongDate);
        // 设置打卡类型(一次卡)
        WaParseGroup waParseGroup = getEmpBelongParseGroupByDate(empInfo.getEmpid(), recordBdkDo.getBelongDate());
        if (waParseGroup != null && ClockParseEnum.ONE_TIME_CARD.getIndex().equals(waParseGroup.getClockType())) {
            recordBdkDo.setRegisterType(ClockTypeEnum.SIGN_ONCE.getIndex());
        }
        // 补打卡次数校验
        checkClockCountLimit(empInfo.getEmpid(), recordBdkDo.getBelongDate());
        // 考勤截止日校验
        checkBeyondDeadLine(empInfo.getEmpid(), recordBdkDo.getBelongDate());
        // 补卡时间规则校验
        checkByClockTimeLimit(currentShiftDo, dto.getRegDateTimes(), belongDate);
        // 设置班次
        // List<Integer> shiftDefIds = currentShiftDo.doGetShiftDoList().stream().map(WaShiftDo::getShiftDefId).collect(Collectors.toList());
        List<Integer> shiftDefIds = bdkTimeInfoDtoList.stream().map(BdkTimeInfoDto::getShiftDefId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(shiftDefIds)) {
            recordBdkDo.setShiftDefId(shiftDefIds.get(0));
            recordBdkDo.setShiftDefIds(StringUtils.join(shiftDefIds, ","));
        }

        // 保存补卡申请
        recordBdkDo.setCrttime(DateUtil.getCurrentTime(Boolean.TRUE));
        recordBdkDo.setCrtuser(userInfo.getUserId());
        recordBdkDo.setApprovalStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
        recordBdkDo.setEmpid(userInfo.getStaffId());
        recordBdkDo.setIsWorkflow(Boolean.TRUE);
        recordBdkDo.setType(ClockWayEnum.FILLCLOCK.getIndex());
        recordBdkDo.setResultType(ClockResultEnum.NORMAL.getIndex());
        if (StringUtils.isNotBlank(dto.getFile())) {
            recordBdkDo.setFilePath(dto.getFile());
        }
        if (StringUtils.isNotBlank(dto.getFileName())) {
            recordBdkDo.setPicList(dto.getFileName());
        }
        recordBdkDo.setCorpid(ConvertHelper.longConvert(userInfo.getTenantId()));
        recordBdkDo.setBelongOrgId(userInfo.getTenantId());
        recordBdkDo.setRecordId(snowflakeUtil.createId());
        DefaultTransactionDefinition def = new DefaultTransactionDefinition();
        // 发起新事务
        def.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        // 获取新开事务状态
        TransactionStatus transactionStatus = platformTransactionManager.getTransaction(def);
        try {
            registerRecordBdkDo.save(recordBdkDo);
            // 保存补卡记录
            saveBdkRegisterRecords(dto, recordBdkDo, currentShiftDo, bdkTimeInfoDtoList);
            // 提交新开事务
            platformTransactionManager.commit(transactionStatus);
        } catch (Exception e) {
            // 回滚新开事务
            platformTransactionManager.rollback(transactionStatus);
            log.error("申请补卡失败:{}", e.getMessage(), e);
            throw new CDException("申请补卡失败");
        }
        // 启动工作流
        // 检查流程是否已启用
        Result<Boolean> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.REGISTER.getCode());
        if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
        }
        Boolean workflowEnabledResultData = checkWorkflowEnableResult.getData();
        if (!workflowEnabledResultData) {
            if (configService.checkSwitchStatus(SysConfigsEnum.CLOCK_WORKFLOW_SWITCH.name())) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
            }
            workFlowApprovalProcessorService.finishedBdkApproval(recordBdkDo.getRecordId(), WfCallbackTriggerOperationEnum.APPROVED);
        } else {
            String businessKey = String.valueOf(recordBdkDo.getRecordId());
            WfBeginWorkflowDto wfBeginWorkflowDto = new WfBeginWorkflowDto();
            wfBeginWorkflowDto.setFuncCode(BusinessCodeEnum.REGISTER.getCode());
            wfBeginWorkflowDto.setBusinessId(businessKey);
            wfBeginWorkflowDto.setApplicantId(String.valueOf(empInfo.getEmpid()));
            wfBeginWorkflowDto.setApplicantName(empInfo.getEmpName());
            wfBeginWorkflowDto.setEventTime(recordBdkDo.getBelongDate() * 1000);
            Result<?> result = wfRegisterFeign.begin(wfBeginWorkflowDto);
            if (null == result || !result.isSuccess() || null == result.getData() || StringUtils.isBlank(result.getData().toString())) {
                registerRecordBdkDo.delete(recordBdkDo.getRecordId());
                WaRegisterRecordExample waRegisterRecordExample = new WaRegisterRecordExample();
                waRegisterRecordExample.createCriteria().andBelongOrgIdEqualTo(userInfo.getTenantId()).andBdkRecordIdEqualTo(recordBdkDo.getRecordId());
                waRegisterRecordMapper.deleteByExample(waRegisterRecordExample);
                if (configService.checkSwitchStatus(SysConfigsEnum.CLOCK_WORKFLOW_SWITCH.name())) {
                    throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WORKFLOW_NOT_SET, null).getMsg());
                }
                workFlowApprovalProcessorService.finishedBdkApproval(recordBdkDo.getRecordId(), WfCallbackTriggerOperationEnum.APPROVED);
            }
        }
        return String.format("%s_%s", recordBdkDo.getRecordId(), BusinessCodeEnum.REGISTER.getCode());
    }

    private WaParseGroup getEmpBelongParseGroupByDate(Long empId, Long date) {
        WaEmpGroupExample example = new WaEmpGroupExample();
        example.createCriteria().andEmpidEqualTo(empId).
                andStartTimeLessThanOrEqualTo(date).
                andEndTimeGreaterThanOrEqualTo(date + 86399);
        List<WaEmpGroup> empGroupList = waEmpGroupMapper.selectByExample(example);
        if (CollectionUtils.isEmpty(empGroupList)) {
            return null;
        }
        Integer waGroupId = empGroupList.get(0).getWaGroupId();
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
        if (waGroup == null) {
            return null;
        }
        return waParseGroupMapper.selectByPrimaryKey(waGroup.getParseGroupId());
    }

    public void saveBdkRegisterRecords(SaveBdkDto dto, WaRegisterRecordBdkDo recordBdkDo, WaShiftDo waShiftDo,
                                       List<BdkTimeInfoDto> bdkTimeInfoDtoList) {
        if (CollectionUtils.isEmpty(dto.getRegDateTimes())) {
            return;
        }
        Map<Long, Integer> regTimeMap = bdkTimeInfoDtoList.stream()
                .collect(Collectors.toMap(BdkTimeInfoDto::getRegDateTime, BdkTimeInfoDto::getShiftDefId, (v1, v2) -> v1));
        for (Long regDateTime : dto.getRegDateTimes()) {
            WaRegisterRecord register = ObjectConverter.convert(dto, WaRegisterRecord.class);
            register.setBelongDate(recordBdkDo.getBelongDate());
            register.setRegDateTime(regDateTime);
            register.setCrtuser(recordBdkDo.getCrtuser());
            register.setCrttime(DateUtil.getCurrentTime(Boolean.TRUE));
            // 设置班次ID（补卡分析时会重新分析对应班次信息）
            if (regTimeMap.containsKey(regDateTime) && null != regTimeMap.get(regDateTime)) {
                register.setShiftDefId(regTimeMap.get(regDateTime));
            } else {
                register.setShiftDefId(waShiftDo.getShiftDefId());
            }
            register.setEmpid(recordBdkDo.getEmpid());
            register.setIsWorkflow(Boolean.TRUE);
            register.setApprovalStatus(ApprovalStatusEnum.IN_APPROVAL.getIndex());
            register.setResultType(ClockResultEnum.NORMAL.getIndex());
            register.setType(ClockWayEnum.FILLCLOCK.getIndex());
            if (StringUtils.isNotBlank(dto.getFile())) {
                register.setFilePath(dto.getFile());
            }
            if (StringUtils.isNotBlank(dto.getFileName())) {
                register.setPicList(dto.getFileName());
            }
            register.setCorpid(ConvertHelper.longConvert(recordBdkDo.getBelongOrgId()));
            register.setBelongOrgId(recordBdkDo.getBelongOrgId());
            register.setBdkRecordId(recordBdkDo.getRecordId());
            waRegisterRecordMapper.insertSelective(register);
        }
    }

    /**
     * 补卡时间规则校验
     *
     * @param waShiftDo
     * @param regDateTimes
     * @param belongDate
     */
    private void checkByClockTimeLimit(WaShiftDo waShiftDo, List<Long> regDateTimes, Long belongDate) {
        if (StringUtil.isBlank(waShiftDo.getClockTimeLimit())) {
            return;
        }
        List<Long> bdkTimeList = new ArrayList<>(regDateTimes);
        List<WaShiftDo> shiftDoList = waShiftDo.doGetShiftDoList();
        if (shiftDoList.size() > 1) {// 一天多个班
            List<WaShiftDo> haveLimitShiftDoList = shiftDoList.stream()
                    .filter(it -> StringUtils.isNotBlank(it.getClockTimeLimit()))
                    .collect(Collectors.toList());
            if (CollectionUtils.isEmpty(haveLimitShiftDoList)) {
                return;
            }
            for (WaShiftDo shiftDo : haveLimitShiftDoList) {
                List<ClockTimeLimitDto> clockTimeLimitDtoList =
                        FastjsonUtil.toArrayList(shiftDo.getClockTimeLimit(), ClockTimeLimitDto.class);
                for (ClockTimeLimitDto timeLimitDto : clockTimeLimitDtoList) {
                    if (bdkTimeList.isEmpty()) {
                        return;
                    }
                    long startTime = belongDate + timeLimitDto.doGetRealStartTime() * 60L;
                    long endTime = belongDate + timeLimitDto.doGetRealEndTime() * 60L;
                    bdkTimeList.removeIf(bdkTime -> bdkTime >= startTime && bdkTime <= endTime);
                }
            }
        } else {// 一天排一个班
            if (StringUtils.isBlank(waShiftDo.getClockTimeLimit())) {
                return;
            }
            List<ClockTimeLimitDto> clockTimeLimitDtoList = FastjsonUtil.toArrayList(waShiftDo.getClockTimeLimit(), ClockTimeLimitDto.class);
            for (ClockTimeLimitDto timeLimitDto : clockTimeLimitDtoList) {
                if (bdkTimeList.isEmpty()) {
                    return;
                }
                long startTime = belongDate + timeLimitDto.doGetRealStartTime() * 60L;
                long endTime = belongDate + timeLimitDto.doGetRealEndTime() * 60L;
                bdkTimeList.removeIf(bdkTime -> bdkTime >= startTime && bdkTime <= endTime);
            }
        }
        if (!bdkTimeList.isEmpty()) {
            throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.TIME_NOT_ALLOWED, null).getMsg());
        }
    }

    private void checkClockCountLimit(Long empId, Long date) throws Exception {
        // 获取员工打卡方案
        WaClockPlanDto clockPlanDto = getWaClockPlanDto();
        if (null == clockPlanDto) {
            throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.NO_CHECK_IN_PLAN_SET_UP, null).getMsg());
        }
        // 补打卡上限校验
        if (BooleanUtils.isTrue(clockPlanDto.getIsSupplement()) && clockPlanDto.getSupplementCount() != null) {
            // 获取考勤周期的开始结束时间
            Map cycleDateMap = mobileV18Service.getEmpCycleDate(empId, date);
            if (cycleDateMap == null) {
                //throw new MobileException("未设置考勤周期");
                throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.WA_CYCLE_NOT_SET, null).getMsg());
            }
            Integer bdkCount = registerRecordBdkDo.getEmpDkCountByType(empId, ClockWayEnum.FILLCLOCK.getIndex(), (Long) cycleDateMap.get("cycleBegin"), (Long) cycleDateMap.get("cycleEnd"));
            if (bdkCount != null && bdkCount >= clockPlanDto.getSupplementCount()) {
                //throw new MobileException("补打卡次数已经超过设置上限");
                throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.BDK_NUM_EXCEEDED_UPPER_LIMIT, null).getMsg());
            }
        }
    }

    public PageResult<WaEmpOvertimeDo> myWorkOvertimeList(MyWorkOvertimeDto myWorkOvertimeDto) {
        UserInfo userInfo = workCalendarService.checkSession();
        WaEmpOvertimeDo search = new WaEmpOvertimeDo();
        search.setEmpid(userInfo.getStaffId());
        search.setBelongOrgId(userInfo.getTenantId());
        search.setApplyTime(myWorkOvertimeDto.getStartApplyTime());
        search.setEndApplyTime(myWorkOvertimeDto.getEndApplyTime());
        search.setStatus(myWorkOvertimeDto.getStatus());
        PageResult<WaEmpOvertimeDo> pageResult = waEmpOvertimeDo.getPageList(myWorkOvertimeDto, search);
        convertPageWorkOvertime(pageResult);
        return pageResult;
    }

    public PageResult<MyWorkOvertimeVo> myWorkOvertimeListOfPortal(QueryPageBean queryPageBean) {
        PageResult<WaEmpOvertimeDo> pageListOfPortal = waEmpOvertimeDo.getPageListOfPortal(queryPageBean);
        convertPageWorkOvertime(pageListOfPortal);
        PageResult<MyWorkOvertimeVo> myWorkOvertimeVoPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageListOfPortal, myWorkOvertimeVoPageResult, "items");
        if (CollectionUtils.isNotEmpty(pageListOfPortal.getItems())) {
            List<MyWorkOvertimeVo> voList = ObjectConverter.convertList(pageListOfPortal.getItems(), MyWorkOvertimeVo.class);
            myWorkOvertimeVoPageResult.setItems(voList);
        } else {
            myWorkOvertimeVoPageResult.setItems(Lists.newArrayList());
        }
        return myWorkOvertimeVoPageResult;
    }

    private PageResult<WaEmpOvertimeDo> convertPageWorkOvertime(PageResult<WaEmpOvertimeDo> pageResult) {
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaEmpOvertimeDo> doList = pageResult.getItems();
            String otIds = doList.stream().map(d -> d.getOtId().toString()).collect(Collectors.joining(","));
            Map<String, Float> timeDurationMap = new HashMap<>();
            if (StringUtil.isNoneBlank(otIds)) {
                List<Map> timeDurationList = workOvertimeMapper.getTimeDurationByOtIds(otIds);
                timeDurationMap = timeDurationList.stream().collect(Collectors.toMap(t -> String.format("%s_%s", t.get("overtime_id"), t.get("overtime_type_id")), t -> (Float) t.get("rel_time_duration")));
            }
            for (WaEmpOvertimeDo row : doList) {
                row.setFuncType(BaseConst.WF_FUNC_TYPE_2);
                String key = String.format("%s_%s", row.getOtId(), row.getOvertimeTypeId());
                if (timeDurationMap.containsKey(key)) {
                    row.setDuration(timeDurationMap.get(key));
                }
                if (row.getStatus() != null) {
                    row.setStatusName(ApprovalStatusEnum.getName(row.getStatus()));
                }
                if (row.getDateType() != null) {
                    row.setDateTypeName(OtTypeEnum.getDescByIndex(row.getDateType()));
                }
                if (row.getCompensateType() != null) {
                    row.setCompensateTypeName(CompensateTypeEnum.getDescByOrdinal(row.getCompensateType()));
                }
            }
        }
        return pageResult;
    }

    /**
     * 根据考勤日期获取员工当天的出勤规则
     *
     * @param vo
     * @param date
     */
    public WaParseGroup calcClockType(MyWorkEventVo vo, Long date, Long empId, UserInfo userInfo) {
        userInfo = userInfo == null ? getUserInfo() : userInfo;
        if (empId == null) {
            empId = userInfo.getStaffId();
        }
        WaParseGroup parseGroup = waRegisterRecordDo.selectAttendanceRuleByEmpidAndDate(userInfo.getTenantId(), empId, date);

        if (parseGroup != null && parseGroup.getClockType() != null && ParseGroupClockTypeEnum.SIGN_ONCE.getIndex().equals(parseGroup.getClockType())) {
            //一次卡
            vo.setClockType(ParseGroupClockTypeEnum.SIGN_ONCE.getIndex());
        } else {
            vo.setClockType(ParseGroupClockTypeEnum.SIGN_TWICE.getIndex());
        }
        return parseGroup;
    }

    private void checkBeyondDeadLine(Long empId, Long regDateTime) throws Exception {
        Optional<WaSob> optional = Optional.ofNullable(waSobService.getWaSob(empId, regDateTime));
        if (!optional.isPresent()) {
            //throw new MobileException("未设置考勤周期");
            throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.WA_CYCLE_NOT_SET, null).getMsg());
        }
        WaSob waSob = optional.get();
        Long sobEndDate = waSob.getSobEndDate();
        if (regDateTime > sobEndDate) {
            Integer sysPeriodMonth = waSob.getSysPeriodMonth();
            String enDate = DateUtil.getDateStrByTimesamp(sobEndDate);
            String[] dateList = enDate.split("-");
            //throw new MobileException("申请时间已超过" + sysPeriodMonth + "月考勤截止日" + dateList[1] + "月" + dateList[2] + "日，请联系管理员。");
            throw new MobileException(String.format(ResponseWrap.wrapResult(AttendanceCodes.EXCEEDED_ATTENDANCE_DEADLINE, null).getMsg(), sysPeriodMonth, dateList[1], dateList[2]));
        }
        if (waSob.getStatus().equals(CycleStatusEnum.ARCHIVED.getIndex())) {
            throw new MobileException(ResponseWrap.wrapResult(AttendanceCodes.WA_SOB_ISLOCK, Boolean.FALSE).getMsg());
        }
    }

    public PageResult<EmpTravelDto> getTravelList(EmpTravelReqDto dto) {
        UserInfo userInfo = workCalendarService.checkSession();
        dto.setEmpId(userInfo.getStaffId());
        return empTravelService.getEmpTravelPageList(dto, userInfo);
    }

    public PageResult<EmpTravelVo> getTravelListOfPortal(QueryPageBean queryPageBean) {
        PageResult<EmpTravelDto> pageResult = empTravelService.getEmpTravelPageListOfPortal(queryPageBean, Boolean.FALSE);
        PageResult<EmpTravelVo> empTravelVoPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, empTravelVoPageResult, "items");
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            empTravelVoPageResult.setItems(ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class));
        } else {
            empTravelVoPageResult.setItems(Lists.newArrayList());
        }
        return empTravelVoPageResult;
    }

    public PageResult<ApplyShiftRecordDto> getShiftPageList(ChangeShiftReqDto dto) {
        UserInfo userInfo = workCalendarService.checkSession();
        dto.setEmpId(userInfo.getStaffId());
        return waShiftApplyService.pageList(dto, userInfo);
    }

    public PageResult<ChangeShiftVo> getShiftPageListOfPortal(QueryPageBean queryPageBean) {
        PageResult<ApplyShiftRecordDto> pageResult = waShiftApplyService.pageListOfPortal(queryPageBean);
        PageResult<ChangeShiftVo> voResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, voResult, "items");
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<ChangeShiftVo> voList = ObjectConverter.convertList(pageResult.getItems(), ChangeShiftVo.class);
            voResult.setItems(voList);
        } else {
            voResult.setItems(Lists.newArrayList());
        }
        return voResult;
    }

    public PageResult<LeaveCancelListDto> getLeaveCancelList(LeaveCancelReqDto dto) {
        UserInfo userInfo = workCalendarService.checkSession();
        dto.setEmpId(userInfo.getStaffId());
        return waEmpLeaveCancelService.getPageList(dto, userInfo);
    }

    public List<WaTravelTypeDo> getTravelTypeList() {
        UserInfo userInfo = workCalendarService.checkSession();
        List<WaTravelTypeDo> list = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        return list;
    }

    public WaAnalyzeStatisticsReportDo getStatisticsReportByEmpId(Integer rptType, Long startDate) {
        UserInfo userInfo = workCalendarService.checkSession();
        WaAnalyzeStatisticsReportDo reportDo = waAnalyzeStatisticsReportDo.getStatisticsReportByEmpId(userInfo.getTenantId(), userInfo.getStaffId(), rptType, startDate);
        if (reportDo != null) {
            Float initValue = 0.0f;
            reportDo.setWorkTimeHour(initValue);
            reportDo.setActualWorkTimeHour(initValue);
            reportDo.setOtTimeHour(initValue);
            if (reportDo.getOtTime() != null && reportDo.getOtTime() > 0) {
                reportDo.setOtTimeHour(new BigDecimal(reportDo.getOtTime()).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN).floatValue());
            }
            if (reportDo.getWorkTime() != null && reportDo.getWorkTime() > 0) {
                reportDo.setWorkTimeHour(new BigDecimal(reportDo.getWorkTime()).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN).floatValue());
            }
            if (reportDo.getActualWorkTime() != null && reportDo.getActualWorkTime() > 0) {
                reportDo.setActualWorkTimeHour(new BigDecimal(reportDo.getActualWorkTime()).divide(new BigDecimal(60), 2, RoundingMode.HALF_DOWN).floatValue());
            }
        }
        return reportDo;
    }

    public List<LeaveQuotaDto> getLeaveQuotaList(String tenantId, Long empId, Long onlyDate) {
        if (null == onlyDate) {
            onlyDate = DateUtil.getOnlyDate();
        }
        LeaveQuotaDto dto = new LeaveQuotaDto();
        List<LeaveQuotaDto> dtoList = new ArrayList<>();
        List<LeaveQuotaDo> list = leaveQuotaDo.getLeaveQuotaList(tenantId, empId, onlyDate);
        if (CollectionUtils.isNotEmpty(list)) {
            //调休
            List<LeaveQuotaDo> restList = list.stream().filter(e -> e.getQuotaType().equals(QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(restList)) {
                BigDecimal total = BigDecimal.ZERO;
                BigDecimal usedDay = BigDecimal.ZERO;
                BigDecimal leftDay = BigDecimal.ZERO;
                BigDecimal inTransitDay = BigDecimal.ZERO;
                BigDecimal usedTime = BigDecimal.ZERO;
                for (LeaveQuotaDo quotaDo : restList) {
                    Boolean displayQuotaDetail = Optional.ofNullable(quotaDo.getDisplayQuotaDetail()).orElse(false);
                    //假期名称
                    String leaveName = LangParseUtil.getI18nLanguage(quotaDo.getI18nLeaveName(), quotaDo.getLeaveName());
                    //单位
                    Integer unit = quotaDo.getUnit();
                    //总额度
                    BigDecimal sum = BigDecimal.valueOf(quotaDo.getQuotaDay() == null ? 0f : quotaDo.getQuotaDay());
                    //已使用
                    BigDecimal time = BigDecimal.valueOf(quotaDo.getUsedDay() == null ? 0f : quotaDo.getUsedDay());
                    //流程中
                    BigDecimal inTransit = BigDecimal.valueOf(quotaDo.getInTransitQuota() == null ? 0f : quotaDo.getInTransitQuota());
                    //调整额度
                    BigDecimal adjustQuotaDay = BigDecimal.valueOf(quotaDo.getAdjustQuota() == null ? 0f : quotaDo.getAdjustQuota());

                    if (TravelTypeUnitEnum.HOUR.getIndex().equals(unit)) {
                        sum = sum.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                        //已使用
                        time = time.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                        //流程中
                        inTransit = inTransit.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                        //调整额度
                        adjustQuotaDay = adjustQuotaDay.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    }
                    usedTime = usedTime.add(time);
                    //已用 = 已使用+流程中
                    BigDecimal used = time.add(inTransit);
                    //剩余 = 总额度 - 已用
                    BigDecimal left = sum.add(adjustQuotaDay).subtract(used);
                    if (left.floatValue() >= 0) {
                        total = total.add(sum);
                        usedDay = usedDay.add(used);
                        leftDay = leftDay.add(left);
                        inTransitDay = inTransitDay.add(inTransit);
                    }
                    dto.setEmpQuotaId(quotaDo.getEmpQuotaId());
                    dto.setLeaveName(leaveName);
                    dto.setLeaveTypeId(quotaDo.getLeaveTypeId());
                    dto.setUnit(unit);
                    dto.setUnitTxt(TravelTypeUnitEnum.getName(unit));
                    dto.setQuotaType(quotaDo.getQuotaType());
                    dto.setDisplayQuotaDetail(displayQuotaDetail);
                }
                dto.setTotalDay(total.floatValue());
                dto.setUsedDay(usedTime.floatValue());
                dto.setLeftDay(leftDay.floatValue());
                dto.setInTransitQuota(inTransitDay.floatValue());
                dtoList.add(dto);
            }
            list.removeAll(restList);
            //年假和固额
            if (CollectionUtils.isNotEmpty(list)) {
                Map<Integer, List<LeaveQuotaDo>> leaveMap = list.stream().collect(Collectors.groupingBy(LeaveQuotaDo::getLeaveTypeId));
                leaveMap.forEach((leaveTypeId, leaveList) -> {
                    List<LeaveQuotaDto> dtos = new ArrayList<>();
                    BigDecimal sumDay = BigDecimal.ZERO;
                    BigDecimal usedSumDay = BigDecimal.ZERO;
                    BigDecimal leftSumDay = BigDecimal.ZERO;
                    BigDecimal inTransitSumDay = BigDecimal.ZERO;
                    LeaveQuotaDto quotaDto = new LeaveQuotaDto();
                    for (LeaveQuotaDo quotaDo : leaveList) {
                        //假期名称
                        //String leaveName = quotaDo.getLeaveName();
                        String leaveName = LangParseUtil.getI18nLanguage(quotaDo.getI18nLeaveName(), quotaDo.getLeaveName());
                        Boolean displayQuotaDetail = Optional.ofNullable(quotaDo.getDisplayQuotaDetail()).orElse(false);
                        //单位
                        Integer unit = quotaDo.getUnit();
                        //本年额度
                        BigDecimal sum = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getQuotaDay()).orElse(0f));
                        //已使用
                        BigDecimal used = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getUsedDay()).orElse(0f));
                        //流程中
                        BigDecimal inTransit = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getInTransitQuota()).orElse(0f));
                        //调整配额
                        BigDecimal adjustDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getAdjustQuota()).orElse(0f));
                        //调整本年已用
                        BigDecimal fixUsedDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getFixUsedDay()).orElse(0f));
                        //总额 = 本年额度 + 调整配额 + 留存
                        BigDecimal totalDay = sum.add(adjustDay);
                        BigDecimal quotaUsed = used.add(fixUsedDay);
                        //已用 = 已使用 + 流程中 + 调整本年已用
                        BigDecimal leaveUsedDay = used.add(inTransit).add(fixUsedDay);
                        Integer ifAdvance = quotaDo.getIfAdvance();
                        //当前配额
                        BigDecimal nowQuotaDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getNowQuotaDay()).orElse(0f));
                        nowQuotaDay = nowQuotaDay.add(adjustDay);
                        //剩余 = 总额 - 已用
                        BigDecimal restDay = ifAdvance != null && ifAdvance == 1 ? totalDay.subtract(leaveUsedDay) : nowQuotaDay.subtract(leaveUsedDay);
                        if (restDay.floatValue() >= 0) {
                            sumDay = sumDay.add(totalDay);
                            usedSumDay = usedSumDay.add(quotaUsed);
                            leftSumDay = leftSumDay.add(restDay);
                            inTransitSumDay = inTransitSumDay.add(inTransit);
                        }
                        quotaDto.setEmpQuotaId(quotaDo.getEmpQuotaId());
                        quotaDto.setLeaveTypeId(leaveTypeId);
                        quotaDto.setLeaveName(leaveName);
                        quotaDto.setTotalDay(formatFloat(unit, sumDay.floatValue(), 1));
                        quotaDto.setUsedDay(formatFloat(unit, usedSumDay.floatValue(), 1));
                        quotaDto.setLeftDay(formatFloat(unit, leftSumDay.floatValue(), 1));
                        quotaDto.setInTransitQuota(formatFloat(unit, inTransitSumDay.floatValue(), 1));
                        quotaDto.setUnit(unit);
                        quotaDto.setUnitTxt(TravelTypeUnitEnum.getName(unit));
                        quotaDto.setQuotaType(quotaDo.getQuotaType());
                        quotaDto.setDisplayQuotaDetail(displayQuotaDetail);
                    }
                    dtos.add(quotaDto);
                    dtoList.addAll(dtos);
                });
            }
        }
        return dtoList;
    }

    public LeaveDetailDto getLeaveDetail(Integer leaveTypeId, Integer quotaType) {
        UserInfo userInfo = workCalendarService.checkSession();
        LeaveDetailDto dto = null;
        //调休
        if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(quotaType)) {
            dto = getCompensatoryList(userInfo);
        } else {
            dto = getAnnualList(leaveTypeId, quotaType, userInfo);
        }
        return dto;
    }

    public AttendancePageResult<CompensotaryDto> getInvalidCompensatoryDetail(AttendanceBasePage page) {
        LeaveDetailDto dto = new LeaveDetailDto();
        UserInfo userInfo = workCalendarService.checkSession();
        Long onlyDate = DateUtil.getOnlyDate();
        PageList<LeaveQuotaDo> pageList = leaveQuotaDo.getInvalidCompensatory(page, userInfo.getTenantId(), userInfo.getStaffId(), onlyDate);
        handleCompenList(dto, pageList);
        var compensotaryDtos = new PageList<CompensotaryDto>(dto.getCompenList(), null);
        AttendancePageResult result = new AttendancePageResult(compensotaryDtos, page.getPageNo(), page.getPageSize());
        result.setTotal(pageList.getPaginator().getTotalCount());
        return result;
    }

    /**
     * 年假固额
     *
     * @param leaveTypeId
     * @param userInfo
     * @return
     */
    public LeaveDetailDto getAnnualList(Integer leaveTypeId, Integer quotaType, UserInfo userInfo) {
        LeaveDetailDto dto = new LeaveDetailDto();
        //计算司龄
        Float corpAge = employeeService.getEmpCorpAge(userInfo.getTenantId(), userInfo.getStaffId(), null);
        dto.setCorpAge(corpAge);
        //年假固额
        Long today = DateUtil.getOnlyDate();
        List<LeaveQuotaDo> list = leaveQuotaDo.getAnnualLeaveList(userInfo.getTenantId(), userInfo.getStaffId(), leaveTypeId, null, quotaType, today, true);
        if (CollectionUtils.isNotEmpty(list)) {
            Long hireDate = list.get(0).getHireDate();
            dto.setHireDate(hireDate);
            dto.setQuotaType(list.get(0).getQuotaType());
            dto.setWorkplace(list.get(0).getWorkplace());
            //总配额
            BigDecimal totalDay = BigDecimal.ZERO;
            //总已使用额度
            BigDecimal usedDay = BigDecimal.ZERO;
            //总剩余额度
            BigDecimal leftTotalDay = BigDecimal.ZERO;
            //总当前额度(当前额度+调整额度)
            BigDecimal totalCurrentDay = BigDecimal.ZERO;
            //总已使用当前额度
            BigDecimal totalCurrentUsedDay = BigDecimal.ZERO;
            //总剩余当前额度
            BigDecimal totalCurrentLeftDay = BigDecimal.ZERO;
            //总的可预支额度
            BigDecimal prepayAbleDay = BigDecimal.ZERO;
            //总的剩余可预支配额
            BigDecimal leftPrepayAbleDay = BigDecimal.ZERO;
            Integer unit = 1;
            String description = null;
            for (LeaveQuotaDo quotaDo : list) {
                if (StringUtil.isBlank(description) && StringUtil.isNotBlank(quotaDo.getDescription())) {
                    description = quotaDo.getDescription();
                }
                //单位
                unit = quotaDo.getUnit();
                //上年留存
                BigDecimal retainDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getRetainDay()).orElse(0f));
                quotaDo.setRetainDay(formatFloat(unit, retainDay.floatValue(), 1));
                //上年留存流程中
                BigDecimal retainInTransitQuota = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getRetainInTransitQuota()).orElse(0f));
                quotaDo.setRetainInTransitQuota(formatFloat(unit, retainInTransitQuota.floatValue(), 1));
                //上年留存已使用
                BigDecimal retainUsedDay = BigDecimal.valueOf(Optional.ofNullable(quotaDo.getRetainUsedDay()).orElse(0f));
                quotaDo.setRetainUsedDay(formatFloat(unit, retainUsedDay.floatValue(), 1));
                //本年额度
                BigDecimal sum = BigDecimal.valueOf(quotaDo.getQuotaDay() == null ? 0f : quotaDo.getQuotaDay());
                quotaDo.setQuotaDay(formatFloat(unit, sum.floatValue(), 1));
                //当前额度
                BigDecimal nowQuotaDay = BigDecimal.valueOf(quotaDo.getNowQuotaDay() == null ? 0f : quotaDo.getNowQuotaDay());
                quotaDo.setNowQuotaDay(formatFloat(unit, nowQuotaDay.floatValue(), 1));
                //调整配额
                BigDecimal adjustDay = BigDecimal.valueOf(quotaDo.getAdjustQuota() == null ? 0f : quotaDo.getAdjustQuota());
                quotaDo.setAdjustQuota(formatFloat(unit, adjustDay.floatValue(), 1));
                //已使用
                BigDecimal used = BigDecimal.valueOf(quotaDo.getUsedDay() == null ? 0f : quotaDo.getUsedDay());
                quotaDo.setUsedDay(formatFloat(unit, used.floatValue(), 1));
                //调整本年已用
                BigDecimal fixUsedDay = BigDecimal.valueOf(quotaDo.getFixUsedDay() == null ? 0f : quotaDo.getFixUsedDay());
                quotaDo.setFixUsedDay(formatFloat(unit, fixUsedDay.floatValue(), 1));
                //流程中
                BigDecimal inTransit = BigDecimal.valueOf(quotaDo.getInTransitQuota() == null ? 0f : quotaDo.getInTransitQuota());
                quotaDo.setInTransitQuota(formatFloat(unit, inTransit.floatValue(), 1));
                //总配额
                BigDecimal total = sum.add(adjustDay);
                //已用 = 已使用 + 流程中 + 调整本年已用
                BigDecimal leaveUsedDay = used.add(inTransit).add(fixUsedDay);
                //计算本年可用配额
                quotaDo.calAvailableYear();
                //本年余额
                BigDecimal leftDay = BigDecimal.valueOf(quotaDo.getAvailabQuotaDay()).add(adjustDay).subtract(leaveUsedDay).add(retainDay).subtract(retainUsedDay).subtract(retainInTransitQuota);
                leftDay = leftDay.floatValue() < 0 ? BigDecimal.ZERO : leftDay;
                quotaDo.setLeftDay(formatFloat(unit, leftDay.floatValue(), 1));
                // 预支年假 = 本年已用 + 流程中 - 当前额度，老逻辑
                // 已预支年假 = 调整本年已用 + 本年已用 + 流程中 - 当前额度 - 调整额度，新逻辑
                BigDecimal advanceDay = used.add(fixUsedDay).add(inTransit).subtract(nowQuotaDay).subtract(adjustDay);
                if (advanceDay.compareTo(BigDecimal.ZERO) < 0) {
                    quotaDo.setAdvanceDay(0f);
                } else {
                    quotaDo.setAdvanceDay(advanceDay.floatValue());
                }
                totalDay = totalDay.add(total);
                usedDay = usedDay.add(leaveUsedDay);
                //当前余额
                float currentQuota = nowQuotaDay.add(adjustDay).subtract(leaveUsedDay).add(retainDay).subtract(retainUsedDay).subtract(retainInTransitQuota).floatValue();
                quotaDo.setCurrentQuota(formatFloat(unit, currentQuota < 0 ? 0f : currentQuota, 1));
                leftTotalDay = leftTotalDay.add(leftDay);
                totalCurrentDay = totalCurrentDay.add(nowQuotaDay);
                totalCurrentUsedDay = totalCurrentUsedDay.add(leaveUsedDay);
                totalCurrentLeftDay = totalCurrentLeftDay.add(BigDecimal.valueOf(quotaDo.getCurrentQuota()));
                prepayAbleDay = prepayAbleDay.add(total.subtract(nowQuotaDay));
                //剩余可预支
                BigDecimal subtract = total.subtract(nowQuotaDay).subtract(BigDecimal.valueOf(quotaDo.getAdvanceDay()));
                if (subtract.floatValue() < 0) {
                    subtract = BigDecimal.ZERO;
                }
                leftPrepayAbleDay = leftPrepayAbleDay.add(subtract);
            }
            List<AnnualLeaveDto> dtoList = new ArrayList<>();
            Map<Integer, List<LeaveQuotaDo>> leaveMap = list.stream().collect(Collectors.groupingBy(LeaveQuotaDo::getPeriodYear));
            leaveMap.forEach((year, quotaList) -> {
                AnnualLeaveDto annualLeaveDto = null;
                for (LeaveQuotaDo quotaDo : quotaList) {
                    if (null == annualLeaveDto) {
                        annualLeaveDto = ObjectConverter.convert(quotaDo, AnnualLeaveDto.class);
                    } else {
                        quotaDo.calTotalQuota(annualLeaveDto);
                    }
                }
                assert annualLeaveDto != null;
                annualLeaveDto.setStartDate(quotaList.stream().map(LeaveQuotaDo::getStartDate).min(Long::compare).orElse(0L));
                annualLeaveDto.setLastDate(quotaList.stream().map(LeaveQuotaDo::getLastDate).max(Long::compare).orElse(0L));
                annualLeaveDto.setRetainValidDate(quotaList.stream().map(LeaveQuotaDo::getRetainValidDate).filter(Objects::nonNull).max(Long::compare).orElse(null));
                annualLeaveDto.reCalCurrentQuotaAndAdvance();
                dtoList.add(annualLeaveDto);
            });
            //总配额（本年+调整）
            dto.setTotalDay(formatFloat(unit, totalDay.floatValue(), 1));
            //总已使用（已使用+调整已使用+流程中）
            dto.setUsedDay(formatFloat(unit, usedDay.floatValue(), 1));
            //总剩余（总配额-总已使用）
            dto.setLeftDay(formatFloat(unit, leftTotalDay.floatValue(), 1));
            //总当前额度
            dto.setTotalCurrentDay(formatFloat(unit, totalCurrentDay.floatValue(), 1));
            //总已使用当前额度
            dto.setTotalCurrentUsedDay(formatFloat(unit, totalCurrentUsedDay.floatValue(), 1));
            //总剩余当前额度
            dto.setTotalCurrentLeftDay(totalCurrentLeftDay.floatValue());
            //总的可预支额度
            dto.setPrepayAbleDay(formatFloat(unit, prepayAbleDay.floatValue(), 1));
            //总的剩余可预支
            dto.setLeftPrepayAbleDay(formatFloat(unit, leftPrepayAbleDay.floatValue(), 1));
            dto.setUnit(unit);
            dto.setUnitTxt(TravelTypeUnitEnum.getName(unit));
            dto.setDescription(description);
            dto.setAnnualList(dtoList);
            dto.setIfAdvance(list.stream().anyMatch(quota -> quota.getIfAdvance() != null && quota.getIfAdvance() == 1));
        }
        return dto;
    }

    /**
     * 调休配额
     *
     * @param userInfo
     * @return
     */
    public LeaveDetailDto getCompensatoryList(UserInfo userInfo) {
        LeaveDetailDto dto = new LeaveDetailDto();
        //计算司龄
        Float corpAge = employeeService.getEmpCorpAge(userInfo.getTenantId(), userInfo.getStaffId(), null);
        dto.setCorpAge(corpAge);
        Long onlyDate = DateUtil.getOnlyDate();
        List<LeaveQuotaDo> compensatoryQuotaList = leaveQuotaDo.getCompensatoryQuotaList(userInfo.getTenantId(), userInfo.getStaffId(), onlyDate, null, null, null);
        handleCompenList(dto, compensatoryQuotaList);
        return dto;
    }

    public void handleCompenList(LeaveDetailDto dto, List<LeaveQuotaDo> compensatoryQuotaList) {
        BigDecimal total = BigDecimal.ZERO;
        BigDecimal usedDay = BigDecimal.ZERO;
        BigDecimal leftDay = BigDecimal.ZERO;
        BigDecimal inTransitDay = BigDecimal.ZERO;
        if (null != compensatoryQuotaList && compensatoryQuotaList.size() > 0) {
            Long hireDate = compensatoryQuotaList.get(0).getHireDate();
            dto.setHireDate(hireDate);
        }
        Integer unit = 2;
        String description = null;
        if (null != compensatoryQuotaList && compensatoryQuotaList.size() > 0) {
            for (LeaveQuotaDo quotaDo : compensatoryQuotaList) {
                if (StringUtil.isBlank(description) && StringUtil.isNotBlank(quotaDo.getDescription())) {
                    description = quotaDo.getDescription();
                }
                Integer overTimeType = quotaDo.getOverTimeType();
                if (null != overTimeType) {
                    String overTimeTypeTxt = OtTypeEnum.getDescByIndex(overTimeType);
                    quotaDo.setOverTimeTypeTxt(overTimeTypeTxt);
                }
                //单位
                unit = quotaDo.getUnit();
                //总额度
                BigDecimal sum = BigDecimal.valueOf(quotaDo.getQuotaDay() == null ? 0f : quotaDo.getQuotaDay());
                //已使用
                BigDecimal time = BigDecimal.valueOf(quotaDo.getUsedDay() == null ? 0f : quotaDo.getUsedDay());
                //流程中
                BigDecimal inTransit = BigDecimal.valueOf(quotaDo.getInTransitQuota() == null ? 0f : quotaDo.getInTransitQuota());
                //调整额度
                BigDecimal adjustQuota = BigDecimal.valueOf(quotaDo.getAdjustQuota() == null ? 0f : quotaDo.getAdjustQuota());
                //调整已使用
                BigDecimal fixUsedDay = BigDecimal.valueOf(quotaDo.getFixUsedDay() == null ? 0f : quotaDo.getFixUsedDay());
                //当前额度
                BigDecimal nowQuota = BigDecimal.valueOf(quotaDo.getNowQuotaDay() == null ? 0f : quotaDo.getNowQuotaDay());
                if (TravelTypeUnitEnum.HOUR.getIndex().equals(unit)) {
                    //当前配额
                    sum = sum.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    //已使用
                    time = time.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    //流程中
                    inTransit = inTransit.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    //调整额度
                    adjustQuota = adjustQuota.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    //调整已使用
                    fixUsedDay = fixUsedDay.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                    //当前额度
                    nowQuota = nowQuota.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_DOWN);
                }
                quotaDo.setQuotaDay(sum.floatValue());
                quotaDo.setUsedDay(time.floatValue());
                quotaDo.setInTransitQuota(inTransit.floatValue());
                quotaDo.setAdjustQuota(adjustQuota.floatValue());

                //已用 = 已使用+流程中
                BigDecimal used = time.add(inTransit);
                //本年余额 = 总额度 - 已用
                BigDecimal left = sum.add(adjustQuota).subtract(used);
                quotaDo.setLeftDay(left.floatValue() < 0 ? 0f : left.floatValue());

                total = total.add(sum).add(adjustQuota);
                usedDay = usedDay.add(used);
                leftDay = leftDay.add(left);
                float currentQuota = nowQuota.add(adjustQuota).subtract(used).subtract(fixUsedDay).floatValue();
                quotaDo.setCurrentQuota(currentQuota < 0 ? 0f : currentQuota);
                inTransitDay = inTransitDay.add(inTransit);
            }
        }
        List<CompensotaryDto> list = ObjectConverter.convertList(compensatoryQuotaList, CompensotaryDto.class);
        for (CompensotaryDto l : list) {
            var quotaConfigOptional = leaveTypeService.getTxLeaveQuotaConfigDetail(l.getConfigId());
            if (quotaConfigOptional.isPresent()) {
                l.checkIsApplyCompensatoryCash(quotaConfigOptional.get());
            } else if (DataSourceEnum.MANUAL.name().equals(l.getDataSource())) {
                l.setApplyCompensatoryCash(true);
            }
        }
        dto.setCompenList(list);
        dto.setUnit(unit);
        dto.setUnitTxt(TravelTypeUnitEnum.getName(unit));
        dto.setTotalDay(total.floatValue());
        dto.setUsedDay(usedDay.floatValue());
        dto.setLeftDay(leftDay.floatValue());
        dto.setDescription(description);
    }

    public PageResult<EmpTransitAppLyDo> getEmpTransitAppLyList(TransitAppLyReqDto dto) {
        UserInfo userInfo = workCalendarService.checkSession();
        Map param = new HashMap();
        param.put("empId", userInfo.getStaffId());
        param.put("belongOrgId", userInfo.getTenantId());
        PageResult<EmpTransitAppLyDo> list = empTransitAppLyDo.getEmpTransitAppLyList(dto, param);
        List<EmpTransitAppLyDo> appLyDoList = list.getItems();
        appLyDoList.forEach(EmpTransitAppLyDo::buildKey);
        return list;
    }

    public AttendancePageResult<RegisterRecordDto> getRegisterRecordListByEmpId(RegisterRecordRequestDto requestDto) {
        UserInfo userInfo = this.getUserInfo();
        requestDto.setBelongOrgId(userInfo.getTenantId());
        requestDto.setQueryApprovalBdk(true);
        AttendancePageResult<RegisterRecordDto> dtoPageResult = new AttendancePageResult<>();
        AttendancePageResult<WaRegisterRecordDo> pageResult = waRegisterRecordDo.getRegisterRecordPageListByEmpId(requestDto, userInfo.getStaffId());
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<WaRegisterRecordDo> doList = pageResult.getItems();
            List<RegisterRecordDto> dtoList = ObjectConverter.convertList(doList, RegisterRecordDto.class);
            dtoList.forEach(row -> {
                if (row.getType() != null) {
                    row.setTypeName(BaseConst.REGISTER_TYPE.get(row.getType()));
                    if (row.getType().equals(ClockWayEnum.FILLCLOCK.getIndex())) {
                        row.setBdkReason(row.getReason());
                        if (row.getApprovalStatus() != null) {
                            //row.setApprovalStatusName(BaseConst.CHANGE_APPROVAL_STATUS.get(row.getApprovalStatus()));
                            row.setApprovalStatusName(ApprovalStatusEnum.getName(row.getApprovalStatus()));
                        }
                    }
                }
            });
            //dtoList = dtoList.stream().filter(dto -> !(dto.getType().equals(ClockWayEnum.FILLCLOCK.getIndex()) && (dto.getApprovalStatus() == null || !ApprovalStatusEnum.PASSED.getIndex().equals(dto.getApprovalStatus())))).collect(Collectors.toList());
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    public ShiftInfoDto getShiftInfo(Long daytime) {
        UserInfo userInfo = workCalendarService.checkSession();
        // 打卡记录
        RegisterRecordRequestDto requestDto = new RegisterRecordRequestDto();
        requestDto.setPageNo(1);
        requestDto.setPageSize(10000);
        requestDto.setEndDate(daytime.intValue() + (24 * 60 * 60) - 1);
        requestDto.setStartDate(daytime.intValue());
        AttendancePageResult<RegisterRecordDto> registerRecord = getRegisterRecordListByEmpId(requestDto);
        List<RegisterRecordDto> list = registerRecord.getItems();
        ShiftInfoDto dto = new ShiftInfoDto();
        dto.setRecords(list);
        List<Map> shiftMapList = waCommonService.getShiftListForEmpPortal(userInfo.getStaffId(), daytime);
        List<MyWorkDateShiftDto> shiftDtoList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(shiftMapList)) {
            Long nowDate = DateUtil.getOnlyDate();
            shiftDtoList = FastjsonUtil.convertList(shiftMapList, MyWorkDateShiftDto.class);
            shiftDtoList.forEach(shift -> {
                if (shift.getStartTime() != null && shift.getStartTime() >= 0) {
                    shift.setStartTimeTxt(DateUtil.convertDateTimeToStr(nowDate + (shift.getStartTime() * 60), "HH:mm", true));
                }
                if (shift.getEndTime() != null && shift.getEndTime() >= 0) {
                    shift.setEndTimeTxt(DateUtil.convertDateTimeToStr(nowDate + (shift.getEndTime() * 60), "HH:mm", true));
                }
                shift.setShiftDefName(LangParseUtil.getI18nLanguage(shift.getI18nShiftDefName(), shift.getShiftName()));
            });
        }
        dto.setShifts(shiftDtoList);
        return dto;
    }

    public Result applyShift(ApplyShiftDto dto) {
        UserInfo userInfo = workCalendarService.checkSession();
        String lockKey = MessageFormat.format("SAVE_USER_SHIFT_APPLY_LOCK_{0}_{1}", userInfo.getTenantId(), userInfo.getStaffId());
        if (cacheService.containsKey(lockKey)) {
            return ResponseWrap.wrapResult(AttendanceCodes.DO_NOT_OPERATE_FREQUENTLY, Boolean.FALSE);
        }
        cacheService.cacheValue(lockKey, "1", 10);
        try {
            return waShiftApplyService.apply(dto, userInfo);
        } catch (Exception e) {
            log.error("applyShift exception：{}", e.getMessage(), e);
            if (e instanceof CDException || e instanceof APIException) {
                return Result.fail(e.getMessage());
            }
            return ResponseWrap.wrapResult(AttendanceCodes.SHIFT_APPLY_FAIL, Boolean.FALSE);
        } finally {
            cacheService.remove(lockKey);
        }
    }

    public PageResult<WaEmpApplyRecordVo> getAllType(QueryPageBean queryPageDto) {
        var originPageResult = WaEmpApplyRecordDo.getPage(queryPageDto);
        var pageResult = new PageResult<WaEmpApplyRecordVo>();
        BeanUtils.copyProperties(originPageResult, pageResult, "items");
        if (CollectionUtils.isEmpty(originPageResult.getItems())) {
            return pageResult;
        }
        var travelIdList = originPageResult.getItems().stream()
                .filter(e -> e.getBusinessKey().contains("ATTENDANCE-TRAVEL"))
                .map(e -> Long.valueOf(e.getBusinessKey().split("_")[0]))
                .collect(Collectors.toList());
        final Map<String, WaEmpTravelDo> travelDoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(travelIdList)) {
            travelDoMap.putAll(waEmpTravelDo.getDurationOfTravel(travelIdList).stream()
                    .collect(Collectors.toMap(e -> String.format("%s_ATTENDANCE-TRAVEL", e.getTravelId()), Function.identity())));
        }
        List<String> funCodeList = originPageResult.getItems().stream().filter(e -> StringUtils.isNotBlank(e.getModule())).map(e -> {
            var funcTypeEnum = FuncTypeEnum.valueOf(e.getModule());
            return funcTypeEnum.getName();
        }).collect(Collectors.toList());
        Map<String, Map<String, String>> funI18nMap = FuncUtil.getI18NameOfFunc(funCodeList);
        HttpServletRequest request = ((ServletRequestAttributes) (RequestContextHolder.currentRequestAttributes())).getRequest();
        val language = request.getHeader("accept-language");
        pageResult.setItems(originPageResult.getItems().stream().filter(Objects::nonNull).map(e -> {
            WaEmpApplyRecordVo waEmpApplyRecordVo = new WaEmpApplyRecordVo();
            BeanUtils.copyProperties(e, waEmpApplyRecordVo);
            if (e.getApplyTime() != null) {
                waEmpApplyRecordVo.setApplyTime(com.caidaocloud.util.DateUtil.formatTime(e.getApplyTime() * 1000));
            }
            if (e.getStartTime() != null) {
                waEmpApplyRecordVo.setStartTime(com.caidaocloud.util.DateUtil.formatTime(e.getStartTime() * 1000));
            }
            if (e.getEndTime() != null) {
                waEmpApplyRecordVo.setEndTime(com.caidaocloud.util.DateUtil.formatTime(e.getEndTime() * 1000));
            }
            if (!travelDoMap.isEmpty() && travelDoMap.containsKey(e.getBusinessKey())) {
                WaEmpTravelDo waEmpTravelDo = travelDoMap.get(e.getBusinessKey());
                if (waEmpTravelDo.getTimeUnit() == 1) {
                    //waEmpApplyRecordVo.setDuration(String.format("%s 天", waEmpTravelDo.getTimeDuration()));
                    waEmpApplyRecordVo.setDuration(String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_DAY, null).getMsg(), waEmpTravelDo.getTimeDuration()));
                } else {
                    BigDecimal v = new BigDecimal(String.valueOf(waEmpTravelDo.getTimeDuration())).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    //waEmpApplyRecordVo.setDuration(String.format("%s 小时", v.floatValue()));
                    waEmpApplyRecordVo.setDuration(String.format(ResponseWrap.wrapResult(AttendanceCodes.UNIT_HOUR, null).getMsg(), v.floatValue()));
                }
            }
            if (e.getReplaceTime() != null) {
                waEmpApplyRecordVo.setReplaceTime(com.caidaocloud.util.DateUtil.formatTime(e.getReplaceTime() * 1000));
            }
            if (StringUtils.isNotBlank(e.getModule())) {
                FuncTypeEnum funcTypeEnum = FuncTypeEnum.valueOf(e.getModule());
                if (StringUtils.isBlank(language)) {
                    waEmpApplyRecordVo.setModuleName(funcTypeEnum.getName());
                } else {
                    if (funI18nMap.containsKey(funcTypeEnum.getName())) {
                        String funName = funI18nMap.get(funcTypeEnum.getName()).getOrDefault(language, "");
                        waEmpApplyRecordVo.setModuleName(StringUtils.isNotBlank(funName) ? funName : funI18nMap.get(funcTypeEnum.getName()).getOrDefault("default", ""));
                    }
                }
            }
            return waEmpApplyRecordVo;
        }).collect(Collectors.toList()));
        return pageResult;
    }

    public PageResult<WaRegisterRecordOfPortalVo> getMakeupPuneCard(QueryPageBean queryPageBean) {
        var page1 = waRegisterRecordDomainService.getPageOfPortal(queryPageBean);
        var pageResult = new PageResult<WaRegisterRecordOfPortalVo>();
        BeanUtils.copyProperties(page1, pageResult, "items");
        if (CollectionUtils.isNotEmpty(page1.getItems())) {
            Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(UserContext.getTenantId());
            Long nowDate = DateUtil.getOnlyDate();
            pageResult.setItems(page1.getItems().stream().map(e -> {
                var waRegisterRecordOfPortalVo = new WaRegisterRecordOfPortalVo();
                BeanUtils.copyProperties(e, waRegisterRecordOfPortalVo);
                waRegisterRecordOfPortalVo.setShiftDefName(LangParseUtil.getI18nLanguage(e.getI18nShiftDefName(), e.getShiftDefName()));
                if (StringUtils.isNotBlank(e.getBdkRecordId())) {
                    waRegisterRecordOfPortalVo.setBusinessKey(String.format("%s_%s", e.getBdkRecordId(), "ATTENDANCE-REGISTER"));
                }
                if (e.getApprovalStatus() != null) {
                    waRegisterRecordOfPortalVo.setApprovalStatusName(ApprovalStatusEnum.getName(e.getApprovalStatus()));
                }
                // 班次
                if (null != e.getShiftDefIds() && !e.getShiftDefIds().isEmpty()) {
                    String[] shiftDefIds = e.getShiftDefIds().split(",");
                    List<Integer> shiftDefIdList = Stream.of(shiftDefIds).map(Integer::valueOf)
                            .collect(Collectors.toCollection(java.util.ArrayList::new));
                    List<ClockListShiftDefDto> allShiftDefDtoList = new ArrayList<>();
                    for (Integer shiftDefId : shiftDefIdList) {
                        WaShiftDef shiftDef = corpShiftDefMap.get(shiftDefId);
                        if (null == shiftDef) {
                            continue;
                        }
                        List<MultiShiftSimpleVo> shiftSimpleVoList = shiftService.convertDoToSimpleVo(Lists.newArrayList(ObjectConverter.convert(shiftDef, WaShiftDo.class)));
                        List<ClockListShiftDefDto> shiftDefDtoList = ClockListShiftDefDto.getList(shiftSimpleVoList, nowDate,
                                null, null, false);
                        allShiftDefDtoList.addAll(shiftDefDtoList);
                    }
                    waRegisterRecordOfPortalVo.setShiftDefList(allShiftDefDtoList);
                } else if (null != e.getShiftDefId() && null != corpShiftDefMap.get(e.getShiftDefId())) {
                    WaShiftDef shiftDef = corpShiftDefMap.get(e.getShiftDefId());
                    List<MultiShiftSimpleVo> shiftSimpleVoList = shiftService.convertDoToSimpleVo(Lists.newArrayList(ObjectConverter.convert(shiftDef, WaShiftDo.class)));
                    List<ClockListShiftDefDto> shiftDefDtoList = ClockListShiftDefDto.getList(shiftSimpleVoList, nowDate,
                            null, null, false);
                    waRegisterRecordOfPortalVo.setShiftDefList(shiftDefDtoList);
                }
                return waRegisterRecordOfPortalVo;
            }).collect(Collectors.toList()));
        }
        return pageResult;
    }

    public Long myCalendarDataRange() {
        UserInfo userInfo = workCalendarService.checkSession();
        //查询员工所在的考勤分组
        Map<String, Object> groupParams = new HashMap<>();
        groupParams.put("empid", userInfo.getStaffId());
        List<Map> listEmpWaGroup = waMapper.listEmpWaGroup(groupParams);
        Integer empWaGroupId = null;
        if (CollectionUtils.isNotEmpty(listEmpWaGroup)) {
            Map groupMap = listEmpWaGroup.get(0);
            empWaGroupId = (Integer) groupMap.get("wa_group_id");
            Optional<WaGroup> opt = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(empWaGroupId));
            if (opt.isPresent()) {
                WaGroup waGroup = opt.get();
                int dataRange = Optional.ofNullable(waGroup.getCalendarDataRange()).orElse(0);
                long curTime = DateUtil.getOnlyDate();
                if (dataRange == 1) {
                    return DateUtil.getOnlyDate(DateUtil.getFirstDayOfMonth(new Date(curTime * 1000)));
                } else if (dataRange == 2) {
                    long lastMonth = DateUtil.getOnlyDate(DateUtil.getFirstDayOfMonth(new Date(curTime * 1000))) - 1;
                    return DateUtil.getOnlyDate(DateUtil.getFirstDayOfMonth(new Date(lastMonth * 1000)));
                }
            }
        }
        return null;
    }

    public PageResult<MyWorkOvertimeVo> myWorkOvertimeRevokeListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        PageResult<WaEmpOvertimeDo> pageListOfPortal = waEmpOvertimeDo.getRevokePageListOfPortal(queryPageBean, workflowEnum);
        convertPageWorkOvertime(pageListOfPortal);
        PageResult<MyWorkOvertimeVo> myWorkOvertimeVoPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageListOfPortal, myWorkOvertimeVoPageResult, "items");
        if (CollectionUtils.isNotEmpty(pageListOfPortal.getItems())) {
            List<MyWorkOvertimeVo> voList = ObjectConverter.convertList(pageListOfPortal.getItems(), MyWorkOvertimeVo.class);
            voList.forEach(row -> row.setBusinessKey(String.format("%s_%s", row.getRevokeId(), workflowEnum.getCode())));
            myWorkOvertimeVoPageResult.setItems(voList);
        } else {
            myWorkOvertimeVoPageResult.setItems(Lists.newArrayList());
        }
        return myWorkOvertimeVoPageResult;
    }

    public PageResult<EmpTravelVo> getTravelRevokeListOfPortal(QueryPageBean queryPageBean, BusinessCodeEnum workflowEnum) {
        PageResult<EmpTravelDto> pageResult = empTravelService.getEmpTravelPageListOfPortal(queryPageBean, workflowEnum);
        PageResult<EmpTravelVo> empTravelVoPageResult = new PageResult<>();
        BeanUtils.copyProperties(pageResult, empTravelVoPageResult, "items");
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            empTravelVoPageResult.setItems(ObjectConverter.convertList(pageResult.getItems(), EmpTravelVo.class));
        } else {
            empTravelVoPageResult.setItems(Lists.newArrayList());
        }
        return empTravelVoPageResult;
    }

    public CompensatoryDataDto myCompensatoryQuota() {
        UserInfo userInfo = null;
        CompensatoryDataDto dto = new CompensatoryDataDto();
        try {
            userInfo = workCalendarService.checkSession();
        } catch (ServerException e) {
            log.error("myCompensatoryQuota occur error", e);
            return dto;
        }
        long onlyDate = DateUtil.getOnlyDate();
        GroupDetailDto groupDetail = groupService.getEmpGroup(userInfo.getTenantId(), userInfo.getStaffId(), onlyDate);
        if (null == groupDetail) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.ATTENDANCE_PLAN_NOT_EXIST, "").getMsg());
        }
        List<LeaveTypeInfoDto> list = leaveTypeService.getLeaveTypes(groupDetail.getWaGroupId(), null, null);
        LeaveTypeInfoDto leaveType = list.stream().filter(l -> l.getLeaveType() == 3 && l.getStatus() == 1).findFirst().orElse(null);
        if (null == leaveType) {
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.GROUP_NOT_EXIST_COMPENSATORY_LEAVE, "").getMsg());
        }
        //生效中调休配额
        List<LeaveQuotaDo> validCompensatoryQuotaList = leaveQuotaDo.getCompensatoryQuotaList(userInfo.getTenantId(), userInfo.getStaffId(), onlyDate, leaveType.getLeaveTypeId(), null, true);
        LeaveDetailDto validQuota = new LeaveDetailDto();
        handleCompenList(validQuota, validCompensatoryQuotaList);
        dto.setValidQuotaUnit(validQuota.getUnit());
        if (CollectionUtils.isNotEmpty(validQuota.getCompenList())) {
            List<Long> configIds = validQuota.getCompenList().stream().map(CompensotaryDto::getConfigId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, LeaveQuotaConfigDo> quotaConfigMap = getQuotaConfigMap(userInfo.getTenantId(), configIds);
            Map<Long, List<CompensotaryDto>> quotaMap = validQuota.getCompenList().stream().collect(Collectors.groupingBy(CompensotaryDto::getConfigId));
            for (Map.Entry<Long, List<CompensotaryDto>> entry : quotaMap.entrySet()) {
                Long configId = entry.getKey();
                if (!quotaConfigMap.containsKey(configId)) {
                    continue;
                }
                LeaveQuotaConfigDo config = quotaConfigMap.get(configId);
                Integer applyCompensatoryCash = Optional.ofNullable(config.getApplyCompensatoryCash()).orElse(0);
                if (applyCompensatoryCash != 1) {
                    continue;
                }
                if (CollectionUtils.isEmpty(config.getApplyTypes())) {
                    continue;
                }
                if (!config.getApplyTypes().contains(ApplyQuotaEnum.EFFICIENT.name())) {
                    continue;
                }
                if (config.getValidQuotaLimit() == null) {
                    continue;
                }
                dto.setValidQuota(Optional.ofNullable(dto.getValidQuota()).orElse(0f));
                Float leftQuota = BigDecimal.valueOf(entry.getValue().stream().mapToDouble(CompensotaryDto::getLeftDay).sum()).floatValue();
                Float validQuotaLimit = Optional.ofNullable(config.getValidQuotaLimit()).orElse(0f);
                Float diff = leftQuota - validQuotaLimit < 0 ? 0f : leftQuota - validQuotaLimit;
                if (diff > 0) {
                    if (dto.getValidQuotaList() == null) {
                        dto.setValidQuotaList(entry.getValue());
                    } else {
                        dto.getValidQuotaList().addAll(entry.getValue());
                    }
                }
                dto.setValidQuota(dto.getValidQuota() + diff);
            }
        }
        //已失效调休配额
        List<LeaveQuotaDo> invalidCompensatoryQuotaList = leaveQuotaDo.getInvalidCompensatoryQuotaList(userInfo.getTenantId(), userInfo.getStaffId(), onlyDate, leaveType.getLeaveTypeId(), true);
        LeaveDetailDto inValidQuota = new LeaveDetailDto();
        handleCompenList(inValidQuota, invalidCompensatoryQuotaList);
        dto.setInValidQuotaUnit(inValidQuota.getUnit());
        if (CollectionUtils.isNotEmpty(inValidQuota.getCompenList())) {
            List<Long> configIds = inValidQuota.getCompenList().stream().map(CompensotaryDto::getConfigId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
            Map<Long, List<CompensotaryDto>> quotaMap = inValidQuota.getCompenList().stream().collect(Collectors.groupingBy(CompensotaryDto::getConfigId));
            Map<Long, LeaveQuotaConfigDo> quotaConfigMap = getQuotaConfigMap(userInfo.getTenantId(), configIds);
            for (Map.Entry<Long, List<CompensotaryDto>> entry : quotaMap.entrySet()) {
                Long configId = entry.getKey();
                if (!quotaConfigMap.containsKey(configId)) {
                    continue;
                }
                LeaveQuotaConfigDo config = quotaConfigMap.get(configId);
                Integer applyCompensatoryCash = Optional.ofNullable(config.getApplyCompensatoryCash()).orElse(0);
                if (applyCompensatoryCash != 1) {
                    continue;
                }
                if (CollectionUtils.isEmpty(config.getApplyTypes())) {
                    continue;
                }
                if (!config.getApplyTypes().contains(ApplyQuotaEnum.INVALID.name())) {
                    continue;
                }
                dto.setInValidQuota(Optional.ofNullable(dto.getInValidQuota()).orElse(0f));
                Float leftQuota = BigDecimal.valueOf(entry.getValue().stream().mapToDouble(CompensotaryDto::getLeftDay).sum()).floatValue();
                if (leftQuota > 0) {
                    if (dto.getInValidQuotaList() == null) {
                        dto.setInValidQuotaList(entry.getValue());
                    } else {
                        dto.getInValidQuotaList().addAll(entry.getValue());
                    }
                }
                dto.setInValidQuota(dto.getInValidQuota() + leftQuota);
            }
        }
        dto.setOnlyInteger(Optional.ofNullable(leaveType.getApplyNumberType()).orElse(0) == 1);
        if (null != leaveType.getApplyNumberType()) {
            dto.convertToRounding(leaveType.getApplyNumberType());
        }
        return dto;
    }

    private Map<Long, LeaveQuotaConfigDo> getQuotaConfigMap(String tenantId, List<Long> configIds) {
        List<LeaveQuotaConfigDo> list = leaveQuotaConfigDo.getLeaveQuotaConfigByIds(tenantId, configIds);
        list.forEach(l -> {
            l.setApplyCompensatoryCash(Optional.ofNullable(l.getApplyCompensatoryCash()).orElse(0));
        });
        return list.stream().collect(Collectors.toMap(LeaveQuotaConfigDo::getConfigId, Function.identity(), (v1, v2) -> v1));
    }
}