package com.caidaocloud.attendance.service.application.dto.clock;

import com.caidaocloud.attendance.core.wa.dto.EmpOverInfo;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import lombok.Data;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;

import java.util.*;
import java.util.stream.Collectors;

@Data
public class ClockAnalyseDataCacheDto {
    private List<WaRegisterRecordDo> waRegList;
    private Map<String, WaShiftDo> empShiftDoMap;
    private List<EmpParseGroup> empParseList;
    private List<EmpOverInfo> empOtList;

    /**
     * 获取当日加班最早开始时间
     *
     * @param empId 员工ID
     * @param date  日期
     * @return 最早开始时间，如果没有找到则返回null
     */
    public Long doGetMinOtStartTime(Long empId, Long date) {
        if (CollectionUtils.isEmpty(this.empOtList) || empId == null || date == null) {
            return null;
        }

        return this.empOtList.stream()
                .filter(it -> empId.equals(it.getEmpid()) && date.equals(Optional.ofNullable(it.getReal_date()).orElse(it.getRegdate())))
                .map(EmpOverInfo::getStart_time)
                .filter(Objects::nonNull)
                .min(Long::compareTo)
                .orElse(null);
    }

    /**
     * 获取当日加班最晚结束时间
     *
     * @param empId 员工ID
     * @param date  日期
     * @return 最晚结束时间，如果没有找到则返回null
     */
    public Long doGetMaxOtEndTime(Long empId, Long date) {
        if (CollectionUtils.isEmpty(this.empOtList) || empId == null || date == null) {
            return null;
        }

        return this.empOtList.stream()
                .filter(it -> empId.equals(it.getEmpid()) && date.equals(Optional.ofNullable(it.getReal_date()).orElse(it.getRegdate())))
                .map(EmpOverInfo::getEnd_time)
                .filter(Objects::nonNull)
                .max(Long::compareTo)
                .orElse(null);
    }

    public static ClockAnalyseDataCacheDto doBuild(List<WaRegisterRecordDo> waRegList,
                                                   Map<String, WaShiftDo> empShiftDoMap,
                                                   List<EmpParseGroup> empParseList) {
        ClockAnalyseDataCacheDto dataCacheDto = new ClockAnalyseDataCacheDto();
        dataCacheDto.setWaRegList(waRegList);
        dataCacheDto.setEmpShiftDoMap(empShiftDoMap);
        dataCacheDto.setEmpParseList(empParseList);
        return dataCacheDto;
    }

    public static ClockAnalyseDataCacheDto doBuild() {
        return new ClockAnalyseDataCacheDto();
    }

    public static void doClear(ClockAnalyseDataCacheDto dataCacheDto) {
        if (null == dataCacheDto) {
            return;
        }
        if (dataCacheDto.getWaRegList() != null) {
            dataCacheDto.getWaRegList().clear();
        }
        if (dataCacheDto.getEmpShiftDoMap() != null) {
            dataCacheDto.getEmpShiftDoMap().clear();
        }
        if (dataCacheDto.getEmpParseList() != null) {
            dataCacheDto.getEmpParseList().clear();
        }
    }

    public void clearWaRegList(List<WaRegisterRecordDo> clearList) {
        if (CollectionUtils.isEmpty(clearList) || CollectionUtils.isEmpty(this.waRegList)) {
            return;
        }
        if (clearList.size() > 10) {
            Set<WaRegisterRecordDo> toRemoveSet = new HashSet<>(clearList);
            this.waRegList.removeIf(toRemoveSet::contains);
        } else {
            this.waRegList.removeAll(clearList);
        }
    }

    public Map<String, WaShiftDo> filterEmpShiftDoMap(Long startDate, Long endDate) {
        if (MapUtils.isEmpty(this.empShiftDoMap) || startDate == null || endDate == null) {
            return new HashMap<>();
        }

        Map<String, WaShiftDo> result = this.empShiftDoMap.entrySet().stream()
                .filter(entry -> {
                    String[] parts = entry.getKey().split("_");
                    if (parts.length < 2) {
                        return false;
                    }
                    try {
                        long workDate = Long.parseLong(parts[1]);
                        return workDate >= startDate && workDate <= endDate;
                    } catch (NumberFormatException e) {
                        return false;
                    }
                }).collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue));

        result.keySet().forEach(this.empShiftDoMap::remove);

        return result;
    }

    public void clearEmpParseList(List<EmpParseGroup> clearList) {
        if (CollectionUtils.isEmpty(clearList) || CollectionUtils.isEmpty(this.empParseList)) {
            return;
        }
        if (clearList.size() > 10) {
            Set<EmpParseGroup> toRemoveSet = new HashSet<>(clearList);
            this.empParseList.removeIf(toRemoveSet::contains);
        } else {
            this.empParseList.removeAll(clearList);
        }
    }
}
