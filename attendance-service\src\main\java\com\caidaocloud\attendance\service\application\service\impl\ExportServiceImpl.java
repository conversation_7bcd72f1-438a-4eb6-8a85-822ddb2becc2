package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.TypeReference;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.integrate.entity.dto.RecordDto;
import com.caidao1.integrate.service.IntegratedLogicService;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.service.WaRegisterRecordService;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.config.ThreadExecutor;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.vo.MultiWorkTimeInfoSimpleVo;
import com.caidaocloud.attendance.service.application.dto.BaseHeaderDto;
import com.caidaocloud.attendance.service.application.dto.clock.ClockListShiftDefDto;
import com.caidaocloud.attendance.service.application.dto.overtime.OtLeftDurationDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.domain.entity.WaShiftDo;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.RegisterRecordMapper;
import com.caidaocloud.attendance.service.infrastructure.util.BeanMapUtils;
import com.caidaocloud.attendance.service.infrastructure.util.FileUtil;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.clock.EmpClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.dto.clock.EmpClockPlanReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpClockPlanDto;
import com.caidaocloud.attendance.service.interfaces.dto.export.ExportEmpShiftDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupDto;
import com.caidaocloud.attendance.service.interfaces.dto.group.AttEmpGroupReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.leave.BatchLeaveQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.LeaveExtensionDto;
import com.caidaocloud.attendance.service.interfaces.dto.leaveExtension.LeaveExtensionReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.BatchOvertimeQueryDto;
import com.caidaocloud.attendance.service.interfaces.dto.overtime.OtLeftDurationRequestDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.AnnualLeaveSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.CompensatoryQuotaSearchDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaEmpDto;
import com.caidaocloud.attendance.service.interfaces.dto.quota.QuotaPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.shift.ShiftPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.task.TaskDto;
import com.caidaocloud.attendance.service.interfaces.dto.task.UploadFileResult;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelDto;
import com.caidaocloud.attendance.service.interfaces.dto.travel.EmpTravelReqDto;
import com.caidaocloud.attendance.service.interfaces.dto.workHour.WorkingHourSettlementPageDto;
import com.caidaocloud.attendance.service.interfaces.dto.workflow.WorkflowRevokeReqDto;
import com.caidaocloud.attendance.service.interfaces.vo.BatchAnalyseResultAdjustPageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.leave.BatchLeavePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.overtime.BatchOvertimePageListVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.MultiShiftPageVo;
import com.caidaocloud.attendance.service.interfaces.vo.shift.ShiftPageVo;
import com.caidaocloud.attendance.service.interfaces.vo.workHour.EmpWorkingHoursSettlementVo;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IPerformanceService;
import com.caidaocloud.attendance.service.schedule.domain.entity.performance.PerformancePeriodDo;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.performance.PerformanceEmpQueryDto;
import com.caidaocloud.attendance.service.schedule.interfaces.dto.performance.RecordCheckQueryDto;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.performance.PerformanceEmpImportTempVo;
import com.caidaocloud.attendance.service.schedule.interfaces.vo.performance.PerformanceEmpVo;
import com.caidaocloud.attendance.service.wfm.application.dto.*;
import com.caidaocloud.attendance.service.wfm.application.service.IWorkingHourAnalyzeService;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmDayAnalysePageDto;
import com.caidaocloud.attendance.service.wfm.interfaces.dto.WfmMonthAnalysePageDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.oss.dto.UploadResult;
import com.caidaocloud.oss.service.OssService;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import io.swagger.annotations.ApiModelProperty;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import lombok.var;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import redis.clients.jedis.Jedis;

import javax.annotation.Resource;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @Author: Aaron.Chen
 * @Date: 2021/4/20 16:32
 * @Description:
 **/
@Slf4j
@Service
public class ExportServiceImpl implements IExportService {

    @Autowired
    private IRegisterRecordService registerRecordService;
    @Autowired
    private OssService ossService;
    @Autowired
    private ITaskService taskService;
    @Autowired
    private IShiftService shiftService;
    @Autowired
    private IStatisticsService statisticsService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private WorkOvertimeService workOvertimeService;
    @Autowired
    private IQuotaService quotaService;
    @Resource
    private AnnualLeaveService annualLeaveService;
    @Resource
    private EmpWorkCalendarService empWorkCalendarService;
    @Resource
    private IWorkCalendarService workCalendarService;
    @Autowired
    private IClockPlanService clockPlanService;
    @Autowired
    private WaEmpLeaveCancelService waEmpLeaveCancelService;
    @Resource
    private IGroupService groupService;
    @Autowired
    private IRegisterRecordBdkService registerRecordBdkService;
    @Autowired
    private IEmpTravelService empTravelService;
    @Autowired
    private IWorkflowRevokeService workflowRevokeService;
    @Autowired
    private WaBatchLeaveService waBatchLeaveService;
    @Autowired
    private WaBatchOvertimeService waBatchOvertimeService;
    @Autowired
    private ILeaveExtensionService leaveExtensionService;
    @Resource
    private DynamicFeignClient dynamicFeignClient;
    @Autowired
    private WaBatchAnalyseResultAdjustService waBatchAnalyseResultAdjustService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private IWorkingHourAnalyzeService workingHourAnalyzeService;
    @Resource
    private IPerformanceService performanceService;
    @Resource
    private PerformancePeriodDo performancePeriodDo;
    @Autowired
    private EmpWorkingHoursSettlementService empWorkingHoursSettlementService;

    private static final int PAGE_SIZE = 100000;

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                List<Map> dataList = getRegConvertList(requestDto, userInfo);
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出打卡记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }
    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadPerformanceData(PerformanceEmpQueryDto dto, TaskDto task, SecurityUserInfo securityUserInfo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(securityUserInfo.getUserId());
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setCorpid(Integer.valueOf(securityUserInfo.getTenantId()));
        try {
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            List<Map> dataList = performanceService.exportList(dto, securityUserInfo, false);
            List<BaseHeaderDto> head = new ArrayList<>();
            for (Field field : Arrays.stream(PerformanceEmpVo.class.getDeclaredFields()).filter(field -> !Arrays.asList("empId", "bid").contains(field.getName())).collect(Collectors.toList())) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                BaseHeaderDto baseHeaderDto = new BaseHeaderDto();
                baseHeaderDto.setValue(annotation.value());
                baseHeaderDto.setId(field.getName());
                head.add(baseHeaderDto);
            }
            PerformancePeriodDo performancePeriod = performancePeriodDo.getByBId(dto.getPerformancePeriodBid().get(0));
            String period = performancePeriod.getPeriod();
            Long startDate = performancePeriod.getStartDate();
            Long endDate = performancePeriod.getEndDate();
            String slot = period.substring(0, 4) + "-" + period.substring(4, 6)
                    + "(" + com.caidaocloud.util.DateUtil.format(startDate, "yyyy.MM.dd") + " ~ " + com.caidaocloud.util.DateUtil.format(endDate, "yyyy.MM.dd") + ")";
            upload(1, 1, "绩效评分周期人员_" + slot, "xlsx", head, dataList, task, userInfo);
        } catch (Exception ie) {
            log.error("导出绩效记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    @Resource
    private IntegratedLogicService integratedLogicService;
    @Resource
    private WaRegisterRecordService waRegisterRecordService;
    @Resource
    private RegisterRecordMapper registerRecordMapper;

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadKaoQinJiRecordCheckData(RecordCheckQueryDto dto, TaskDto task, SecurityUserInfo securityUserInfo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(securityUserInfo.getUserId());
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setCorpid(Integer.valueOf(securityUserInfo.getTenantId()));
        try {
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            Map params = new HashMap<>();
            params.put("tenantId", userInfo.getTenantId());
            params.put("startDate", dto.getStartDate());
            params.put("endDate", dto.getEndDate());
            if (dto.getWorkNo() != null) {
                Jedis jedis = RedisService.getResource();
                String key = BaseConst.EMP_ + userInfo.getTenantId() + "_" + dto.getWorkNo();
                String empInfo = jedis.get(key);
                if (empInfo != null) {
                    Long empId = Long.parseLong(String.valueOf(empInfo.split(",")[0]).replace("\"", ""));
                    params.put("empId", empId);
                }
            }
            List<RecordDto> dbRecords = registerRecordMapper.selectKqjRecord(params);
            Map<Long, List<Long>> dayEmpRecordTimeMap = new HashMap<>();
            for (RecordDto dbRecord : dbRecords) {
                Long empId = dbRecord.getEmpId();
                Long registerTime = dbRecord.getRegisterTime();
                List<Long> recordTimeList = dayEmpRecordTimeMap.getOrDefault(empId, new ArrayList<>());
                recordTimeList.add(registerTime);
                dayEmpRecordTimeMap.put(empId, recordTimeList);
            }

            List<Map> excelList = new ArrayList<>();
            Map<Long, Integer> empStatsMap = integratedLogicService.initEmp(userInfo.getTenantId());
            List<RecordDto> kqjRecordList = integratedLogicService.getKqjRecord(dto.getStartDate(), dto.getEndDate(), dto.getWorkNo(), dto.getEmpStats().contains("1"), userInfo.getTenantId(), empStatsMap);
            for (RecordDto recordDto : kqjRecordList) {
                Map map = new HashMap<>();
                map.put("workNo", recordDto.getWorkNo());
                map.put("name", recordDto.getName());
                map.put("regTimeStr", recordDto.getRegTimeStr());
                map.put("deviceNumber", recordDto.getDeviceNumber());
                map.put("sourceForm", "考勤机");
                Long empId = recordDto.getEmpId();
                Long registerTime = recordDto.getRegisterTime();
                if (!dayEmpRecordTimeMap.containsKey(empId)) {
                    map.put("systemExists", "否");
                } else {
                    List<Long> recordTimeList = dayEmpRecordTimeMap.get(empId);
                    if (recordTimeList.contains(registerTime)) {
                        map.put("systemExists", "是");
                    } else {
                        map.put("systemExists", "否");
                    }
                }
                excelList.add(map);
            }
            List<RecordDto> gyRecordList = integratedLogicService.getGyRecord(dto.getStartDate(), dto.getEndDate(), dto.getWorkNo(), dto.getEmpStats().contains("1"), userInfo.getTenantId(), empStatsMap);
            for (RecordDto recordDto : gyRecordList) {
                Map map = new HashMap<>();
                map.put("workNo", recordDto.getWorkNo());
                map.put("name", recordDto.getName());
                map.put("regTimeStr", recordDto.getRegTimeStr());
                map.put("deviceNumber", recordDto.getDeviceNumber());
                map.put("sourceForm", "盖雅");
                Long empId = recordDto.getEmpId();
                Long registerTime = recordDto.getRegisterTime();
                if (!dayEmpRecordTimeMap.containsKey(empId)) {
                    map.put("systemExists", "否");
                } else {
                    List<Long> recordTimeList = dayEmpRecordTimeMap.get(empId);
                    if (recordTimeList.contains(registerTime)) {
                        map.put("systemExists", "是");
                    } else {
                        map.put("systemExists", "否");
                    }
                }
                excelList.add(map);
            }
            List<BaseHeaderDto> head = new ArrayList<>();
            for (Field field : Arrays.stream(RecordDto.class.getDeclaredFields()).collect(Collectors.toList())) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                if (annotation != null) {
                    BaseHeaderDto baseHeaderDto = new BaseHeaderDto();
                    baseHeaderDto.setValue(annotation.value());
                    baseHeaderDto.setId(field.getName());
                    head.add(baseHeaderDto);
                }
            }
            upload(1, 1, "考勤机数据接入比对", "xlsx", head, excelList, task, userInfo);
        } catch (Exception ie) {
            log.error("导出考勤机对比数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }

    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadPerformanceImportTemplateData(PerformanceEmpQueryDto dto, TaskDto task, SecurityUserInfo securityUserInfo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setUserId(securityUserInfo.getUserId());
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setCorpid(Integer.valueOf(securityUserInfo.getTenantId()));
        try {
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            List<Map> dataList = performanceService.exportList(dto, securityUserInfo, true);
            List<BaseHeaderDto> head = new ArrayList<>();
            for (Field field : Arrays.stream(PerformanceEmpImportTempVo.class.getDeclaredFields()).collect(Collectors.toList())) {
                ApiModelProperty annotation = field.getAnnotation(ApiModelProperty.class);
                BaseHeaderDto baseHeaderDto = new BaseHeaderDto();
                baseHeaderDto.setValue(annotation.value());
                baseHeaderDto.setId(field.getName());
                head.add(baseHeaderDto);
            }
            PerformancePeriodDo performancePeriod = performancePeriodDo.getByBId(dto.getPerformancePeriodBid().get(0));
            String period = performancePeriod.getPeriod();
            Long startDate = performancePeriod.getStartDate();
            Long endDate = performancePeriod.getEndDate();
            String slot = period.substring(0, 4) + "-" + period.substring(4, 6)
                    + "(" + com.caidaocloud.util.DateUtil.format(startDate, "yyyy.MM.dd") + " ~ " + com.caidaocloud.util.DateUtil.format(endDate, "yyyy.MM.dd") + ")";

            upload(1, 1, "绩效员工导入模板_" + slot, "xlsx", head, dataList, task, userInfo);
        } catch (Exception ie) {
            log.error("导出绩效导入模板记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        }finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    private List<Map> getRegConvertList(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setIfExport(true);
        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto, userInfo);
        List<Map> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<Map> mapList = pageResult.getItems().stream()
                    .map(itDto -> {
                        Map row = FastjsonUtil.convertObject(itDto, Map.class);
                        if (row.containsKey("regDateTime") && row.get("regDateTime") != null) {
                            row.put("regDateTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("regDateTime").toString())));
                        }
                        if (row.containsKey("approvalTime") && row.get("approvalTime") != null) {
                            row.put("approvalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("approvalTime").toString())));
                        }
                        if (row.containsKey("belongDate") && row.get("belongDate") != null) {
                            row.put("belongDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("belongDate").toString())));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName())) {
                            if (row.containsKey("workno") && row.get("workno") != null) {
                                row.put("empName", row.get("workno") + "(" + row.get("empName") + ")");
                            }
                        }
                        if (row.containsKey("fullPath") && row.get("fullPath") != null) {
                            row.put("orgName", row.get("fullPath"));
                        }
                        if (row.containsKey("hireDate") && row.get("hireDate") != null) {
                            row.put("hireDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("hireDate").toString())));
                        }
                        if (row.containsKey("userName") && row.get("userName") != null) {
                            row.put("userName", row.get("userName"));
                        }
                        if (row.containsKey("crttime") && row.get("crttime") != null) {
                            row.put("crtTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("crttime").toString())));
                        }
                        if (row.containsKey("clockSiteStatus") && row.get("clockSiteStatus") != null) {
                            row.put("clockSiteStatus", ValidStatusEnum.getName((Integer) row.get("clockSiteStatus")));
                        }
                        // 班次
                        if (null != itDto.getShiftDefList()) {
                            row.put("shiftDefList", ClockListShiftDefDto.getShiftTxtForExport(itDto.getShiftDefList()));
                        }
                        return row;
                    }).collect(Collectors.toList());
            dataList.addAll(mapList);
        }
        return dataList;
    }

    private int getCount(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        requestDto.setIfExport(true);
        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto, userInfo);
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadBdkData(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getBdkCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                List<Map> dataList = getBdkRegConvertList(requestDto, userInfo);
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出补卡数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private List<Map> getBdkRegConvertList(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setIfExport(true);
        AttendancePageResult<RegisterRecordBdkDto> pageResult = registerRecordBdkService.getRegisterRecordPageList(requestDto, userInfo);
        List<Map> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<Map> mapList = pageResult.getItems().stream()
                    .map(itDto -> {
                        Map row = FastjsonUtil.convertObject(itDto, Map.class);
                        if (row.containsKey("regDateTime") && row.get("regDateTime") != null) {
                            row.put("regDateTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("regDateTime").toString())));
                        }
                        if (row.containsKey("approvalTime") && row.get("approvalTime") != null) {
                            row.put("approvalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("approvalTime").toString())));
                        }
                        if (row.containsKey("belongDate") && row.get("belongDate") != null) {
                            row.put("belongDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("belongDate").toString())));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName())) {
                            if (row.containsKey("workno") && row.get("workno") != null) {
                                row.put("empName", row.get("workno") + "(" + row.get("empName") + ")");
                            }
                        }
                        if (row.containsKey("fullPath") && row.get("fullPath") != null) {
                            row.put("orgName", row.get("fullPath"));
                        }
                        if (row.containsKey("hireDate") && row.get("hireDate") != null) {
                            row.put("hireDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("hireDate").toString())));
                        }
                        if (row.containsKey("userName") && row.get("userName") != null) {
                            row.put("userName", row.get("userName"));
                        }
                        if (row.containsKey("crttime") && row.get("crttime") != null) {
                            row.put("crtTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("crttime").toString())));
                        }
                        if (row.containsKey("clockSiteStatus") && row.get("clockSiteStatus") != null) {
                            row.put("clockSiteStatus", ValidStatusEnum.getName((Integer) row.get("clockSiteStatus")));
                        }
                        if (row.containsKey("regDateTimes") && row.get("regDateTimes") != null) {
                            row.put("regDateTimes", StringUtils.join(Arrays.stream(row.get("regDateTimes").toString().split(",")).map(r -> DateUtil.getTimeStrByTimesamp(Long.valueOf(r))).collect(Collectors.toList()), "\n"));
                        }
                        // 班次
                        if (null != itDto.getShiftDefList()) {
                            row.put("shiftDefList", ClockListShiftDefDto.getShiftTxtForExport(itDto.getShiftDefList()));
                        }
                        return row;
                    }).collect(Collectors.toList());
            dataList.addAll(mapList);
        }
        return dataList;
    }

    private int getBdkCount(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        requestDto.setIfExport(true);
        AttendancePageResult<RegisterRecordBdkDto> pageResult = registerRecordBdkService.getRegisterRecordPageList(requestDto, userInfo);
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(ShiftPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getShiftCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                AttendancePageResult<WaShiftDo> pageResult = shiftService.getPageList(requestDto, userInfo);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> mapList = new ArrayList<>();
                if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
                    if (ShiftBelongModuleEnum.WFM.getCode().equals(requestDto.getBelongModule())) {
                        List<MultiShiftPageVo> voList = ObjectConverter.convertList(pageResult.getItems(), MultiShiftPageVo.class);
                        voList.forEach(MultiShiftPageVo::doSetMultiWorkTimes);
                        mapList = FastjsonUtil.convertList(voList, Map.class);
                    } else {
                        List<ShiftPageVo> voList = ObjectConverter.convertList(pageResult.getItems(), ShiftPageVo.class);
                        mapList = FastjsonUtil.convertList(voList, Map.class);
                    }
                    mapList.forEach(this::convertShiftData);
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), mapList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出班次数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出班次数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private void convertShiftData(Map data) {
        Boolean isNight = Optional.ofNullable((Boolean) data.get("isNight")).orElse(Boolean.FALSE);
        data.put("isNight",
                isNight ? MessageHandler.getMessage("caidao.exception.error_2018170", WebUtil.getRequest())
                        : MessageHandler.getMessage("caidao.exception.error_2018171", WebUtil.getRequest()));

        if (data.containsKey("dateType") && data.get("dateType") != null) {
            data.put("dateType", DateTypeEnum.getName(Integer.valueOf(data.get("dateType").toString())));
        }
        if (data.containsKey("workTotalTime") && data.get("workTotalTime") != null) {
            Integer workTotalTime = (Integer) data.get("workTotalTime");
            if (workTotalTime > 0) {
                BigDecimal workTime = new BigDecimal((Integer) data.get("workTotalTime"));
                data.put("workTotalTime", workTime.divide(new BigDecimal(60), 1, BigDecimal.ROUND_DOWN).floatValue());
            }
        }
        List<String> clockList = new ArrayList<>(Arrays.asList("startTime", "endTime", "noonRestStart", "noonRestEnd",
                "onDutyStartTime", "onDutyEndTime", "offDutyStartTime", "offDutyEndTime", "overtimeStartTime", "overtimeEndTime"));
        clockList.forEach(key -> {
            if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(data, key)) {
                Integer value = Integer.valueOf(data.get(key).toString());
                data.put(key, DateUtil.convertMinuteToTime(value));
            }
        });
    }

    private int getShiftCount(ShiftPageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        AttendancePageResult<WaShiftDo> pageResult = shiftService.getPageList(requestDto, userInfo);
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(LeaveApplyDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getLeaveCount(requestDto, userInfo);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> dataList = new ArrayList<>();
                List<Map> list = (PageList<Map>) leaveApplyService.getLeaveApplyList(requestDto, pageBean, userInfo);
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(originRow -> {
                        Map data = BeanMapUtils.formatHumpName(originRow);
                        if (data.containsKey("crttime") && data.get("crttime") != null) {
                            data.put("crttime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("crttime").toString())));
                        }
                        if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                            data.put("shortname", data.get("fullPath"));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && data.containsKey("workno") && data.get("workno") != null) {
                            data.put("empName", data.get("workno") + "(" + data.get("empName") + ")");
                        }
                        if (data.containsKey("lastApprovalTime") && data.get("lastApprovalTime") != null) {
                            data.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("lastApprovalTime").toString())));
                        }
                        if (data.containsKey("timeSlot") && data.get("timeSlot") != null) {
                            data.put("timeSlot", String.valueOf(data.get("timeSlot")).replace("->", "~"));
                        }
                        if (data.containsKey("startDate") && data.get("startDate") != null) {
                            data.put("startDate", data.get("startDate").toString());
                        }
                        if (data.containsKey("endDate") && data.get("endDate") != null) {
                            data.put("endDate", data.get("endDate").toString());
                        }
                        if (data.containsKey("cancelPeriods") && data.get("cancelPeriods") != null) {
                            List<LeaveCancelPeriod> cancelPeriods = (List<LeaveCancelPeriod>) data.get("cancelPeriods");
                            if (CollectionUtils.isNotEmpty(cancelPeriods)) {
                                List<String> periods = new ArrayList<>();
                                for (LeaveCancelPeriod cancelPeriod : cancelPeriods) {
                                    if (cancelPeriod.getShiftStartTime() != null && cancelPeriod.getShiftEndTime() != null) {
                                        String str = cancelPeriod.getShiftStartTime() + "~" + cancelPeriod.getShiftEndTime();
                                        periods.add(str);
                                    }
                                }
                                String timeStr = StringUtils.join(periods, "\n");
                                data.put("cancelPeriods", timeStr);
                            }
                        }
                        Integer approvalStatus = Integer.valueOf(data.get("status").toString());
                        if (ApprovalStatusEnum.PASSED.getIndex().equals(approvalStatus)) {
                            if (data.containsKey("leaveStatus") && data.get("leaveStatus") != null) {
                                Integer leaveStatus = (Integer) data.get("leaveStatus");
                                data.put("leaveStatus", LeaveCancelStatusEnum.getName(leaveStatus));
                            }
                        } else {
                            data.put("leaveStatus", "-");
                        }
                        if (data.containsKey("duration") && data.get("duration") != null) {
                            data.put("duration", data.get("duration").toString() + data.get("timeUnitName"));
                            data.put("cancelTimeDuration", data.get("cancelTimeDuration").toString() + data.get("timeUnitName"));
                            data.put("actualTimeDuration", data.get("actualTimeDuration").toString() + data.get("timeUnitName"));
                            data.remove("timeUnitName");
                        }
                        dataList.add(data);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出休假记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出休假记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadLeaveCancelData(LeaveCancelReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            requestDto.setAll(false);
            com.caidaocloud.dto.PageResult<LeaveCancelListDto> pageList = waEmpLeaveCancelService.getPageList(requestDto, userInfo);
            int total = pageList.getTotal();
            log.info("导出员工销假申请数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                com.caidaocloud.dto.PageResult<LeaveCancelListDto> pageResult = waEmpLeaveCancelService.getPageList(requestDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
                    pageResult.getItems().forEach(originRow -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("workno", originRow.getWorkNo());
                        row.put("empName", originRow.getEmpName());
                        row.put("orgName", StringUtils.isNotBlank(originRow.getFullPath()) ? originRow.getFullPath() : originRow.getOrgName());
                        row.put("leaveTypeName", originRow.getLeaveTypeName());
                        row.put("leaveCancelTypeName", originRow.getLeaveCancelTypeName());
                        if (originRow.getTimeDuration() != null && originRow.getTimeUnit() != null) {
                            row.put("timeDuration", originRow.getTimeDuration() + LeaveTypeUnitEnum.getName(originRow.getTimeUnit()));
                        }
                        row.put("reason", originRow.getReason());
                        if (originRow.getCreateTime() != null) {
                            row.put("createTime", DateUtil.getDateStrByTimesamp(originRow.getCreateTime()));
                        }
                        row.put("statusName", originRow.getStatusName());
                        if (originRow.getLastApprovalTime() != null) {
                            row.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(originRow.getLastApprovalTime()));
                        }
                        if (null != originRow.getOriginalLeaveDate()) {
                            row.put("originalLeaveDate", originRow.getOriginalLeaveDate());
                        }
                        List<LeaveCancelPeriod> cancelPeriods = originRow.getCancelPeriods();
                        if (CollectionUtils.isNotEmpty(cancelPeriods)) {
                            List<String> list = new ArrayList<>();
                            for (LeaveCancelPeriod cancelPeriod : cancelPeriods) {
                                if (cancelPeriod.getShiftStartTime() != null && cancelPeriod.getShiftEndTime() != null) {
                                    String str = cancelPeriod.getShiftStartTime() + "~" + cancelPeriod.getShiftEndTime();
                                    list.add(str);
                                }
                            }
                            String timeStr = StringUtils.join(list, "\n");
                            row.put("cancelPeriods", timeStr);
                        }
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出销假记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("导出销假记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getLeaveCount(LeaveApplyDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<Map> list = (PageList<Map>) leaveApplyService.getLeaveApplyList(requestDto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        AttendancePageResult pageResult = new AttendancePageResult<>(list, list.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(OvertimeApplyDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getOverTimeCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> list = workOvertimeService.getOvertimeList(userInfo.getTenantId(), pageBean, null, requestDto.getStartDate(), requestDto.getStartTime(), requestDto.getEndDate(), requestDto.getEndTime(), requestDto.getDataScope());
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(originRow -> {
                        Map data = BeanMapUtils.formatHumpName(originRow);
                        if (data.containsKey("crttime") && data.get("crttime") != null) {
                            data.put("crttime", DateUtil.getDateStrByTimesamp(Long.valueOf(data.get("crttime").toString())));
                        }
                        if (data.containsKey("startTime") && data.get("startTime") != null) {
                            data.put("startTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("startTime").toString())));
                        }
                        if (data.containsKey("endTime") && data.get("endTime") != null) {
                            data.put("endTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("endTime").toString())));
                        }
                        if (data.containsKey("lastApprovalTime") && data.get("lastApprovalTime") != null) {
                            data.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("lastApprovalTime").toString())));
                        }
                        if (data.containsKey("dateType") && data.get("dateType") != null) {
                            data.put("dateType", DateTypeEnum.getName(Integer.valueOf(data.get("dateType").toString())));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && data.containsKey("workno") && data.get("workno") != null) {
                            data.put("empName", data.get("workno") + "(" + data.get("empName") + ")");
                        }
                        if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                            data.put("shortname", data.get("fullPath"));
                        }
                        if (originRow.containsKey("transfer_duration") && originRow.get("transfer_duration") != null) {
                            data.put("transferDuration", originRow.get("transfer_duration"));
                        }
                        if (originRow.containsKey("transfer_unit") && originRow.get("transfer_unit") != null) {
                            Integer transferUnit = (Integer) originRow.get("transfer_unit");
                            data.put("transferUnit", PreTimeUnitEnum.getName(transferUnit));
                        }
                        dataList.add(data);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出加班记录列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(QuotaPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getEmpQuotaCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                pageBean.setFilterList(requestDto.getFilterList());
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);

                List<Map> list = quotaService.getQuotaList(pageBean, requestDto.getYear(), requestDto.getQuotaSettingId(), userInfo.getTenantId(), requestDto.getYears(), requestDto.getDataScope());

//                List<Map> list = waConfigService.getEmpQuotaList(pageBean, requestDto.getYear(), requestDto.getQuotaSettingId(), userInfo.getBelongOrgId(),requestDto.getYears());
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(map -> {
                        Map data = BeanMapUtils.formatHumpName(map);
                        checkDate(data, "startDate");
                        checkDate(data, "lastDate");
                        checkDate(data, "remainValidDate");
                        checkDate(data, "hireDate");
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName())) {
                            if (data.containsKey("workno") && data.get("workno") != null) {
                                data.put("empName", data.get("workno") + "(" + data.get("empName") + ")");
                            }
                        }
                        dataList.add(data);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出假期配额列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private void checkDate(Map map, String key) {
        Long convert = ConvertHelper.longConvert(map.get(key));
        map.put(key, convert != null && convert != 0 ? DateUtil.getDateStrByTimesamp(convert) : "");
    }

    @Override
    public void uploadData(DayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getDayCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            requestDto.setIsCount(Boolean.FALSE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            List<BaseHeaderDto> head = statisticsService.getDayHeadersForExport(requestDto.getSobId(), userInfo);
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                pageBean.setFilterList(requestDto.getFilterList());
                List<Map> list = statisticsService.getDayAnalyseList(requestDto, pageBean, userInfo);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(data -> {
                        if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                            data.put("org_name", data.get("fullPath"));
                        }
                        if (data.containsKey("hire_date") && data.get("hire_date") != null) {
                            data.put("hire_date", DateUtil.getDateStrByTimesamp((Long) data.get("hire_date")));
                        }
                        if (data.containsKey("belong_date") && data.get("belong_date") != null) {
                            data.put("belong_date", DateUtil.getDateStrByTimesamp((Long) data.get("belong_date")));
                        }
                        if (data.containsKey("reg_date_time") && data.get("reg_date_time") != null) {
                            data.put("reg_date_time", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("reg_date_time").toString())));
                        }
                        if (data.containsKey("reg_date_time2") && data.get("reg_date_time2") != null) {
                            data.put("reg_date_time2", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("reg_date_time2").toString())));
                        }
                        if (data.containsKey("analyze_result") && data.get("analyze_result") != null) {
                            Integer analyzeResult = Integer.valueOf(String.valueOf(data.get("analyze_result")));
                            data.put("analyze_result", analyzeResult == 0 ? "正常" : "异常");
                        }
                        if (data.containsKey("emp_name") && data.get("emp_name") != null) {
                            data.put("emp_name", data.get("workno") + "(" + data.get("emp_name") + ")");
                        }
                        if (data.containsKey("belong_date") && data.get("belong_date") != null) {
                            data.put("belong_date", data.get("belong_date") + " " + data.get("belong_date_week"));
                        }

                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), head, list, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出考勤统计每日明细列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }


    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadData(MonthAnalysePageDto requestDto, TaskDto task, Boolean summary, UserInfo userInfo, Locale locale) {
        try {
            int total = getMonthAnalyseCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            // 多线程导出考勤汇总
            ThreadPoolExecutor poolExecutor = ThreadExecutor.getThreadPool();
            CountDownLatch downLatch = new CountDownLatch(totalPage);
            for (int i = 0; i < totalPage; i++) {
                poolExecutor.execute(() -> {
                    try {
                        ResponseWrap.setThreadLocale(locale);
                        List<BaseHeaderDto> head = statisticsService.getMonthHeadersForExport(requestDto.getGroupId(), userInfo,
                                new ImmutablePair<>(requestDto.getStartDate(), requestDto.getEndDate()));
                        PageBean pageBean = PageUtil.getPageBean(requestDto);
                        pageBean.setFilterList(requestDto.getFilterList());
                        int pageNo = requestDto.getPageNo();
                        requestDto.setPageNo(pageNo + 1);
                        List<Map> list = summary ? statisticsService.searchRegisterStatisticsAdvance(requestDto, pageBean, userInfo) :
                                statisticsService.searchRegisterStatistics(requestDto, pageBean, userInfo);
                        if (CollectionUtils.isNotEmpty(list)) {
                            list.forEach(data -> {
                                if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                                    data.put("org_name", data.get("fullPath"));
                                }
                                if (data.containsKey("hire_date") && data.get("hire_date") != null) {
                                    data.put("hire_date", DateUtil.getDateStrByTimesamp((Long) data.get("hire_date")));
                                }
                                if (data.containsKey("status") && data.get("status") != null) {
                                    Integer status = (Integer) data.get("status");
                                    data.put("status", status == 1 ? "正常" : "异常");
                                }
                                convertSummaryData(head, data);
                            });
                        }
                        upload(downLatch, pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), head, list, task, userInfo);
                    } finally {
                        ResponseWrap.clearThreadLocale();
                    }
                });
            }
        } catch (Exception ie) {
            log.error("导出考勤统计月度汇总分页列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        }
    }

    /**
     * 月度统计导出-动态列
     *
     * @param requestDto
     * @param task
     * @param summary
     * @param userInfo
     * @param locale
     */
    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadDataByDynamic(MonthAnalysePageDto requestDto, TaskDto task, Boolean summary, UserInfo userInfo, Locale locale) {
        try {
            ResponseWrap.setThreadLocale(locale);
            int total = getMonthAnalyseCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                SecurityUserInfo securityUserInfo = new SecurityUserInfo();
                try {
                    securityUserInfo.setUserId(userInfo.getUserId());
                    securityUserInfo.setTenantId(userInfo.getTenantId());
                    securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                    SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                    List<String> col = initTimeStampColumn("MOUTHLYSUM");
                    List<BaseHeaderDto> head = statisticsService.getMonthHeadersByDynamicForExport(requestDto.getGroupId(), userInfo,
                            new ImmutablePair<>(requestDto.getStartDate(), requestDto.getEndDate()));
                    PageBean pageBean = PageUtil.getPageBean(requestDto);
                    pageBean.setFilterList(requestDto.getFilterList());
                    int pageNo = requestDto.getPageNo();
                    requestDto.setPageNo(pageNo + 1);
                    DynamicPageDto mouthAnalyseListForDynamic = statisticsService.getMouthAnalyseListForDynamic(requestDto, pageBean, userInfo);
                    List<Map> list = mouthAnalyseListForDynamic.getPageData().getItems();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    if (CollectionUtils.isNotEmpty(list)) {
                        list.forEach(data -> {
                            for (String timeCol : col) {
                                if (data.containsKey(timeCol) && data.get(timeCol) != null) {
                                    data.put(timeCol, sdf.format(new Date(Long.valueOf((String) data.get(timeCol)))));
                                }
                            }
                            if (data.containsKey("status") && data.get("status") != null) {
                                Integer status = (Integer) data.get("status");
                                data.put("status", ClockResultEnum.getName(status));
                            }
                            convertSummaryData(head, data);
                        });
                    }
                    upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), head, list, task, userInfo);
                } finally {
                    SecurityUserUtil.removeSecurityUserInfo();
                }
            }
        } catch (Exception ie) {
            log.error("导出考勤统计月度汇总动态分页列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private void convertSummaryData(List<BaseHeaderDto> headKey, Map<String, Object> dataMap) {
        if (dataMap != null && !dataMap.isEmpty()) {
            headKey.forEach(it -> {
                final String itemKey = it.getId();
                Pattern summaryPattern = Pattern.compile("^summary_\\w+$");
                Matcher matcher = summaryPattern.matcher(itemKey);
                if (matcher.find()) {
                    Object itemValue = dataMap.getOrDefault(itemKey, null);
                    if (itemValue == null || StringUtils.isEmpty(Objects.toString(itemValue)) || "approved_bdk".equals(itemValue.toString())) {
                        dataMap.put(itemKey, "✅");
                        if ("approved_bdk".equals(String.valueOf(itemValue))) {
                            dataMap.put(itemKey, "✅\n" + CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
                        }
                    } else {
                        List<RegisterRecordDto> recordDtos = (List<RegisterRecordDto>) dataMap.getOrDefault(itemKey.replace("summary", "record"), null);
                        if (CollectionUtils.isNotEmpty(recordDtos)) {
                            String summaryTxt = Objects.toString(dataMap.getOrDefault(itemKey, ""));
                            StringBuilder builder = new StringBuilder(summaryTxt.replace(",", "\n"));
                            recordDtos.forEach(o1 -> {
                                builder.append("\n");
                                String registerTypeName = Optional.ofNullable(o1.getRegisterTypeName())
                                        .orElseGet(() -> o1.getRegisterType() != null ? o1.getRegisterType() == 1 ? "签到" : "签退" : "打卡");
                                builder.append(registerTypeName).append(":").append(DateUtil.convertDateTimeToStr(o1.getRegDateTime(), "HH:mm:ss", true));
                            });
                            dataMap.put(itemKey, builder.toString());
                        }
                    }
                }
            });
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadEmpFixQuotaData(FixQuotaSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            dto.setAll(false);
            int total = quotaService.getEmpFixQuotaList(dto, userInfo).getTotal();
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            dto.setPageSize(PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                int pageNo = dto.getPageNo();
                val list = quotaService.getEmpFixQuotaList(dto, userInfo).getItems();
                dto.setPageNo(pageNo + 1);
                List<Map> dataList = list.stream().map(fixQuota -> {
                    val map = FastjsonUtil
                            .toObject(FastjsonUtil.toJson(fixQuota), HashMap.class);
                    if (null != fixQuota.getHireDate()) {
                        map.put("hireDate", DateUtil.parseDateToPattern(new Date(fixQuota.getHireDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != fixQuota.getTerminationDate()) {
                        map.put("terminationDate", DateUtil.parseDateToPattern(new Date(fixQuota.getTerminationDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != fixQuota.getStartDate()) {
                        map.put("startDate", DateUtil.parseDateToPattern(new Date(fixQuota.getStartDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != fixQuota.getStatus()) {
                        map.put("status", ValidStatusEnum.getName(fixQuota.getStatus()));
                    }
                    for (Object key : map.keySet()) {
                        val value = map.get(key);
                        if (value != null && value instanceof BigDecimal) {
                            map.put(key, (BigDecimal) value);
                        }
                    }
                    return map;
                }).collect(Collectors.toList());
                upload(pageNo, totalPage, dto.getFileName(), dto.getType(), dto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出固定额度数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadEmpCompensatoryQuotaData(CompensatoryQuotaSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            dto.setAll(false);
            CompensatoryQuotaSearchDto requestDto = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(dto)), CompensatoryQuotaSearchDto.class);
            int total = quotaService.getEmpCompensatoryQuotaList(requestDto, userInfo).getTotal();
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            dto.setPageSize(PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                int pageNo = dto.getPageNo();
                val list = quotaService.getEmpCompensatoryQuotaList(dto, userInfo).getItems();
                dto.setPageNo(pageNo + 1);
                List<Map> dataList = list.stream().map(compensatoryQuota -> {
                    val map = FastjsonUtil
                            .toObject(FastjsonUtil.toJson(compensatoryQuota), HashMap.class);
                    if (null != compensatoryQuota.getOvertimeDate()) {
                        map.put("overtimeDate", DateUtil.parseDateToPattern(new Date(compensatoryQuota.getOvertimeDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != compensatoryQuota.getStartDate()) {
                        map.put("startDate", DateUtil.parseDateToPattern(new Date(compensatoryQuota.getStartDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != compensatoryQuota.getLastDate()) {
                        map.put("lastDate", DateUtil.parseDateToPattern(new Date(compensatoryQuota.getLastDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != compensatoryQuota.getStatus()) {
                        map.put("status", ValidStatusEnum.getName(compensatoryQuota.getStatus()));
                    }
                    if (null != compensatoryQuota.getOvertimeType()) {
                        map.put("overtimeType", OtTypeEnum.getDescByIndex(compensatoryQuota.getOvertimeType()));
                    }
                    if (null != compensatoryQuota.getOvertimeUnit()) {
                        map.put("overtimeUnit", PreTimeUnitEnum.getName(compensatoryQuota.getOvertimeUnit()));
                    }
                    if (null != compensatoryQuota.getQuotaUnit()) {
                        map.put("quotaUnit", PreTimeUnitEnum.getName(compensatoryQuota.getQuotaUnit()));
                    }
                    val workingTime = compensatoryQuota.getWorkingTime();
                    if (workingTime != null) {
                        var newValue = new BigDecimal(workingTime);
                        if (newValue.precision() > 2) {
                            newValue = newValue.setScale(2, RoundingMode.HALF_UP);
                        }
                        map.put("workingTime", "1" + PreTimeUnitEnum.getName(1) + "=" + newValue + PreTimeUnitEnum.getName(2));

                    }
                    int quotaUnit = Optional.ofNullable(compensatoryQuota.getQuotaUnit()).orElse(2);
                    for (Object key : map.keySet()) {
                        val value = map.get(key);
                        if (value instanceof BigDecimal) {
                            var newValue = new BigDecimal(((BigDecimal) value).toPlainString());
                            if (quotaUnit == 2 && newValue.precision() > 2) {
                                newValue = newValue.setScale(2, RoundingMode.HALF_UP);
                            }
                            map.put(key, newValue.toString());

                        }
                    }
                    return map;
                }).collect(Collectors.toList());
                upload(pageNo, totalPage, dto.getFileName(), dto.getType(), dto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出调休额度数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadAnnualQuotaData(AnnualLeaveSearchDto dto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            dto.setAll(false);
            AnnualLeaveSearchDto requestDto = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(dto)), AnnualLeaveSearchDto.class);
            int total = annualLeaveService.pageList(requestDto, null, userInfo).getPaginator().getTotalCount();
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            dto.setPageSize(PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                int pageNo = dto.getPageNo();
                val list = annualLeaveService.pageList(dto, null, userInfo);
                dto.setPageNo(pageNo + 1);
                List<Map> dataList = list.stream().map(annualLeaveQuota -> {
                    val map = FastjsonUtil
                            .toObject(FastjsonUtil.toJson(annualLeaveQuota), HashMap.class);
                    if (null != annualLeaveQuota.getHireDate()) {
                        map.put("hireDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getHireDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getFirstWorkDate()) {
                        map.put("firstWorkDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getFirstWorkDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getDisCycleStart() && null != annualLeaveQuota.getDisCycleEnd()) {
                        map.put("disCycleStart",
                                DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getDisCycleStart() * 1000),
                                        "yyyy-MM-dd") + "~" +
                                        DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getDisCycleEnd() * 1000),
                                                "yyyy-MM-dd"));
                    } else {
                        map.put("disCycleStart", "");
                    }
                    if (null != annualLeaveQuota.getStartDate()) {
                        map.put("startDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getStartDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getLastDate()) {
                        map.put("lastDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getLastDate() * 1000),
                                "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getTerminationDate()) {
                        map.put("terminationDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getTerminationDate() * 1000), "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getValidityUnit()) {
                        map.put("validityUnit", ValidityUnitEnum.getName(annualLeaveQuota.getValidityUnit()));
                    }
                    if (null != annualLeaveQuota.getIfAdvance()) {
                        map.put("ifAdvance", QuotaAdvanceEnum.getName(annualLeaveQuota.getIfAdvance()));
                    }
                    if (null != annualLeaveQuota.getAcctTimeType()) {
                        map.put("acctTimeType", PreTimeUnitEnum.getName(annualLeaveQuota.getAcctTimeType()));
                    }
                    if (null != annualLeaveQuota.getRetainValidDate()) {
                        map.put("retainValidDate", DateUtil.parseDateToPattern(new Date(annualLeaveQuota.getRetainValidDate() * 1000), "yyyy-MM-dd"));
                    }
                    if (null != annualLeaveQuota.getInTransitQuota() && null != annualLeaveQuota.getRetainInTransitQuota()) {
                        map.put("inTransitQuota", annualLeaveQuota.getInTransitQuota() + annualLeaveQuota.getRetainInTransitQuota());
                    }
                    map.put("annualLeaveStatus", annualLeaveQuota.getAnnualLeaveStatus() ? ValidStatusEnum.getName(1) : ValidStatusEnum.getName(0));
                    for (Object key : map.keySet()) {
                        val value = map.get(key);
                        if (value instanceof BigDecimal) {
                            map.put(key, BigDecimal.valueOf(((BigDecimal) value).floatValue()));
                        }
                    }
                    if (map.get("validityDuration") != null) {
                        String validityDurationStr = map.get("validityDuration").toString();
                        Matcher matcher = Pattern.compile("^[0-9]+(\\.[0]*)?$").matcher(validityDurationStr);
                        if (matcher.find()) {
                            String validityDuration = String.format("%s%s", new BigDecimal(validityDurationStr).intValue(), map.get("validityUnit"));
                            map.put("validityDuration", validityDuration);
                        } else {
                            String validityDuration = map.get("validityDuration").toString() + map.get("validityUnit");
                            map.put("validityDuration", validityDuration);
                        }
                    }
                    return map;
                }).collect(Collectors.toList());
                upload(pageNo, totalPage, dto.getFileName(), dto.getType(), dto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出年假额度数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadEmpQuotaData(QuotaEmpDto dto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            List<BaseHeaderDto> head = quotaService.getQuotaHeaders(userInfo.getTenantId()).stream().map(headString -> {
                val baseHead = FastjsonUtil.toObject(headString, BaseHeaderDto.class);
                return baseHead;
            }).collect(Collectors.toList());
            dto.setMergeWorknoToEmpName(true);
            PageBean pageBean = PageUtil.getPageBean(dto);
            dto.setAll(false);
            val firstPage = quotaService.queryEmpQuotaList(dto, pageBean, userInfo.getTenantId());
            int total = 0;
            if (!firstPage.isEmpty()) {
                total = ((PageList) firstPage).getPaginator().getTotalCount();
            }
            log.info("导出数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            dto.setPageSize(PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                int pageNo = dto.getPageNo();
                pageBean = PageUtil.getPageBean(dto);
                val list = quotaService.queryEmpQuotaList(dto, pageBean, userInfo.getTenantId());
                for (Map data : list) {
                    for (Object key : data.keySet()) {
                        Optional<BaseHeaderDto> header = head.stream().filter(h -> key.toString().equals(h.getId())).collect(Collectors.toList()).stream().findFirst();
                        val value = data.get(key);
                        if (header.isPresent() && "date".equals(header.get().getType())) {
                            if (value != null) {
                                data.put(key, DateUtil.parseDateToPattern(new Date(Long.valueOf(value.toString()) * 1000),
                                        "yyyy-MM-dd"));

                            }
                        } else {
                            if (value != null && value instanceof BigDecimal) {
                                data.put(key, new BigDecimal(((BigDecimal) value).floatValue()));
                            }
                            if (value != null && value instanceof Float) {
                                data.put(key, new BigDecimal((Float) value));
                            }
                        }
                    }
                    if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                        data.put("org_name", data.get("fullPath"));
                    }
                }
                dto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, dto.getFileName(), dto.getType(), head, list, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出假期余额数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadEmpShiftChangeData(EmpShiftSearchDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            requestDto.setAll(false);
            PageList<Map> pageList = empWorkCalendarService.getEmpShiftChangeList(requestDto, userInfo);
            int total = pageList.getPaginator().getTotalCount();
            log.info("uploadEmpShiftChangeData导出数据量：{}", total);

            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();

                PageList<Map> dataList = empWorkCalendarService.getEmpShiftChangeList(requestDto, userInfo);
                if (CollectionUtils.isNotEmpty(dataList)) {
                    dataList.stream().forEach(row -> {
                        if (row.containsKey("workDate") && row.get("workDate") != null) {
                            row.put("workDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("workDate").toString())));
                        }
                        if (row.containsKey("crttime") && row.get("crttime") != null) {
                            row.put("crttime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("crttime").toString())));
                        }
                    });
                }

                requestDto.setPageNo(pageNo + 1);

                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("uploadEmpShiftChangeData导出数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "uploadEmpShiftChangeData导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadEmpGroupChangeData(AttEmpGroupReqDto dto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            dto.setAll(false);
            AttendancePageResult<AttEmpGroupDto> pageList = groupService.getWaEmpGroupList(dto, userInfo);
            int total = pageList.getTotal();
            log.info("导出员工考勤方案数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            dto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = dto.getPageNo();
                AttendancePageResult<AttEmpGroupDto> result = groupService.getWaEmpGroupList(dto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(result.getItems())) {
                    result.getItems().forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("workno", item.getWorkNo());
                        row.put("empName", item.getEmpName());
                        row.put("waGroupName", item.getWaGroupName());
                        row.put("orgName", StringUtils.isNotBlank(item.getFullPath()) ? item.getFullPath() : item.getOrgName());
                        row.put("empStatusName", item.getEmpStatusName());
                        row.put("empStyleName", item.getEmpStyleName());
                        if (item.getStartTime() != null) {
                            row.put("startTime", DateUtil.getDateStrByTimesamp(item.getStartTime()));
                        }
                        row.put("terminationDate", item.getTerminationDate() == null ? "-" : DateUtil.getDateStrByTimesamp(item.getTerminationDate()));
                        row.put("hireDate", item.getHireDate() != null ? DateUtil.getDateStrByTimesamp(item.getHireDate()) : "-");
                        if (item.getEndTime() != null) {
                            row.put("endTime", DateUtil.getDateStrByTimesamp(item.getEndTime()));
                        }
                        row.put("operator", item.getOperator());
                        if (item.getUpdateTime() != null) {
                            row.put("updateTime", DateUtil.getTimeStrByTimesamp(item.getUpdateTime()));
                        }
                        dataList.add(row);
                    });
                }
                dto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, dto.getFileName(), dto.getType(), dto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工考勤方案数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出员工考勤方案数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportEmpShiftList(ExportEmpShiftDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            requestDto.setAll(false);
            EmpShiftReqDto empShiftReqDto = ObjectConverter.convert(requestDto, EmpShiftReqDto.class);
            AttendancePageResult<EmpShiftDto> pageList = workCalendarService.getEmpShiftListByCalendarId(empShiftReqDto, userInfo);
            int total = pageList.getTotal();
            log.info("导出员工日历（排班）数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            empShiftReqDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = empShiftReqDto.getPageNo();
                AttendancePageResult<EmpShiftDto> result = workCalendarService.getEmpShiftListByCalendarId(empShiftReqDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(result.getItems())) {
                    result.getItems().forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("workno", item.getWorkno());
                        row.put("empName", item.getEmpName());
                        row.put("workCalendarName", item.getWorkCalendarName());
                        if (item.getStartTime() != null) {
                            row.put("startTime", DateUtil.getDateStrByTimesamp(item.getStartTime()));
                        }
                        row.put("orgName", StringUtils.isNotBlank(item.getFullPath()) ? item.getFullPath() : item.getOrgName());
                        if (item.getEndTime() != null) {
                            row.put("endTime", DateUtil.getDateStrByTimesamp(item.getEndTime()));
                        }
                        row.put("hireDate", item.getHireDate() != null ? DateUtil.getDateStrByTimesamp(item.getHireDate()) : "-");
                        row.put("empStyleName", item.getEmpStyleName());
                        row.put("empStatusName", item.getEmpStatusName());
                        row.put("terminationDate", item.getTerminationDate() != null ? DateUtil.getDateStrByTimesamp(item.getTerminationDate()) : "-");
                        row.put("updater", item.getUpdater());
                        row.put("updtime", item.getUpdtime() == null ? "-" : DateUtil.getTimeStrByTimesamp(item.getUpdtime()));
                        dataList.add(row);
                    });
                }
                empShiftReqDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工日历（排班）数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出员工日历（排班）数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportEmpClockPlanList(ExportEmpClockPlanDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            requestDto.setAll(false);
            EmpClockPlanReqDto empClockPlanDto = ObjectConverter.convert(requestDto, EmpClockPlanReqDto.class);
            AttendancePageResult<EmpClockPlanDto> pageList = clockPlanService.getEmpClockPlans(empClockPlanDto, userInfo);
            int total = pageList.getTotal();
            log.info("导出员工打卡方案数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            empClockPlanDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = empClockPlanDto.getPageNo();
                AttendancePageResult<EmpClockPlanDto> result = clockPlanService.getEmpClockPlans(empClockPlanDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(result.getItems())) {
                    result.getItems().forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("workno", item.getWorkno());
                        row.put("empName", item.getEmpName());
                        row.put("planName", item.getPlanName());
                        if (item.getEndTime() != null) {
                            row.put("endTime", DateUtil.getDateStrByTimesamp(item.getEndTime()));
                        }
                        row.put("orgName", StringUtils.isNotBlank(item.getFullPath()) ? item.getFullPath() : item.getOrgName());
                        if (item.getStartTime() != null) {
                            row.put("startTime", DateUtil.getDateStrByTimesamp(item.getStartTime()));
                        }
                        row.put("updaterName", item.getUpdaterName());
                        row.put("updateTime", item.getUpdateTime() == null ? "-" : DateUtil.getTimeStrByTimesamp(item.getUpdateTime()));
                        row.put("hireDate", item.getHireDate() != null ? DateUtil.getDateStrByTimesamp(item.getHireDate()) : "-");
                        row.put("empStatusName", item.getEmpStatusName());
                        row.put("empStyleName", item.getEmpStyleName());
                        row.put("terminationDate", item.getTerminationDate() != null ? DateUtil.getDateStrByTimesamp(item.getTerminationDate()) : "-");
                        dataList.add(row);
                    });
                }
                empClockPlanDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工打卡方案数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出员工打卡方案数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportOutworkList(RegisterRecordRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getCount(requestDto, userInfo);
            log.info("导出外勤记录数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                List<Map> dataList = getOutworkRegConvertList(requestDto, userInfo);
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出外勤记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出外勤记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private List<Map> getOutworkRegConvertList(RegisterRecordRequestDto requestDto, UserInfo userInfo) {
        requestDto.setIfExport(true);
        AttendancePageResult<RegisterRecordDto> pageResult = registerRecordService.getRegisterRecordPageList(requestDto, userInfo);
        List<Map> dataList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(pageResult.getItems())) {
            List<Map> mapList = JSONArray.parseArray(JSONArray.toJSONString(pageResult.getItems()), Map.class);
            mapList.forEach(row -> {
                if (row.containsKey("regDateTime") && row.get("regDateTime") != null) {
                    row.put("regDateTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(row.get("regDateTime").toString())));
                }
                if (row.containsKey("fullPath") && row.get("fullPath") != null) {
                    row.put("orgName", row.get("fullPath"));
                }
                if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && row.containsKey("workno") && row.get("workno") != null) {
                    row.put("empName", row.get("workno") + "(" + row.get("empName") + ")");
                }
                if (row.containsKey("regAddr") && row.get("regAddr") != null) {
                    row.put("regAddr", row.get("regAddr"));
                }
                if (row.containsKey("owRmk") && row.get("owRmk") != null) {
                    row.put("owRmk", row.get("owRmk"));
                }
                if (row.containsKey("belongDate") && row.get("belongDate") != null) {
                    row.put("belongDate", DateUtil.getDateStrByTimesamp(Long.valueOf(row.get("belongDate").toString())));
                }
                if (row.containsKey("clockSiteStatus") && row.get("clockSiteStatus") != null) {
                    row.put("clockSiteStatus", ValidStatusEnum.getName((Integer) row.get("clockSiteStatus")));
                }
            });
            dataList.addAll(mapList);
        }
        return dataList;
    }

    private int getEmpQuotaCount(QuotaPageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = PageUtil.getPageBean(requestDto);

        List<Map> list = quotaService.getQuotaList(pageBean, requestDto.getYear(), requestDto.getQuotaSettingId(), userInfo.getTenantId(), requestDto.getYears(), requestDto.getDataScope());
//        List<Map> list = waConfigService.getEmpQuotaList(pageBean, requestDto.getYear(), requestDto.getQuotaSettingId(), userInfo.getBelongOrgId(),requestDto.getYears());
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        PageList<Map> pageList = (PageList<Map>) list;
        AttendancePageResult pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    private int getDayCount(DayAnalysePageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = PageUtil.getPageBean(requestDto);
        requestDto.setNotOrderBy(Boolean.TRUE);
        requestDto.setIsCount(Boolean.TRUE);
        List<Map> list = statisticsService.getDayAnalyseList(requestDto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        PageList<Map> pageList = (PageList<Map>) list;
        AttendancePageResult pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    private int getMonthAnalyseCount(MonthAnalysePageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = PageUtil.getPageBean(requestDto);
        List<Map> list = statisticsService.searchRegisterStatistics(requestDto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        PageList<Map> pageList = (PageList<Map>) list;
        AttendancePageResult pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    private int getOverTimeCount(OvertimeApplyDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        List<Map> list = workOvertimeService.getOvertimeList(userInfo.getTenantId(), pageBean, null, requestDto.getStartDate(), requestDto.getStartTime(), requestDto.getEndDate(), requestDto.getEndTime(), requestDto.getDataScope());
        if (CollectionUtils.isEmpty(list)) {
            return 0;
        }
        PageList<Map> pageList = (PageList<Map>) list;
        AttendancePageResult pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    private void upload(CountDownLatch countDownLatch, int pageNo, int totalPage, String fileName, String type, List<BaseHeaderDto> head, List<Map> dataList, TaskDto task, UserInfo userInfo) {
        if (pageNo > 1 && CollectionUtils.isEmpty(dataList)) {
            return;
        }
        String fileNum = pageNo == 1 && dataList.size() < PAGE_SIZE ? "" : "(" + pageNo + ")";
        String fullFileName = String.format("%s-%s%s.%s", fileName, DateUtil.parseDateToPattern(new Date(), "yyyy-MM-dd"), fileNum, type);
        try {
            // 生成Excel并转换成 MultipartFile
            log.info("文件{}开始生成", fullFileName);
            MultipartFile file = FileUtil.genExcelAndConvertToMultipartFile(head, dataList, fullFileName, fileName, type);
            log.info("文件{}生成结束", fullFileName);
            log.info("文件{}开始上传", fullFileName);
            String bucketName = userInfo.getCorpid() + "-" + userInfo.getTenantId();
            UploadResult uploadResult = ossService.upload(bucketName, file);
            List<UploadFileResult> files = task.getUploadFiles();
            if (null != uploadResult) {
                files.add(ObjectConverter.convert(uploadResult, UploadFileResult.class));
            }
            log.info("文件{}上传结束", fullFileName);
            if (countDownLatch != null) {
                countDownLatch.countDown();
                long remain = countDownLatch.getCount();
                BigDecimal leftPercent = new BigDecimal(remain).divide(new BigDecimal(totalPage), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100"));
                task.setProgress(100 - leftPercent.intValue());
                if (remain <= 0) {
                    task.setStatus(TaskStatusEnum.SUCCESS.getName());
                }
            } else {
                task.setProgress(pageNo > totalPage ? 100 : new BigDecimal(pageNo).divide(new BigDecimal(totalPage), 2, RoundingMode.HALF_UP).multiply(new BigDecimal("100")).intValue());
                if (pageNo == totalPage) {
                    task.setStatus(TaskStatusEnum.SUCCESS.getName());
                }
            }
            task.setUploadFiles(files);
        } catch (IOException ie) {
            log.error("文件生成失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("文件生成失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
        } catch (Exception ex) {
            log.error("文件上传失败：{}", ex.getMessage(), ex);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("文件上传失败");
            task.setExt(ex.getMessage());
            task.setProgress(100);
        } finally {
            taskService.update(task, userInfo);
        }
    }

    private void upload(int pageNo, int totalPage, String fileName, String type, List<BaseHeaderDto> head, List<Map> dataList, TaskDto task, UserInfo userInfo) {
        this.upload(null, pageNo, totalPage, fileName, type, head, dataList, task, userInfo);
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportEmpTravelList(EmpTravelReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            EmpTravelReqDto dto = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), EmpTravelReqDto.class);
            dto.setAll(false);
            com.caidaocloud.dto.PageResult<EmpTravelDto> pageList = empTravelService.getEmpTravelPageList(dto, userInfo);
            int total = pageList.getTotal();
            log.info("导出员工出差申请数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                com.caidaocloud.dto.PageResult<EmpTravelDto> result = empTravelService.getEmpTravelPageList(requestDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(result.getItems())) {
                    result.getItems().forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && item.getWorkNo() != null) {
                            row.put("empName", item.getWorkNo() + "(" + item.getEmpName() + ")");
                        } else {
                            row.put("workno", item.getWorkNo());
                            row.put("empName", item.getEmpName());
                        }
                        if (item.getFullPath() != null) {
                            row.put("orgName", item.getFullPath());
                        }
                        if (item.getTravelType() != null) {
                            row.put("travelType", item.getTravelType());
                        }
                        if (item.getTimeDuration() != null) {
                            row.put("timeDuration", String.format("%s%s", item.getTimeDuration(), item.getTimeUnitName()));
                        }
                        if (item.getStartDate() != null) {
                            row.put("startDate", item.getStartDate());
                        }
                        if (item.getEndDate() != null) {
                            row.put("endDate", item.getEndDate());
                        }
                        if (item.getTravelModeName() != null) {
                            row.put("travelModeName", item.getTravelModeName());
                        }
                        if (item.getCityName() != null) {
                            row.put("cityName", item.getCityName());
                        }
                        row.put("reason", item.getReason() != null ? item.getReason() : "-");
                        if (item.getCreateTime() != null) {
                            row.put("createTime", DateUtil.getTimeStrByTimesamp(item.getCreateTime()));
                        }
                        if (item.getStatusName() != null) {
                            row.put("statusName", item.getStatusName());
                        }
                        if (item.getLastApprovalTime() != null) {
                            row.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(item.getLastApprovalTime()));
                        }
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工出差申请数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出员工出差申请数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportDayAnalyseAbnormalList(DayAnalyseAbnormalPageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getDayAnalyseAbnormalCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                pageBean.setFilterList(requestDto.getFilterList());
                com.caidaocloud.dto.PageResult<DayAnalyseAbnormalDto> pageResult = statisticsService.getDayAnalyseAbnormalList(requestDto, userInfo);
                List<DayAnalyseAbnormalDto> list = pageResult.getItems();
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(data -> {
                        Map dataMap = new HashMap();
                        dataMap.put("analyzeResult", data.getAnalyzeResult() == 1 ? "异常" : "正常");
                        dataMap.put("belongDate", String.format("%s %s", DateUtil.getDateStrByTimesamp(data.getBelongDate()), data.getBelongDateWeek()));
                        dataMap.put("empName", data.getEmpName());
                        dataMap.put("orgName", data.getOrgName());
                        String orgName = Optional.ofNullable(data.getOrgName()).orElse("-");
                        dataMap.put("fullPath", Optional.ofNullable(data.getFullPath()).orElse(orgName));
                        dataMap.put("employTypeName", data.getEmployTypeName() == null ? "" : data.getEmployTypeName());
                        dataMap.put("hireDate", DateUtil.getDateStrByTimesamp(data.getHireDate()));
                        dataMap.put("empStatusName", data.getEmpStatusName());
                        dataMap.put("shiftDefName", data.getShiftDefName());
                        dataMap.put("shiftDefCode", data.getShiftDefCode());
                        dataMap.put("clockType", data.getClockType());
                        dataMap.put("leaveName", data.getLeaveName());
                        dataMap.put("applyDate", DateUtil.getDateStrByTimesamp(data.getApplyDate()));
                        dataMap.put("approvalStatus", ApprovalStatusEnum.getName(data.getApprovalStatus()));
                        dataList.add(dataMap);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出异常汇总记录：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出异常汇总记录");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getDayAnalyseAbnormalCount(DayAnalyseAbnormalPageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        com.caidaocloud.dto.PageResult<DayAnalyseAbnormalDto> pageResult = statisticsService.getDayAnalyseAbnormalList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }

    @Override
    public void exportOvertimeRevokeList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getOvertimeRevokeCount(requestDto, userInfo);
            log.info("导出加班撤销记录数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                PageList<Map> list = workflowRevokeService.getOvertimeWorkflowRevokeList(requestDto, pageBean, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(originRow -> {
                        Map data = BeanMapUtils.formatHumpName(originRow);
                        if (data.containsKey("crttime") && data.get("crttime") != null) {
                            data.put("crttime", DateUtil.getDateStrByTimesamp(Long.valueOf(data.get("crttime").toString())));
                        }
                        if (data.containsKey("startTime") && data.get("startTime") != null) {
                            data.put("startTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("startTime").toString())));
                        }
                        if (data.containsKey("endTime") && data.get("endTime") != null) {
                            data.put("endTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("endTime").toString())));
                        }
                        if (data.containsKey("lastApprovalTime") && data.get("lastApprovalTime") != null) {
                            data.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("lastApprovalTime").toString())));
                        }
                        if (data.containsKey("dateType") && data.get("dateType") != null) {
                            data.put("dateType", DateTypeEnum.getName(Integer.parseInt(data.get("dateType").toString())));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && data.containsKey("workno") && data.get("workno") != null) {
                            data.put("empName", data.get("workno") + "(" + data.get("empName") + ")");
                        }
                        if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                            data.put("shortname", data.get("fullPath"));
                        }
                        if (originRow.containsKey("transfer_duration") && originRow.get("transfer_duration") != null) {
                            data.put("transferDuration", originRow.get("transfer_duration"));
                        }
                        if (originRow.containsKey("revokereason") && originRow.get("revokereason") != null) {
                            data.put("revokeReason", Optional.ofNullable(originRow.get("revokereason")).orElse("-"));
                        }
                        if (originRow.containsKey("transfer_unit") && originRow.get("transfer_unit") != null) {
                            Integer transferUnit = (Integer) originRow.get("transfer_unit");
                            data.put("transferUnit", PreTimeUnitEnum.getName(transferUnit));
                        }
                        dataList.add(data);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出加班撤销流程记录：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出出差撤销流程记录为空" : "导出加班撤销流程记录");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getOvertimeRevokeCount(WorkflowRevokeReqDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<?> pageList = workflowRevokeService.getOvertimeWorkflowRevokeList(requestDto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        AttendancePageResult<?> pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    @Override
    public void exportOvertimeAbolishList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getOvertimeAbolishCount(requestDto, userInfo);
            log.info("导出加班废止记录数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                PageList<Map> list = workflowRevokeService.getOvertimeWorkflowAbolishList(requestDto, pageBean, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(originRow -> {
                        Map data = BeanMapUtils.formatHumpName(originRow);
                        if (data.containsKey("crttime") && data.get("crttime") != null) {
                            data.put("crttime", DateUtil.getDateStrByTimesamp(Long.valueOf(data.get("crttime").toString())));
                        }
                        if (data.containsKey("startTime") && data.get("startTime") != null) {
                            data.put("startTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("startTime").toString())));
                        }
                        if (data.containsKey("endTime") && data.get("endTime") != null) {
                            data.put("endTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("endTime").toString())));
                        }
                        if (data.containsKey("lastApprovalTime") && data.get("lastApprovalTime") != null) {
                            data.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(Long.valueOf(data.get("lastApprovalTime").toString())));
                        }
                        if (data.containsKey("dateType") && data.get("dateType") != null) {
                            data.put("dateType", DateTypeEnum.getName(Integer.parseInt(data.get("dateType").toString())));
                        }
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && data.containsKey("workno") && data.get("workno") != null) {
                            data.put("empName", data.get("workno") + "(" + data.get("empName") + ")");
                        }
                        if (data.containsKey("fullPath") && data.get("fullPath") != null) {
                            data.put("shortname", data.get("fullPath"));
                        }
                        if (originRow.containsKey("transfer_duration") && originRow.get("transfer_duration") != null) {
                            data.put("transferDuration", originRow.get("transfer_duration"));
                        }
                        if (originRow.containsKey("revokereason") && originRow.get("revokereason") != null) {
                            data.put("revokeReason", Optional.ofNullable(originRow.get("revokereason")).orElse("-"));
                        }
                        if (originRow.containsKey("transfer_unit") && originRow.get("transfer_unit") != null) {
                            Integer transferUnit = (Integer) originRow.get("transfer_unit");
                            data.put("transferUnit", PreTimeUnitEnum.getName(transferUnit));
                        }
                        dataList.add(data);
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出加班废止流程记录：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出出差废止流程记录为空" : "导出加班废止流程记录");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getOvertimeAbolishCount(WorkflowRevokeReqDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<?> pageList = workflowRevokeService.getOvertimeWorkflowAbolishList(requestDto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        AttendancePageResult<?> pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    @Override
    public void exportTravelRevokeList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getTravelRevokeCount(requestDto, userInfo);
            log.info("导出加班撤销记录数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                PageList<Map> list = workflowRevokeService.getTravelWorkflowRevokeList(requestDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && item.get("workno") != null) {
                            row.put("empName", item.get("workno") + "(" + item.get("emp_name") + ")");
                        } else {
                            row.put("workno", item.get("workno"));
                            row.put("empName", item.get("emp_name"));
                        }
                        if (item.get("fullPath") != null) {
                            row.put("orgName", item.get("fullPath"));
                        }
                        if (item.get("travelType") != null) {
                            row.put("travelType", item.get("travelType"));
                        }
                        if (item.get("time_duration") != null) {
                            row.put("timeDuration", String.format("%s%s", item.get("time_duration"), item.get("timeUnitName")));
                        }
                        if (item.get("startDate") != null) {
                            row.put("startDate", item.get("startDate"));
                        }
                        if (item.get("endDate") != null) {
                            row.put("endDate", item.get("endDate"));
                        }
                        if (item.get("travelModeName") != null) {
                            row.put("travelModeName", item.get("travelModeName"));
                        }
                        if (item.get("cityName") != null) {
                            row.put("cityName", item.get("cityName"));
                        }
                        row.put("reason", Optional.ofNullable(item.get("reason")).orElse("-"));
                        row.put("revokeReason", Optional.ofNullable(item.get("revokeReason")).orElse("-"));
                        if (item.get("create_time") != null) {
                            row.put("createTime", DateUtil.getTimeStrByTimesamp((Long) item.get("create_time")));
                        }
                        if (item.get("statusName") != null) {
                            row.put("statusName", item.get("statusName"));
                        }
                        if (item.get("last_approval_time") != null) {
                            row.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp((Long) item.get("last_approval_time")));
                        }
                        if (item.containsKey("revokereason") && item.get("revokereason") != null) {
                            row.put("revokeReason", Optional.ofNullable(item.get("revokereason")).orElse("-"));
                        }
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工出差撤销申请数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出员工出差撤销申请数据为空" : "导出员工出差撤销申请数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getTravelRevokeCount(WorkflowRevokeReqDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<?> pageList = workflowRevokeService.getTravelWorkflowRevokeList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        AttendancePageResult<?> pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    @Override
    public void exportTravelAbolishList(WorkflowRevokeReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getTravelAbolishCount(requestDto, userInfo);
            log.info("导出加班废止记录数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                PageList<Map> list = workflowRevokeService.getTravelWorkflowAbolishList(requestDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && item.get("workno") != null) {
                            row.put("empName", item.get("workno") + "(" + item.get("emp_name") + ")");
                        } else {
                            row.put("workno", item.get("workno"));
                            row.put("empName", item.get("emp_name"));
                        }
                        if (item.get("fullPath") != null) {
                            row.put("orgName", item.get("fullPath"));
                        }
                        if (item.get("travelType") != null) {
                            row.put("travelType", item.get("travelType"));
                        }
                        if (item.get("time_duration") != null) {
                            row.put("timeDuration", String.format("%s%s", item.get("time_duration"), item.get("timeUnitName")));
                        }
                        if (item.get("startDate") != null) {
                            row.put("startDate", item.get("startDate"));
                        }
                        if (item.get("endDate") != null) {
                            row.put("endDate", item.get("endDate"));
                        }
                        if (item.get("travelModeName") != null) {
                            row.put("travelModeName", item.get("travelModeName"));
                        }
                        if (item.get("cityName") != null) {
                            row.put("cityName", item.get("cityName"));
                        }
                        row.put("reason", Optional.ofNullable(item.get("reason")).orElse("-"));
                        row.put("revokeReason", Optional.ofNullable(item.get("revokeReason")).orElse("-"));
                        if (item.get("create_time") != null) {
                            row.put("createTime", DateUtil.getTimeStrByTimesamp((Long) item.get("create_time")));
                        }
                        if (item.get("statusName") != null) {
                            row.put("statusName", item.get("statusName"));
                        }
                        if (item.get("last_approval_time") != null) {
                            row.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp((Long) item.get("last_approval_time")));
                        }
                        if (item.containsKey("revokereason") && item.get("revokereason") != null) {
                            row.put("revokeReason", Optional.ofNullable(item.get("revokereason")).orElse("-"));
                        }
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出员工出差废止申请数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出员工出差废止申请数据为空" : "导出员工出差废止申请数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getTravelAbolishCount(WorkflowRevokeReqDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<?> pageList = workflowRevokeService.getTravelWorkflowAbolishList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        AttendancePageResult<?> pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadBatchLeaveData(BatchLeaveQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getBatchLeaveCount(requestDto, userInfo);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getNewPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> dataList = new ArrayList<>();
                PageList<BatchLeavePageListVo> pageList = waBatchLeaveService.getPageList(pageBean, requestDto, userInfo);
                if (CollectionUtils.isNotEmpty(pageList)) {
                    dataList = FastjsonUtil.convertList(pageList, Map.class);
                    dataList.forEach(dataRow -> {
                        Long createDate = (Long) dataRow.get("createDate");
                        if (null != createDate) {
                            dataRow.put("createDate", DateUtil.getDateStrByTimesamp(createDate / 1000));
                        }
                        Long lastApprovalTime = (Long) dataRow.get("lastApprovalTime");
                        if (null != lastApprovalTime) {
                            dataRow.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(lastApprovalTime / 1000));
                        }
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出批量休假记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出批量休假记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getBatchLeaveCount(BatchLeaveQueryDto dto, UserInfo userInfo) {
        dto.setAll(false);
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        PageList<BatchLeavePageListVo> pageList = waBatchLeaveService.getPageList(pageBean, dto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        return pageList.getPaginator().getTotalCount();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadBatchOvertimeData(BatchOvertimeQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getBatchOvertimeCount(requestDto, userInfo);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getNewPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> dataList = new ArrayList<>();
                PageList<BatchOvertimePageListVo> pageList = waBatchOvertimeService.getPageList(pageBean, requestDto, userInfo);
                if (CollectionUtils.isNotEmpty(pageList)) {
                    dataList = FastjsonUtil.convertList(pageList, Map.class);
                    dataList.forEach(dataRow -> {
                        Long createDate = (Long) dataRow.get("createDate");
                        if (null != createDate) {
                            dataRow.put("createDate", DateUtil.getDateStrByTimesamp(createDate / 1000));
                        }
                        Long lastApprovalTime = (Long) dataRow.get("lastApprovalTime");
                        if (null != lastApprovalTime) {
                            dataRow.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(lastApprovalTime / 1000));
                        }
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出批量加班记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出批量加班记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getBatchOvertimeCount(BatchOvertimeQueryDto dto, UserInfo userInfo) {
        dto.setAll(false);
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        PageList<BatchOvertimePageListVo> pageList = waBatchOvertimeService.getPageList(pageBean, dto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        return pageList.getPaginator().getTotalCount();
    }


    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadBatchAnalyseAdjustData(BatchAnalyseResultAdjustQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getBatchAnalyseAdlustCount(requestDto, userInfo);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            for (int i = 0; i < totalPage; i++) {
                PageBean pageBean = PageUtil.getNewPageBean(requestDto);
                int pageNo = requestDto.getPageNo();
                requestDto.setPageNo(pageNo + 1);
                List<Map> dataList = new ArrayList<>();
                PageList<BatchAnalyseResultAdjustPageListVo> pageList = waBatchAnalyseResultAdjustService.getPageList(pageBean, requestDto, userInfo);
                if (CollectionUtils.isNotEmpty(pageList)) {
                    dataList = FastjsonUtil.convertList(pageList, Map.class);
                    dataList.forEach(dataRow -> {
                        Long createDate = (Long) dataRow.get("createDate");
                        if (null != createDate) {
                            dataRow.put("createDate", DateUtil.getDateStrByTimesamp(createDate / 1000));
                        }
                        Long lastApprovalTime = (Long) dataRow.get("lastApprovalTime");
                        if (null != lastApprovalTime) {
                            dataRow.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(lastApprovalTime / 1000));
                        }
                    });
                }
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出批量考勤异常申请记录数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出批量考勤异常申请记录数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getBatchAnalyseAdlustCount(BatchAnalyseResultAdjustQueryDto dto, UserInfo userInfo) {
        dto.setAll(false);
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        PageList<BatchAnalyseResultAdjustPageListVo> pageList = waBatchAnalyseResultAdjustService.getPageList(pageBean, dto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        return pageList.getPaginator().getTotalCount();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportLeaveExtensionList(LeaveExtensionReqDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getLeaveExtensionCount(requestDto, userInfo);
            log.info("导出假期延期申请列表数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                PageList<LeaveExtensionDto> list = leaveExtensionService.getEmpLeaveExtensionList(requestDto, userInfo);
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && item.getWorkNo() != null) {
                            row.put("empName", String.format("%s(%s)", item.getWorkNo(), item.getEmpName()));
                        } else {
                            row.put("workno", item.getWorkNo());
                            row.put("empName", item.getEmpName());
                        }
                        row.put("orgName", Optional.ofNullable(item.getOrgName()).orElse("-"));
                        row.put("quotaName", Optional.ofNullable(item.getQuotaName()).orElse("-"));
                        if (item.getStartDate() != null) {
                            row.put("startDate", DateUtil.getDateStrByTimesamp(item.getStartDate()));
                        }
                        if (item.getEndDate() != null) {
                            row.put("endDate", DateUtil.getDateStrByTimesamp(item.getEndDate()));
                        }
                        row.put("timeDuration", Optional.ofNullable(item.getTimeDuration()).orElse(0f));
                        if (item.getTimeUnit() != null) {
                            row.put("timeUnit", PreTimeUnitEnum.getName(item.getTimeUnit()));
                        }
                        if (item.getTimeUnitName() != null) {
                            row.put("timeUnitName", item.getTimeUnitName());
                        }
                        if (item.getCreateTime() != null) {
                            row.put("createTime", DateUtil.getTimeStrByTimesamp(item.getCreateTime()));
                        }
                        row.put("reason", Optional.ofNullable(item.getReason()).orElse("-"));
                        if (item.getStatusName() != null) {
                            row.put("statusName", item.getStatusName());
                        }
                        row.put("revokeReason", Optional.ofNullable(item.getRevokeReason()).orElse("-"));
                        if (item.getLastApprovalTime() != null) {
                            row.put("lastApprovalTime", DateUtil.getTimeStrByTimesamp(item.getLastApprovalTime()));
                        }
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出假期延期申请列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出假期延期申请列表数据为空" : "导出假期延期申请列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getLeaveExtensionCount(LeaveExtensionReqDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageBean pageBean = FastjsonUtil.toObject(JSONUtils.ObjectToJson(PageUtil.getPageBean(requestDto)), PageBean.class);
        PageList<LeaveExtensionDto> pageList = leaveExtensionService.getEmpLeaveExtensionList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageList)) {
            return 0;
        }
        AttendancePageResult<LeaveExtensionDto> pageResult = new AttendancePageResult<>(pageList, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
        return pageResult.getTotal();
    }

    /**
     * 动态列-考勤明细
     *
     * @param requestDto
     * @param task
     * @param b
     * @param userInfo
     * @param locale
     */
    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadDataAdvanceByDynamic(MonthAnalysePageDto requestDto, TaskDto task, boolean b, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getMonthAnalyseCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            List<BaseHeaderDto> head = new ArrayList<>();
            ThreadPoolExecutor poolExecutor = ThreadExecutor.getThreadPool();
            CountDownLatch downLatch = new CountDownLatch(totalPage);
            for (int i = 0; i < totalPage; i++) {
                poolExecutor.execute(() -> {
                    SecurityUserInfo securityUserInfo = new SecurityUserInfo();
                    try {
                        securityUserInfo.setUserId(userInfo.getUserId());
                        securityUserInfo.setTenantId(userInfo.getTenantId());
                        securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                        List<String> monthlyAdvanced = initTimeStampColumn("MOUTHLYADVANCET");
                        if (CollectionUtils.isEmpty(head)) {
                            head.addAll(statisticsService.getMonthAdvanceHeadersByDynamicForExport(requestDto.getGroupId(), userInfo,
                                    new ImmutablePair<>(requestDto.getStartDate(), requestDto.getEndDate())));
                            log.info("DataByDynamic headList {}", head);
                        }

                        PageBean pageBean = PageUtil.getPageBean(requestDto);
                        pageBean.setFilterList(requestDto.getFilterList());
                        int pageNo = requestDto.getPageNo();
                        requestDto.setPageNo(pageNo + 1);
                        DynamicPageDto mouthAnalyseListForDynamic = statisticsService.getRegisterStatisticsAdvancedForDynamic(requestDto, pageBean, userInfo);
                        List<Map> list = mouthAnalyseListForDynamic.getPageData().getItems();
                        if (CollectionUtils.isNotEmpty(list)) {
                            list.forEach(data -> {
                                for (String timeCol : monthlyAdvanced) {
                                    if (data.containsKey(timeCol) && data.get(timeCol) != null) {
                                        data.put(timeCol, DateUtil.getDateStrByTimesamp((Long.parseLong((String) data.get(timeCol))) / 1000));
                                    }
                                }
                                if (data.containsKey("status") && data.get("status") != null) {
                                    Integer status = (Integer) data.get("status");
                                    data.put("status", ClockResultEnum.getName(status));
                                }
                                convertSummaryData(head, data);
                            });
                        }
                        upload(downLatch, pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), head, list, task, userInfo);
                    } finally {
                        SecurityUserUtil.removeSecurityUserInfo();
                    }
                });
            }
        } catch (Exception ie) {
            log.error("导出考勤统计月度分析动态分页列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
            ResponseWrap.clearThreadLocale();
        }
    }

    /**
     * 动态列导出-每日统计
     *
     * @param requestDto
     * @param task
     * @param userInfo
     */
    @Async("exportAsyncTaskExecutor")
    @Override
    public void uploadDataByDynamic(DayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getDayCount(requestDto, userInfo);
            log.info("导出数据量：{}", total);
            requestDto.setPageSize(PAGE_SIZE);
            requestDto.setIsCount(Boolean.FALSE);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            List<BaseHeaderDto> head = new ArrayList<>();
            ThreadPoolExecutor poolExecutor = ThreadExecutor.getThreadPool();
            CountDownLatch downLatch = new CountDownLatch(totalPage);
            for (int i = 0; i < totalPage; i++) {
                poolExecutor.execute(() -> {
                    try {
                        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
                        securityUserInfo.setUserId(userInfo.getUserId());
                        securityUserInfo.setTenantId(userInfo.getTenantId());
                        securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
                        List<String> col = initTimeStampColumn("DAILYSUM");
                        if (CollectionUtils.isEmpty(head)) {
                            head.addAll(statisticsService.getDayHeadersByDynamicForExport(requestDto.getSobId(), userInfo));
                            log.info("DataByDynamic headList {}", head);
                        }
                        PageBean pageBean = PageUtil.getPageBean(requestDto);
                        pageBean.setFilterList(requestDto.getFilterList());
                        List<Map> list = statisticsService.getDayAnalyseListForDynamic(requestDto, pageBean, userInfo).getPageData().getItems();
                        int pageNo = requestDto.getPageNo();
                        requestDto.setPageNo(pageNo + 1);
                        if (CollectionUtils.isNotEmpty(list)) {
                            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                            list.forEach(data -> {
                                if (data.containsKey("analyze_result") && data.get("analyze_result") != null) {
                                    Integer analyzeResult = Integer.valueOf(String.valueOf(data.get("analyze_result")));
                                    data.put("analyze_result", analyzeResult == 0 ? "正常" : "异常");
                                }
                                if (data.containsKey("belong_date") && data.get("belong_date") != null) {
                                    data.put("belong_date", sdf.format(new Date(Long.parseLong(data.get("belong_date").toString()) * 1000)) + " " + data.get("belong_date_week"));
                                }
                                for (String timeCol : col) {
                                    if (data.containsKey(timeCol) && data.get(timeCol) != null) {
                                        data.put(timeCol, sdf.format(new Date(Long.parseLong((String) data.get(timeCol)))));
                                    }
                                }
                            });
                        }
                        upload(downLatch, pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), head, list, task, userInfo);
                    } catch (Exception e) {
                        log.error("poolExecutor execute fail,{}", e.getMessage(), e);
                    } finally {
                        SecurityUserUtil.removeSecurityUserInfo();
                    }
                });
            }
        } catch (Exception ie) {
            log.error("导出考勤统计每日明细列表数据失败：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出数据为空" : "导出数据失败");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private List<String> initTimeStampColumn(String code) {
        val userDynamicSet = dynamicFeignClient.userDynamicTableLoad(code).getData();
        List<String> timeCols = new ArrayList<>();
        List<Map> columns = userDynamicSet.getColumns();
        for (Map column : columns) {
            Map<String, Object> props = (Map<String, Object>) column.get("props");
            if (props.get("dataType") != null && props.get("dataType").equals("Timestamp")) {
                timeCols.add(String.valueOf(props.get("dataIndex")));
            }
        }
        return timeCols;
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportEmpOtLeftDurationList(OtLeftDurationRequestDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getEmpOvertimeLeftCount(requestDto, userInfo);
            log.info("导出加班结余时长列表数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                AttendancePageResult<OtLeftDurationDto> pageResult = overtimeApplyService.getEmpOvertimeLeftDurationPageList(requestDto, userInfo);
                List<OtLeftDurationDto> list = pageResult.getItems();
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        if (BooleanUtils.isTrue(requestDto.getMergeWorknoToEmpName()) && item.getWorkno() != null) {
                            row.put("empName", String.format("%s(%s)", item.getWorkno(), item.getEmpName()));
                        } else {
                            row.put("workno", item.getWorkno());
                            row.put("empName", item.getEmpName());
                        }
                        row.put("leftDuration", Optional.ofNullable(item.getLeftDuration()).orElse(0f));
                        row.put("timeUnitName", PreTimeUnitEnum.getName(item.getTimeUnit()));
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出加班结余时长列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出加班结余时长列表数据为空" : "导出加班结余时长列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getEmpOvertimeLeftCount(OtLeftDurationRequestDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        AttendancePageResult<OtLeftDurationDto> pageResult = overtimeApplyService.getEmpOvertimeLeftDurationPageList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportDayWorkingHourAnalyze(WfmDayAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getDayWorkingHourAnalyzeCount(requestDto, userInfo);
            log.info("导出工时统计-每日统计列表数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                AttendancePageResult<WorkingHourAnalyzeDto> pageResult = workingHourAnalyzeService.getDayAnalyseList(requestDto, userInfo);
                List<WorkingHourAnalyzeDto> list = pageResult.getItems();
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("empName", item.getEmpName());
                        row.put("workno", item.getWorkno());
                        row.put("productName", item.getProductName());
                        row.put("processName", item.getProcessName());
                        row.put("belongDate", DateUtil.getDateStrByTimesamp(item.getBelongDate()));
                        row.put("shiftName", item.getShiftName());
                        row.put("shiftTimes", getShiftTimes(item.getShiftTimes()));
                        row.put("regStartTime", item.getRegStartTime() != null ? DateUtil.getTimeStrByTimesamp(item.getRegStartTime()) : "");
                        row.put("regEndTime", item.getRegEndTime() != null ? DateUtil.getTimeStrByTimesamp(item.getRegEndTime()) : "");
                        if (Optional.ofNullable(item.getRegDateTime()).isPresent()) {
                            List<RegDateTimePair> regDateTimes = item.getRegDateTime();
                            String regDateTimeStr = regDateTimes.stream().map(regDateTime -> {
                                String checkIn = Optional.ofNullable(regDateTime.getCheckIn()).map(wfmScanCheckInDto -> DateUtil.getTimeStrByTimesamp(wfmScanCheckInDto.getRegDateTime())).orElse("缺卡");
                                String checkOut = Optional.ofNullable(regDateTime.getCheckOut()).map(wfmScanCheckInDto -> DateUtil.getTimeStrByTimesamp(wfmScanCheckInDto.getRegDateTime())).orElse("缺卡");
                                return String.format("%s~%s", checkIn, checkOut);
                            }).collect(Collectors.joining(","));
                            row.put("regDateTime", regDateTimeStr);
                        }
                        row.put("workTime", item.getWorkTime());
                        row.put("abnormalWorkTime", item.getAbnormalWorkTime());
                        row.put("effectWorkTime", item.getEffectWorkTime());
                        row.put("actualWorkTime", item.getActualWorkTime());
                        row.put("holidayActualWorkTime", item.getHolidayActualWorkTime());
                        row.put("orderNum", item.getOrderNum());
                        row.put("workshopSectionName", item.getWorkshopSectionName());
                        row.put("wfmTypeName", item.getWfmTypeName());
                        row.put("effectiveScanTime", item.getEffectiveScanTime());
                        row.put("processWorkTime", item.getProcessWorkTime());

                        row.put("processTime", item.getProcessTime());
                        row.put("workDayActualWorkTime", item.getWorkDayActualWorkTime());
                        row.put("completedQuantity", item.getCompletedQuantity());
                        row.put("actualCompletedQuantity", item.getActualCompletedQuantity());
                        row.put("completedTime", item.getCompletedTime() != null ? DateUtil.getTimeStrByTimesamp(item.getCompletedTime()) : "-");
                        row.put("stored", Optional.ofNullable(item.getStored()).orElse(false) ? BooleanValueEnum.getName(1) : BooleanValueEnum.getName(0));
                        row.put("storedTime", item.getStoredTime() != null ? DateUtil.getTimeStrByTimesamp(item.getStoredTime()) : "-");
                        row.put("tripleProductStored", Optional.ofNullable(item.getTripleProductStored()).orElse(false) ? BooleanValueEnum.getName(1) : BooleanValueEnum.getName(0));
                        row.put("processActualCompletedQuantity", item.getProcessActualCompletedQuantity());
                        row.put("notes", item.getNotes());
                        row.put("workDayOtDuration", item.getWorkDayOtDuration());
                        row.put("restDayOtDuration", item.getRestDayOtDuration());
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出工时统计-每日统计列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出工时统计-每日统计列表数据为空" : "导出工时统计-每日统计列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private String getShiftTimes(List<MultiWorkTimeInfoSimpleVo> shiftTimes) {
        if (CollectionUtils.isEmpty(shiftTimes)) {
            return "";
        }
        List<String> times = shiftTimes.stream().map(shiftTime -> {
            String startTime = String.format("%s %s", DateUtil.convertMinuteToTime(shiftTime.getStartTime()), shiftTime.getStartTimeBelong() == 1 ? "" : "次日");
            String endTime = String.format("%s %s", DateUtil.convertMinuteToTime(shiftTime.getEndTime()), shiftTime.getEndTimeBelong() == 1 ? "" : "次日");
            return String.format("%s~%s", startTime, endTime);
        }).collect(Collectors.toList());
        return String.join("、", times);
    }

    private int getDayWorkingHourAnalyzeCount(WfmDayAnalysePageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        AttendancePageResult<WorkingHourAnalyzeDto> pageResult = workingHourAnalyzeService.getDayAnalyseList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportMonthWorkingHourAnalyze(WfmMonthAnalysePageDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getMonthWorkingHourAnalyzeCount(requestDto, userInfo);
            log.info("导出工时统计-月度汇总列表：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                AttendancePageResult<WorkingHourMonthAnalyzeDto> pageResult = workingHourAnalyzeService.getMonthAnalyseList(requestDto, userInfo);
                List<WorkingHourMonthAnalyzeDto> list = pageResult.getItems();
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("empName", item.getEmpName());
                        row.put("workno", item.getWorkno());
                        row.put("productName", item.getProductName());
                        row.put("processName", item.getProcessName());
                        row.put("workTime", item.getWorkTime());
                        row.put("holidayWorkTime", item.getHolidayWorkTime());
                        row.put("actualWorkTime", item.getActualWorkTime());
                        row.put("holidayActualWorkTime", item.getHolidayActualWorkTime());
                        row.put("completedQuantity", item.getCompletedQuantity());
                        row.put("inventoryQuantity", item.getInventoryQuantity());
                        row.put("abnormalWorkTime", item.getAbnormalWorkTime());
                        row.put("effectWorkTime", item.getEffectWorkTime());
                        row.put("holidayEffectWorkTime", item.getHolidayEffectWorkTime());
                        row.put("processWorkTime", item.getProcessWorkTime());
                        row.put("actualCompletedQuantity", item.getActualCompletedQuantity());

                        row.put("workDayActualWorkTime", item.getWorkDayActualWorkTime());
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出工时统计-月度汇总列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出工时统计-月度汇总列表数据为空" : "导出工时统计-月度汇总列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getMonthWorkingHourAnalyzeCount(WfmMonthAnalysePageDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        AttendancePageResult<WorkingHourMonthAnalyzeDto> pageResult = workingHourAnalyzeService.getMonthAnalyseList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportProcessStatistic(ProcessStatisticsPageQueryDto requestDto, TaskDto task, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            int total = getProcessStatisticCount(requestDto, userInfo);
            log.info("导出工时统计-计薪工序汇总列表：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                PageResult<ProcessStatisticsPageResultDto> pageResult = workingHourAnalyzeService.getProcessStatisticsPageResult(requestDto, userInfo);
                List<ProcessStatisticsPageResultDto> list = pageResult.getItems();
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    dataList = list.stream()
                            .map(it -> FastjsonUtil.toObject(FastjsonUtil.toJson(it),
                                    new TypeReference<Map<String, Object>>() {
                                    }))
                            .collect(Collectors.toList());
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出工时统计-计薪工序汇总列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出工时统计-计薪工序汇总列表数据为空" : "导出工时统计-计薪工序汇总列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private int getProcessStatisticCount(ProcessStatisticsPageQueryDto requestDto, UserInfo userInfo) {
        requestDto.setAll(false);
        PageResult<ProcessStatisticsPageResultDto> pageResult = workingHourAnalyzeService.getProcessStatisticsPageResult(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }

    @Async("exportAsyncTaskExecutor")
    @Override
    public void exportWorkHourSettlement(WorkingHourSettlementPageDto requestDto, TaskDto task, SecurityUserInfo securityUserInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        UserInfo userInfo = getUserInfo(securityUserInfo);
        try {
            int total = getWorkHourSettlementCount(requestDto, securityUserInfo);
            log.info("导出考勤统计-工时结算列表数据量：{}", total);
            int totalPage = (int) Math.ceil((double) total / PAGE_SIZE);
            if (totalPage == 0) {
                throw new Exception("-1");
            }
            requestDto.setPageSize(PAGE_SIZE);
            //分页查询数据
            for (int i = 0; i < totalPage; i++) {
                int pageNo = requestDto.getPageNo();
                AttendancePageResult<EmpWorkingHoursSettlementVo> pageResult = empWorkingHoursSettlementService.getPageList(requestDto, securityUserInfo);
                List<EmpWorkingHoursSettlementVo> list = pageResult.getItems();
                List<Map> dataList = new ArrayList<>();
                if (CollectionUtils.isNotEmpty(list)) {
                    list.forEach(item -> {
                        Map<String, Object> row = new HashMap<>();
                        row.put("empName", item.getEmpName());
                        row.put("workno", item.getWorkno());
                        row.put("organizeTx", item.getOrganizeTxt());
                        row.put("salaryWorkHour", item.getSalaryWorkHour());
                        row.put("workHour", item.getWorkHour());
                        row.put("stdWorkTime", item.getStdWorkTime());
                        row.put("actualWorkTime", item.getActualWorkTime());
                        row.put("middleShiftDays", item.getMiddleShiftDays());
                        row.put("nightShiftDays", item.getNightShiftDays());
                        row.put("curPeriodCompensatorySum", item.getCurPeriodCompensatorySum());
                        row.put("lastPeriodCompensatory", item.getLastPeriodCompensatory());
                        row.put("curPeriodCompensatory", item.getCurPeriodCompensatory());
                        row.put("curMonthSalarySettlement", item.getCurMonthSalarySettlement());
                        row.put("remark", item.getRemark());
                        row.put("waGroupName", item.getWaGroupName());
                        dataList.add(row);
                    });
                }
                requestDto.setPageNo(pageNo + 1);
                upload(pageNo, totalPage, requestDto.getFileName(), requestDto.getType(), requestDto.getHead(), dataList, task, userInfo);
            }
        } catch (Exception ie) {
            log.error("导出考勤统计-工时结算列表数据异常：{}", ie.getMessage(), ie);
            task.setStatus(TaskStatusEnum.FAILED.getName());
            task.setReason("-1".equals(ie.getMessage()) ? "导出考勤统计-工时结算列表数据为空" : "导出考勤统计-工时结算列表数据异常");
            task.setExt(ie.getMessage());
            task.setProgress(100);
            taskService.update(task, userInfo);
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    private UserInfo getUserInfo(SecurityUserInfo securityUserInfo) {
        UserInfo userInfo = new UserInfo();
        userInfo.setTenantId(securityUserInfo.getTenantId());
        userInfo.setUserId(securityUserInfo.getUserId());
        userInfo.setStaffId(securityUserInfo.getEmpId());
        return userInfo;
    }

    private int getWorkHourSettlementCount(WorkingHourSettlementPageDto requestDto, SecurityUserInfo userInfo) {
        requestDto.setAll(false);
        AttendancePageResult<EmpWorkingHoursSettlementVo> pageResult = empWorkingHoursSettlementService.getPageList(requestDto, userInfo);
        if (CollectionUtils.isEmpty(pageResult.getItems())) {
            return 0;
        }
        return pageResult.getTotal();
    }
}
