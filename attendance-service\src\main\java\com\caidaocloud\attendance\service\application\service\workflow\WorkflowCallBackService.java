package com.caidaocloud.attendance.service.application.service.workflow;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.wa.dto.EmpGroupPo;
import com.caidao1.wa.enums.LeaveCancelSettingTypeEnum;
import com.caidao1.wa.enums.LeaveCancelStatusEnum;
import com.caidao1.wa.enums.LeaveCancelTypeEnum;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaSobService;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.dto.WaEmpQuota;
import com.caidaocloud.attendance.service.application.dto.workflow.WfCallbackResultExtDto;
import com.caidaocloud.attendance.service.application.enums.DataSourceEnum;
import com.caidaocloud.attendance.service.application.enums.QuotaRestrictionTypeEnum;
import com.caidaocloud.attendance.service.application.event.publish.WorkFlowCallBackPublish;
import com.caidaocloud.attendance.service.application.service.*;
import com.caidaocloud.attendance.service.application.service.impl.WaBatchAnalyseResultAdjustService;
import com.caidaocloud.attendance.service.application.service.impl.WaEmpLeaveCancelService;
import com.caidaocloud.attendance.service.application.service.user.WorkFlowApprovalProcessorService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpCompensatoryCaseApplyMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaEmpTravelMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaTravelTypeMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.WaWorkflowRevokeMapper;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpCompensatoryCaseApply;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaEmpTravel;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaTravelType;
import com.caidaocloud.attendance.service.infrastructure.repository.po.WaWorkflowRevoke;
import com.caidaocloud.attendance.service.infrastructure.util.PatternUtil;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeEmpLeaveDto;
import com.caidaocloud.attendance.service.interfaces.dto.RevokeLeaveCancelDto;
import com.caidaocloud.attendance.service.interfaces.dto.WorkflowCallBackHandleLeaveDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.lock.Locker;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.SpringUtil;
import com.caidaocloud.util.StrExpChangeUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.caidaocloud.workflow.dto.WfCallbackResultDto;
import com.caidaocloud.workflow.enums.WfCallbackTriggerOperationEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.ParseException;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Lock;
import java.util.stream.Collectors;

/**
 * <AUTHOR> Hanley
 * @Date 2022/8/5 11:34
 * @Version 1.0
 * 工作流回调业务服务类
 */
@Slf4j
@Service
public class WorkflowCallBackService {
    @Autowired
    private WaEmpLeaveMapper waEmpLeaveMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private WaEmpOvertimeMapper waEmpOvertimeMapper;
    @Autowired
    private PreEmpOvertimeMapper preEmpOvertimeMapper;
    @Autowired
    private EmpTravelMapper empTravelMapper;
    @Autowired
    private WaEmpTravelMapper waEmpTravelMapper;
    @Autowired
    private WaTravelTypeMapper travelTypeMapper;
    @Autowired
    private WaLeaveCancelMapper leaveCancelMapper;
    @Autowired
    private WaEmpLeaveTimeMapper waEmpLeaveTimeMapper;
    @Autowired
    private WaLeaveCancelDayTimeMapper leaveCancelDayTimeMapper;
    @Autowired
    private WaLeaveDaytimeMapper leaveDaytimeMapper;
    @Autowired
    private WaLeaveFileMapper waLeaveFileMapper;
    @Autowired
    private EmpCompensatoryQuotaMapper empCompensatoryQuotaMapper;
    @Autowired
    private WaCompensatoryQuotaUseMapper waCompensatoryQuotaUseMapper;
    @Autowired
    private WaLeaveQuotaUseMapper waLeaveQuotaUseMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WorkFlowApprovalProcessorService workFlowApprovalProcessorService;
    @Autowired
    private WaEmpLeaveCancelService empLeaveCancelService;
    @Autowired
    private ILeaveApplyService leaveApplyService;
    @Autowired
    private IWaShiftApplyService waShiftApplyService;
    @Resource
    private EmpGroupMapper empGroupDao;
    @Resource
    private EmployeeInfoMapper employeeInfoMapper;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Resource
    private WaEmpCompensatoryCaseApplyMapper waEmpCompensatoryCaseApplyMapper;
    @Autowired
    private ITravelCompensatoryService travelCompensatoryService;
    @Resource
    private WaWorkflowRevokeMapper workflowRevokeMapper;
    @Autowired
    private EmpCompensatoryQuotaDo empCompensatoryQuotaDo;
    @Autowired
    @Lazy
    private IOvertimeApplyService overtimeApplyService;
    @Autowired
    private WaSobService waSobService;
    @Autowired
    @Lazy
    private IEmpTravelService empTravelService;
    @Autowired
    private Locker locker;
    @Autowired
    private WaBatchTravelDo waBatchTravelDo;
    @Autowired
    private WaEmpTravelDo waEmpTravelDo;
    @Autowired
    private EmpCompensatoryCaseDo empCompensatoryCaseDo;
    @Autowired
    private WaBatchLeaveDo waBatchLeaveDo;
    @Autowired
    private WaBatchOvertimeDo waBatchOvertimeDo;
    @Autowired
    private WaBatchAnalyseResultAdjustDo waBatchAnalyseResultAdjustDo;
    @Autowired
    private WaAnalyseResultAdjustDo waAnalyseResultAdjustDo;
    @Autowired
    private WaLeaveExtensionDo waLeaveExtensionDo;
    @Autowired
    private WaEmpQuotaMapper waEmpQuotaMapper;
    @Autowired
    private WorkFlowCallBackPublish workFlowCallBackPublish;
    @Autowired
    private ISessionService sessionService;

    private final String ATTENDANCE_LEAVE_CALLBACK_KEY = "ATTENDANCE_LEAVE_CALLBACK_%s_%s";

    /**
     * 请假回调业务处理
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfLeaveApproval(WfCallbackResultDto dto) {
        log.warn("leave callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null!");
            return;
        }
        Long leaveId = getBusinessId(dto.getBusinessKey());
        if (leaveId == null) {
            log.info("businessKey is null!");
            return;
        }
        WaEmpLeave waEmpLeave = waEmpLeaveMapper.selectByPrimaryKey(leaveId.intValue());
        if (waEmpLeave == null) {
            log.info("saveWfLeaveApproval异常，请假单据系统不存在，leaveId=" + leaveId);
            return;
        }
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(waEmpLeave.getLeaveTypeId());
        if (waLeaveType == null) {
            log.info("saveWfLeaveApproval异常，假期类型系统不存在，leaveTypeId=" + waEmpLeave.getLeaveTypeId());
            return;
        }
        WaEmpLeave waEmpLeaveUpd = new WaEmpLeave();
        waEmpLeaveUpd.setLeaveId(leaveId.intValue());
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            //审批通过
            waEmpLeaveUpd.setStatus(ApprovalStatusEnum.PASSED.getIndex().shortValue());
            //更新realDate
            Long realDate = DateUtil.getOnlyDate();
            leaveDaytimeMapper.updateRealDate(waEmpLeaveUpd.getLeaveId(), realDate);
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {
            //审批拒绝
            waEmpLeaveUpd.setStatus(ApprovalStatusEnum.REJECTED.getIndex().shortValue());
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {
            //审批撤销
            waEmpLeaveUpd.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        } else if (WfCallbackTriggerOperationEnum.ERROR == callbackType) {
            //审批过程中发生错误
            log.info("callbackType is error!");
            return;
        }
        //自动销假审批通过直接为休假完成
        if (LeaveCancelSettingTypeEnum.AUTOMATIC.getIndex().equals(waLeaveType.getLeaveCancelType())) {
            waEmpLeaveUpd.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
        } else {
            if (waLeaveType.getCancelTime() != null && waLeaveType.getCancelTimeType() != null && waLeaveType.getCancelTime() == 1) {
                if (waLeaveType.getCancelTimeType() == 1 || (waLeaveType.getCancelTimeDay() != null && waLeaveType.getCancelTimeDay() == 0)) {
                    String tenantId = waLeaveType.getBelongOrgid();
                    EmployeeGroupDto employeeGroupDto = selectByKeyAndGroupType(String.valueOf(waLeaveType.getLeaveTypeId()), "wa_leave_type", Long.valueOf(tenantId));
                    if (null != employeeGroupDto) {
                        String groupExp = employeeGroupDto.getGroupExp();
                        if (StringUtils.isNotBlank(groupExp)) {
                            String sqlExp = StrExpChangeUtil.strExpChangeV2(groupExp);
                            if (sqlExp.contains("total_time_duration") && waLeaveType.getAcctTimeType() == 2) {
                                sqlExp = PatternUtil.replaceTotalTimeDurationFromRegex(sqlExp, "total_time_duration");
                            }
                            sqlExp = String.format("(%s) and stats<>42 ", sqlExp);
                            Long currentTime = DateUtil.getCurrentTime(true);
                            // 司龄（至年底）计算使用参数
                            Long yearEndTime = null;
                            try {
                                yearEndTime = DateUtilExt.getYearsEndTime(currentTime);
                            } catch (ParseException e) {
                                log.error(e.getMessage(), e);
                                yearEndTime = currentTime;
                            }
                            if (CollectionUtils.isNotEmpty(employeeInfoMapper.getGroupEmpIds(tenantId, waEmpLeave.getEmpid(),
                                    waEmpLeave.getLeaveId(), waEmpLeave.getLeaveTypeId(), sqlExp, currentTime, yearEndTime))) {
                                waEmpLeaveUpd.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
                            }
                        }
                    }
                }
            }
        }
        waEmpLeaveUpd.setLastApprovalTime(System.currentTimeMillis() / 1000);
        waEmpLeaveUpd.setUpdtime(System.currentTimeMillis() / 1000);
        waEmpLeaveUpd.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
        waEmpLeaveMapper.updateByPrimaryKeySelective(waEmpLeaveUpd);
        waEmpLeave.setStatus(waEmpLeaveUpd.getStatus());
        if (waLeaveType.getLeaveType() != 4) {
            if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType()) && waEmpLeave.getStatus() != null) {
                //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                Integer quotaType = waLeaveType.getQuotaType();
                if (quotaType == null) {
                    if (waLeaveType.getLeaveType() == 3) {
                        //假期类型为调休
                        quotaType = 2;
                    } else {
                        quotaType = 1;
                    }
                }
                String key = String.format(ATTENDANCE_LEAVE_CALLBACK_KEY, dto.getTenantId(), waEmpLeave.getEmpid());
                Lock lock = locker.getLock(key);
                try {
                    boolean locked = lock.tryLock(10, TimeUnit.SECONDS);
                    if (locked) {
                        try {
                            if (quotaType == 2) {
                                waCommonService.updateEmpCompensatoryQuotaApprovalResult(0L, waEmpLeave);
                            } else {
                                waCommonService.updateWaLeaveQuotaUse(0L, waEmpLeave);
                            }
                        } finally {
                            lock.unlock();
                        }
                    }
                } catch (Exception e) {
                    throw new CDException("leave callbackTime saveWfLeaveApproval err");
                }
            }
        }
    }

    /**
     * 批量休假-回调业务处理
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchLeaveCallback(WfCallbackResultDto dto) {
        Long batchId = getBusinessId(dto.getBusinessKey());
        if (batchId == null) {
            log.info("batch leave businessKey is null businessKey={}", dto.getBusinessKey());
            return;
        }
        WaBatchLeaveDo batchLeave = waBatchLeaveDo.getById(batchId);
        if (null == batchLeave) {
            UserInfo userInfo = sessionService.getUserInfo();
            log.info("batch leave batchId={} travelData is null userInfo={}", batchId, FastjsonUtil.toJsonStr(userInfo));
            // 发送到延迟队列进行延迟消费
            WfCallbackResultExtDto extDto = FastjsonUtil.convertObject(dto, WfCallbackResultExtDto.class);
            extDto.setUserInfo(userInfo);
            workFlowCallBackPublish.publish(extDto);
        } else {
            saveBatchLeaveApproval(dto);
        }
    }

    public String getTenantIdByBusinessKey(String businessKey) {
        Long batchId = getBusinessId(businessKey);
        if (batchId == null) {
            return null;
        }
        WaBatchLeaveDo batchLeave = waBatchLeaveDo.getById(batchId);
        if (null == batchLeave) {
            return null;
        }
        return batchLeave.getTenantId();
    }

    /**
     * 批量休假-回调业务处理
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchLeaveApproval(WfCallbackResultDto dto) {
        log.info("batch leave callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));

        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("batch leave callbackType is null businessKey={}", dto.getBusinessKey());
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("batch leave callbackType is error businessKey={}", dto.getBusinessKey());
            return;
        }
        Long batchId = getBusinessId(dto.getBusinessKey());
        if (batchId == null) {
            log.info("batch leave businessKey is null businessKey={}", dto.getBusinessKey());
            return;
        }
        WaBatchLeaveDo batchLeave = waBatchLeaveDo.getById(batchId);
        if (null == batchLeave) {
            log.info("batch leave batchId={} travelData is null", batchId);
            return;
        }

        switch (callbackType) {
            case REVOKE:
                batchLeave.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                break;
            case APPROVED:
                batchLeave.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                break;
            case REFUSED:
                batchLeave.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
                break;
        }
        batchLeave.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        batchLeave.setUpdateTime(System.currentTimeMillis());
        batchLeave.setLastApprovalTime(batchLeave.getUpdateTime());
        waBatchLeaveDo.updateById(batchLeave);

        // 明细更新
        WaEmpLeaveExample empLeaveExample = new WaEmpLeaveExample();
        empLeaveExample.createCriteria().andBatchIdEqualTo(batchId).andTenantIdEqualTo(batchLeave.getTenantId());
        List<WaEmpLeave> waEmpLeaveList = waEmpLeaveMapper.selectByExample(empLeaveExample);
        if (CollectionUtils.isNotEmpty(waEmpLeaveList)) {
            for (WaEmpLeave waEmpLeave : waEmpLeaveList) {
                if (ApprovalStatusEnum.REVOKED.getIndex().equals(waEmpLeave.getStatus().intValue())) {
                    continue;
                }
                WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                wfCallbackResultDto.setBusinessKey(String.format("%s_%s", waEmpLeave.getLeaveId(), BusinessCodeEnum.LEAVE.getCode()));
                wfCallbackResultDto.setCallbackType(callbackType);
                wfCallbackResultDto.setTenantId(batchLeave.getTenantId());
                saveWfLeaveApproval(wfCallbackResultDto);
            }
        }
    }

    private EmployeeGroupDto selectByKeyAndGroupType(String businessKey, String groupType, Long tenantId) {
        QueryWrapper<EmpGroupPo> wrapper = new QueryWrapper<>();
        wrapper.eq("tenant_id", tenantId)
                .eq("business_key", businessKey)
                .eq("group_type", groupType)
                .eq("deleted", 0);
        EmpGroupPo empGroupPo = empGroupDao.selectOne(wrapper);
        if (null == empGroupPo) {
            return null;
        }
        return ObjectConverter.convert(empGroupPo, EmployeeGroupDto.class);
    }

    /**
     * 批量加班回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchOvertimeApproval(WfCallbackResultDto dto) throws Exception {
        log.info("batch overtime start callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));

        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("batch overtime callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("batch overtime callbackType is error");
            return;
        }
        Long batchId = getBusinessId(dto.getBusinessKey());
        if (batchId == null) {
            log.info("batch overtime businessKey is null!");
            return;
        }
        WaBatchOvertimeDo batchOvertime = waBatchOvertimeDo.getById(batchId);
        if (null == batchOvertime) {
            log.info("batch overtime batchId={} Data is null!", batchId);
            return;
        }
        switch (callbackType) {
            case REVOKE:
                batchOvertime.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                break;
            case APPROVED:
                batchOvertime.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                break;
            case REFUSED:
                batchOvertime.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
                break;
        }
        batchOvertime.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        batchOvertime.setUpdateTime(System.currentTimeMillis());
        batchOvertime.setLastApprovalTime(batchOvertime.getUpdateTime());
        waBatchOvertimeDo.updateById(batchOvertime);

        // 明细更新
        log.info("batch overtime detail callbackTime = {},businessKey = {},", System.currentTimeMillis(), dto.getBusinessKey());
        WaEmpOvertimeExample example = new WaEmpOvertimeExample();
        example.createCriteria().andBatchIdEqualTo(batchId);
        List<WaEmpOvertime> empOvertimeList = waEmpOvertimeMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(empOvertimeList)) {
            for (WaEmpOvertime waEmpOvertime : empOvertimeList) {
                if (ApprovalStatusEnum.REVOKED.getIndex().equals(waEmpOvertime.getStatus().intValue())) {
                    continue;
                }
                WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                wfCallbackResultDto.setBusinessKey(String.format("%s_%s", waEmpOvertime.getOtId(), BusinessCodeEnum.OVERTIME.getCode()));
                wfCallbackResultDto.setCallbackType(callbackType);
                wfCallbackResultDto.setTenantId(batchOvertime.getTenantId());
                saveWfOtApproval(wfCallbackResultDto);
            }
        }

        log.info("batch overtime end callbackTime = {},businessKey = {},", System.currentTimeMillis(), dto.getBusinessKey());
    }

    /**
     * 批量考勤异常调整回调接口
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchAnalyseAdjustApproval(WfCallbackResultDto dto) {
        log.info("batch analyse adjust callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("batch analyse adjust callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("batch analyse adjust callbackType is error");
            return;
        }
        Long businessId = getBusinessId(dto.getBusinessKey());
        if (businessId == null) {
            log.info("batch analyse adjust businessKey is null!");
            return;
        }
        WaBatchAnalyseResultAdjustDo batchAnalyseResultAdjust = waBatchAnalyseResultAdjustDo.getById(businessId);
        if (null == batchAnalyseResultAdjust) {
            log.info("batch analyse adjust businessId={} data is null!", businessId);
            return;
        }
        switch (callbackType) {
            case REVOKE:
                batchAnalyseResultAdjust.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                break;
            case APPROVED:
                batchAnalyseResultAdjust.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                SpringUtil.getBean(WaBatchAnalyseResultAdjustService.class).updateEmpAnalyseResult(batchAnalyseResultAdjust);
                break;
            case REFUSED:
                batchAnalyseResultAdjust.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
                break;
        }
        batchAnalyseResultAdjust.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        batchAnalyseResultAdjust.setUpdateTime(System.currentTimeMillis());
        batchAnalyseResultAdjust.setLastApprovalTime(batchAnalyseResultAdjust.getUpdateTime());
        waBatchAnalyseResultAdjustDo.updateById(batchAnalyseResultAdjust);

        // 更新明细
        List<WaAnalyseResultAdjustDo> adjustDoList = waAnalyseResultAdjustDo.listByBatchId(businessId);
        if (CollectionUtils.isNotEmpty(adjustDoList)) {
            for (WaAnalyseResultAdjustDo adjustDo : adjustDoList) {
                if (ApprovalStatusEnum.REVOKED.getIndex().equals(adjustDo.getStatus())) {
                    continue;
                }
                adjustDo.setStatus(batchAnalyseResultAdjust.getStatus());
                adjustDo.setUpdateBy(batchAnalyseResultAdjust.getUpdateBy());
                adjustDo.setUpdateTime(System.currentTimeMillis());
                adjustDo.setLastApprovalTime(adjustDo.getUpdateTime());
                waAnalyseResultAdjustDo.updateById(adjustDo);
            }
        }
    }

    /**
     * 加班回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfOtApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("overTime callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null");
            return;
        }
        Long otId = getBusinessId(dto.getBusinessKey());
        if (otId == null) {
            log.info("businessKey is null!");
            return;
        }
        WaEmpOvertime ot = new WaEmpOvertime();
        ot.setOtId(otId.intValue());
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            //审批通过
            ot.setStatus(ApprovalStatusEnum.PASSED.getIndex().shortValue());
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {
            //审批拒绝
            ot.setStatus(ApprovalStatusEnum.REJECTED.getIndex().shortValue());
            this.updatePreOtStatus(ot.getPreOtId(), 0);
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {
            //审批撤销
            ot.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        } else if (WfCallbackTriggerOperationEnum.ERROR == callbackType) {
            log.info("callbackType is error!");
            //审批过程中发生错误
        }
        ot.setLastApprovalTime(DateUtil.getCurrentTime(true));
        waEmpOvertimeMapper.updateByPrimaryKeySelective(ot);
    }

    /**
     * 补卡回调业务处理
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfBdkApproval(WfCallbackResultDto dto) {
        log.warn("bdk callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("callbackType is error");
            return;
        }
        Long recordId = getBusinessId(dto.getBusinessKey());
        if (recordId == null) {
            log.info("businessKey is null!");
            return;
        }
        workFlowApprovalProcessorService.finishedBdkApproval(recordId, callbackType);
    }

    /**
     * 出差回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTravelApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("travel callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("callbackType is error");
            return;
        }
        Long travelId = getBusinessId(dto.getBusinessKey());
        if (travelId == null) {
            log.info("businessKey is null!");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.REVOKE) {
            empTravelMapper.updateWaEmpTravelStatus(travelId, ApprovalStatusEnum.REVOKED.getIndex(), System.currentTimeMillis() / 1000L);
            return;
        }
        /*if (CollectionUtils.isNotEmpty(waWorkflowRevokeDo.getWorkflowRevokeList(dto.getTenantId(), travelId,
                Collections.singletonList(ApprovalStatusEnum.IN_APPROVAL.getIndex()), Collections.singletonList(BusinessCodeEnum.TRAVEL_REVOKE.name())))) {
            //校验是否有审批中的撤销流程，已有审批中的撤销流程不可进行审批操作
            throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_REVOKING_NOT_ALLOWED_REVOKE, Boolean.FALSE).getMsg());
        }*/
        Integer status = null;
        if (callbackType == WfCallbackTriggerOperationEnum.APPROVED) {
            status = 2;
            //出差转调休配额
            travelCompensatoryService.generateCompensatoryQuota(travelId);
        } else if (callbackType == WfCallbackTriggerOperationEnum.REFUSED) {
            status = 3;
        }
        empTravelMapper.updateWaEmpTravelStatus(travelId, status, System.currentTimeMillis() / 1000L);
    }

    /**
     * 批量出差-回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveBatchTravelApproval(WfCallbackResultDto dto) throws Exception {
        log.info("batch travel callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("batch travel callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("batch travel callbackType is error");
            return;
        }
        Long travelId = getBusinessId(dto.getBusinessKey());
        if (travelId == null) {
            log.info("batch travel businessKey is null!");
            return;
        }
        WaBatchTravelDo travelData = waBatchTravelDo.getById(travelId);
        if (null == travelData) {
            log.info("batch travel travelId={} travelData is null!", travelId);
            return;
        }
        switch (callbackType) {
            case REVOKE:
                travelData.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
                break;
            case APPROVED:
                travelData.setStatus(ApprovalStatusEnum.PASSED.getIndex());
                break;
            case REFUSED:
                travelData.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
                break;
        }
        travelData.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        travelData.setUpdateTime(System.currentTimeMillis());
        waBatchTravelDo.updateById(travelData);

        // 明细更新
        List<WaEmpTravelDo> waEmpTravelDos = waEmpTravelDo.listByBatchId(travelData.getTenantId(), travelData.getBatchTravelId());
        if (CollectionUtils.isNotEmpty(waEmpTravelDos)) {
            WaEmpTravelDo empTravel = waEmpTravelDos.get(0);
            if (!ApprovalStatusEnum.REVOKED.getIndex().equals(empTravel.getStatus())) {
                WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
                String businessKey = String.valueOf(empTravel.getTravelId());
                String wfBusKey = String.format("%s_%s", businessKey, BusinessCodeEnum.TRAVEL.getCode());
                wfCallbackResultDto.setBusinessKey(wfBusKey);
                wfCallbackResultDto.setCallbackType(callbackType);
                wfCallbackResultDto.setTenantId(travelData.getTenantId());
                saveTravelApproval(wfCallbackResultDto);
            }
        }
    }

    /**
     * 调班回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveShiftApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("shift change callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("callbackType is error");
            return;
        }
        Long shiftId = getBusinessId(dto.getBusinessKey());
        if (shiftId == null) {
            log.info("businessKey is null!");
            return;
        }
        waShiftApplyService.saveShiftApprovalWorkFlow(shiftId, callbackType);
    }

    /**
     * 销假回调业务处理
     *
     * @param dto
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveLeaveCancelApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("leave cancel callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.warn("callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("callbackType is error");
            return;
        }
        Long leaveCancelId = getBusinessId(dto.getBusinessKey());
        if (leaveCancelId == null) {
            log.warn("businessKey is null!");
            return;
        }
        saveWfLeaveCancelApproval(leaveCancelId, callbackType);
    }

    /**
     * 销假审批回调
     *
     * @throws Exception
     */
    @Transactional
    public void saveWfLeaveCancelApproval(Long leaveCancelId, WfCallbackTriggerOperationEnum choice) throws Exception {
        WaLeaveCancel waLeaveCancel = leaveCancelMapper.selectByPrimaryKey(leaveCancelId);
        if (waLeaveCancel == null) {
            log.warn("saveWfLeaveCancelApproval异常，销假单据系统不存在，leaveCancelId=" + leaveCancelId);
            return;
        }
        WaEmpLeave waEmpLeave = waEmpLeaveMapper.selectByPrimaryKey(waLeaveCancel.getLeaveId());
        if (waEmpLeave == null) {
            log.warn("saveWfLeaveCancelApproval异常，请假单据系统不存在");
            return;
        }
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(waEmpLeave.getLeaveTypeId());
        if (waLeaveType == null) {
            log.warn("saveWfLeaveCancelApproval异常，休假系统不存在");
            return;
        }
        WaEmpLeaveTime leaveTime = null;
        WaEmpLeaveTimeExample timeExample = new WaEmpLeaveTimeExample();
        WaEmpLeaveTimeExample.Criteria empLeaveTimeCri = timeExample.createCriteria();
        empLeaveTimeCri.andLeaveIdEqualTo(waEmpLeave.getLeaveId());
        List<WaEmpLeaveTime> leaveTimeList = waEmpLeaveTimeMapper.selectByExample(timeExample);
        if (CollectionUtils.isNotEmpty(leaveTimeList)) {
            leaveTime = leaveTimeList.get(0);
        }
        WaLeaveCancel cancelUpd = new WaLeaveCancel();
        cancelUpd.setLeaveCancelId(leaveCancelId);
        if (choice == WfCallbackTriggerOperationEnum.APPROVED) {
            cancelUpd.setStatus(ApprovalStatusEnum.PASSED.getIndex().shortValue());
        } else if (choice == WfCallbackTriggerOperationEnum.REFUSED) {
            cancelUpd.setStatus(ApprovalStatusEnum.REJECTED.getIndex().shortValue());
        } else if (choice == WfCallbackTriggerOperationEnum.REVOKE) {
            cancelUpd.setStatus(ApprovalStatusEnum.REVOKED.getIndex().shortValue());
        }
        //1.更改销假单状态
        cancelUpd.setLastApprovalTime(System.currentTimeMillis() / 1000);
        cancelUpd.setUpdateTime(System.currentTimeMillis() / 1000);
        cancelUpd.setTenantId(waLeaveCancel.getTenantId());
        leaveCancelMapper.updateByPrimaryKeySelective(cancelUpd);
        if (ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(cancelUpd.getStatus()))) {
            //2.更新休假单销假时长
            WaEmpLeave updEmpLeave = new WaEmpLeave();
            updEmpLeave.setLeaveId(waEmpLeave.getLeaveId());
            updEmpLeave.setCancelTimeDuration(waEmpLeave.getCancelTimeDuration() + waLeaveCancel.getTimeDuration());
            updEmpLeave.setUpdtime(System.currentTimeMillis() / 1000);
            waEmpLeaveMapper.updateByPrimaryKeySelective(updEmpLeave);
            if (leaveTime != null) {
                WaEmpLeaveTime updEmpLeaveTime = new WaEmpLeaveTime();
                updEmpLeaveTime.setLeaveTimeId(leaveTime.getLeaveTimeId());
                updEmpLeaveTime.setCancelTimeDuration(waEmpLeave.getCancelTimeDuration() + waLeaveCancel.getTimeDuration());
                waEmpLeaveTimeMapper.updateByPrimaryKeySelective(updEmpLeaveTime);
            }
            List<WaLeaveCancelDayTime> list = leaveCancelDayTimeMapper.selectListByCancelId(waLeaveCancel.getLeaveCancelId());
            if (CollectionUtils.isNotEmpty(list)) {
                //对同一天的销假单进行合并计算
                Map<String, List<WaLeaveCancelDayTime>> map = list.stream().collect(Collectors.groupingBy(l -> String.format("%s_%s", l.getLeaveCancelDate(), l.getShiftDefId())));
                List<WaLeaveCancelDayTime> dayTimeList = new ArrayList<>();
                for (Map.Entry<String, List<WaLeaveCancelDayTime>> entry : map.entrySet()) {
                    List<WaLeaveCancelDayTime> dayTimes = entry.getValue();
                    Double duration = dayTimes.stream().mapToDouble(WaLeaveCancelDayTime::getTimeDuration).sum();
                    WaLeaveCancelDayTime dayTime = dayTimes.get(0);
                    dayTime.setTimeDuration(duration.floatValue());
                    dayTimeList.add(dayTime);
                }
                for (WaLeaveCancelDayTime dayTime : dayTimeList) {
                    WaLeaveDaytimeExample example = new WaLeaveDaytimeExample();
                    WaLeaveDaytimeExample.Criteria criteria = example.createCriteria();
                    criteria.andLeaveIdEqualTo(waEmpLeave.getLeaveId());
                    criteria.andLeaveDateEqualTo(dayTime.getLeaveCancelDate());
                    if (Optional.ofNullable(dayTime.getShiftDefId()).isPresent()) {
                        criteria.andUseShiftDefIdEqualTo(dayTime.getShiftDefId());
                    }
                    List<WaLeaveDaytime> daytimeList = leaveDaytimeMapper.selectByExample(example);
                    if (CollectionUtils.isNotEmpty(daytimeList)) {
                        //更新假期明细(每天)的销假时长
                        WaLeaveDaytime waLeaveDaytime = daytimeList.get(0);
                        WaLeaveDaytime updEmpDayTime = new WaLeaveDaytime();
                        updEmpDayTime.setLeaveDaytimeId(waLeaveDaytime.getLeaveDaytimeId());
                        updEmpDayTime.setCancelTimeDuration(waLeaveDaytime.getCancelTimeDuration() + dayTime.getTimeDuration());
                        leaveDaytimeMapper.updateByPrimaryKeySelective(updEmpDayTime);
                        if (waLeaveType.getLeaveType() != 4) {//非产假
                            //3.额度返还
                            Float cancelTimeDuration = dayTime.getTimeDuration();
                            if (waLeaveType.getQuotaRestrictionType() != null && QuotaRestrictionTypeEnum.LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())) {
                                //quota_type is '额度类型： 1 按年发放、2 调休、3 固定额度'
                                Integer quotaType = waLeaveType.getQuotaType();
                                if (quotaType == 2) {
                                    List<WaCompensatoryQuotaUse> quotaUseList = waCompensatoryQuotaUseMapper.selectOrderBy(waEmpLeave.getEmpid(), ConvertHelper.longConvert(waLeaveDaytime.getLeaveDaytimeId()));
                                    for (WaCompensatoryQuotaUse quotaUse : quotaUseList) {
                                        Float timeDuration = quotaUse.getTimeDuration();
                                        if (cancelTimeDuration > timeDuration) {
                                            cancelTimeDuration = cancelTimeDuration - timeDuration;
                                            WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                            updUse.setUseId(quotaUse.getUseId());
                                            updUse.setCancelTimeDuration(timeDuration);
                                            updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                            waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                        } else {
                                            WaCompensatoryQuotaUse updUse = new WaCompensatoryQuotaUse();
                                            updUse.setUseId(quotaUse.getUseId());
                                            updUse.setCancelTimeDuration(cancelTimeDuration + quotaUse.getCancelTimeDuration());
                                            updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                            waCompensatoryQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                            break;
                                        }
                                    }
                                    Long userId = SessionHolder.getUserId();
                                    long updateTime = System.currentTimeMillis() / 1000;
                                    List<Long> idList = new ArrayList<>();
                                    idList.add(ConvertHelper.longConvert(waLeaveDaytime.getLeaveDaytimeId()));
                                    empCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(ConvertHelper.longConvert(userId), updateTime, idList, Integer.valueOf(cancelUpd.getStatus()));
                                } else {
                                    List<WaLeaveQuotaUse> quotaUseList = waLeaveQuotaUseMapper.selectOrderBy(waEmpLeave.getEmpid(), waLeaveDaytime.getLeaveDaytimeId());
                                    for (WaLeaveQuotaUse quotaUse : quotaUseList) {
                                        Float timeDuration = quotaUse.getTimeDuration();
                                        if (cancelTimeDuration > timeDuration) {
                                            cancelTimeDuration = cancelTimeDuration - timeDuration;
                                            WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                            updUse.setUseId(quotaUse.getUseId());
                                            updUse.setCancelTimeDuration(timeDuration);
                                            updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                            waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                        } else {
                                            WaLeaveQuotaUse updUse = new WaLeaveQuotaUse();
                                            updUse.setUseId(quotaUse.getUseId());
                                            updUse.setCancelTimeDuration(cancelTimeDuration + quotaUse.getCancelTimeDuration());
                                            updUse.setUpdateTime(System.currentTimeMillis() / 1000);
                                            waLeaveQuotaUseMapper.updateByPrimaryKeySelective(updUse);
                                            break;
                                        }
                                    }
                                    Long userId = SessionHolder.getUserId();
                                    long updateTime = System.currentTimeMillis() / 1000;
                                    List<Integer> idList = new ArrayList<>();
                                    idList.add(waLeaveDaytime.getLeaveDaytimeId());
                                    waLeaveQuotaUseMapper.updateWaEmpQuota(ConvertHelper.longConvert(userId), updateTime, idList, Integer.valueOf(cancelUpd.getStatus()));
                                }
                            }
                        }
                    }
                }
            }
        }
        //根据不同类型的销假更新休假单状态
        WaEmpLeave updEmpLeave = waEmpLeaveMapper.selectByPrimaryKey(waLeaveCancel.getLeaveId());
        WaLeaveCancel leaveCancel = leaveCancelMapper.selectByPrimaryKey(leaveCancelId);
        dealLeaveStatus(leaveCancel, updEmpLeave);
    }

    /**
     * 不同销假类型对休假单状态的处理
     *
     * @param waLeaveCancel
     * @param updEmpLeave
     */
    public void dealLeaveStatus(WaLeaveCancel waLeaveCancel, WaEmpLeave updEmpLeave) throws Exception {
        log.warn("leave cancel type is:" + LeaveCancelTypeEnum.getName(waLeaveCancel.getTypeId()));
        List<WaLeaveCancel> leaveCancelList = leaveCancelMapper.getLeaveCancelByLeaveId(updEmpLeave.getLeaveId());
        //是否存在审批中的销假单
        boolean flag = leaveCancelList.stream().anyMatch(e -> ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(Integer.valueOf(e.getStatus())));
        if (!flag) {
            boolean pass = leaveCancelList.stream().anyMatch(e -> ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(e.getStatus())));
            //是否存在审批通过的销假单
            if (!pass) {
                updEmpLeave.setLeaveStatus(LeaveCancelStatusEnum.NO_LEAVE_CANCEL.getIndex());
            } else {
                updEmpLeave.setLeaveStatus(LeaveCancelStatusEnum.LEAVE_CANCEL_PASSED.getIndex());
            }
            waEmpLeaveMapper.updateByPrimaryKeySelective(updEmpLeave);
        }
        //如果是休假取消,则撤销休假单,并返还所有额度
        if (Long.valueOf(LeaveCancelTypeEnum.LEAVE_CANCEL.getIndex()).equals(waLeaveCancel.getTypeId())
                && ApprovalStatusEnum.PASSED.getIndex().equals(Integer.valueOf(waLeaveCancel.getStatus()))) {
            //1.先撤销销假单(不包含休假取消本身)
            List<WaLeaveCancel> cancelList = leaveCancelList.stream().filter(e -> !waLeaveCancel.getLeaveCancelId().equals(e.getLeaveCancelId())).collect(Collectors.toList());
            for (WaLeaveCancel cancel : cancelList) {
                RevokeLeaveCancelDto dto = ObjectConverter.convert(cancel, RevokeLeaveCancelDto.class);
                //revokeLeaveCancel(cancel, waLeaveCancel.getReason());
                Result<Boolean> result = empLeaveCancelService.revokeCancelLeave(dto);
                if (result.getCode() != 0) {
                    log.error(result.getMsg());
                }
            }
            //2.再撤销对应的休假单
            //revokeLeave(waLeaveCancel);
            RevokeEmpLeaveDto dto = new RevokeEmpLeaveDto();
            dto.setLeaveId(updEmpLeave.getLeaveId());
            dto.setRecokeReason(waLeaveCancel.getRevokeReason());
            try {
                leaveApplyService.revokeEmpLeave(dto, null, true, true);
            } catch (Exception e) {
                log.error("Revoke leave error:{}", e.getMessage(), e);
            }
        }
        //休假调整，恢复原休假单，删除新地休假单
        if (Long.valueOf(LeaveCancelTypeEnum.TIME_ADJUST.getIndex()).equals(waLeaveCancel.getTypeId())) {
            handleLeave(waLeaveCancel.getLeaveCancelId(), waLeaveCancel.getLeaveId(), waLeaveCancel.getOriginalLeaveId(), waLeaveCancel.getStatus(), waLeaveCancel.getEmpid());
        }
    }

    public void handleLeave(Long leaveCancelId, Integer leaveId, Integer originalLeaveId, Short status, Long empId) throws Exception {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (empInfo == null) {
            return;
        }
        if (status.equals(ApprovalStatusEnum.PASSED.getIndex().shortValue())) {
            String wfBusKey = String.format("%s_%s", leaveId, BusinessCodeEnum.LEAVE.getCode());
            saveWfLeaveApproval(getWfCallbackResultDto(wfBusKey, empInfo.getBelongOrgId(), WfCallbackTriggerOperationEnum.APPROVED));
        } else {
            String wfBusKey = String.format("%s_%s", leaveId, BusinessCodeEnum.LEAVE.getCode());
            saveWfLeaveApproval(getWfCallbackResultDto(wfBusKey, empInfo.getBelongOrgId(), WfCallbackTriggerOperationEnum.REVOKE));
            //删除附件
            WaLeaveFileExample leaveFileExample = new WaLeaveFileExample();
            WaLeaveFileExample.Criteria leaveFileExampleCriteria = leaveFileExample.createCriteria();
            leaveFileExampleCriteria.andLeaveIdEqualTo(leaveId);
            waLeaveFileMapper.deleteByExample(leaveFileExample);
            //删除LeaveDaytime
            WaLeaveDaytimeExample leaveDaytimeExample = new WaLeaveDaytimeExample();
            WaLeaveDaytimeExample.Criteria leaveDaytimeCriteria = leaveDaytimeExample.createCriteria();
            leaveDaytimeCriteria.andLeaveIdEqualTo(leaveId);
            leaveDaytimeMapper.deleteByExample(leaveDaytimeExample);
            //删除leaveTime
            WaEmpLeaveTimeExample empLeaveTimeExample = new WaEmpLeaveTimeExample();
            WaEmpLeaveTimeExample.Criteria empLeaveTimeCriteria = empLeaveTimeExample.createCriteria();
            empLeaveTimeCriteria.andLeaveIdEqualTo(leaveId);
            waEmpLeaveTimeMapper.deleteByExample(empLeaveTimeExample);
            //删除休假单
            waEmpLeaveMapper.deleteByPrimaryKey(leaveId);
        }
    }

    public void handleLeave(WorkflowCallBackHandleLeaveDto dto) throws Exception {
        this.handleLeave(dto.getLeaveCancelId(), dto.getLeaveId(), dto.getOriginalLeaveId(), dto.getStatus(), dto.getEmpId());
    }

    public WfCallbackResultDto getWfCallbackResultDto(String wfBusKey, String tenantId, WfCallbackTriggerOperationEnum callbackType) {
        WfCallbackResultDto wfCallbackResultDto = new WfCallbackResultDto();
        wfCallbackResultDto.setBusinessKey(wfBusKey);
        wfCallbackResultDto.setCallbackType(callbackType);
        wfCallbackResultDto.setTenantId(tenantId);
        return wfCallbackResultDto;
    }

    public Long getBusinessId(String businessKey) {
        if (StringUtils.isEmpty(businessKey)) {
            return null;
        }
        if (businessKey.contains("_")) {
            String[] businessKeys = businessKey.split("_");
            return Long.valueOf(businessKeys[0]);
        }
        return Long.valueOf(businessKey);
    }

    public void updatePreOtStatus(Integer preOtId, Integer status) {
        PreEmpOvertime overtime = new PreEmpOvertime();
        overtime.setPreOtId(preOtId);
        overtime.setUpdtime(DateUtil.getCurrentTime(true));
        overtime.setUseStatus(status);
        preEmpOvertimeMapper.updateByPrimaryKeySelective(overtime);
    }

    /**
     * 调休付现回调业务处理
     *
     * @param dto 入参
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void compensatoryCallback(WfCallbackResultDto dto) throws Exception {
        log.warn("compensatory to case callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("callbackType is error");
            return;
        }
        Long id = getBusinessId(dto.getBusinessKey());
        if (id == null) {
            log.info("businessKey is null!");
            return;
        }
        //更新调休付现
        WaEmpCompensatoryCaseApply empCompensatoryCaseApply = waEmpCompensatoryCaseApplyMapper.selectByPrimaryKey(id);
        if (null == empCompensatoryCaseApply) {
            log.info("empCompensatoryCaseApply is null!");
            return;
        }
        empCompensatoryCaseApply.setId(id);
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            //审批通过
            empCompensatoryCaseApply.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {
            //审批拒绝
            empCompensatoryCaseApply.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {
            //审批撤销
            empCompensatoryCaseApply.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        }
        Long currentTime = DateUtil.getCurrentTime(true);
        Long userId = ConvertHelper.longConvert(SessionHolder.getUserId());
        empCompensatoryCaseApply.setLastApprovalTime(currentTime);
        empCompensatoryCaseApply.setUpdateTime(currentTime);
        empCompensatoryCaseApply.setUpdateBy(userId);
        waEmpCompensatoryCaseApplyMapper.updateByPrimaryKeySelective(empCompensatoryCaseApply);
        List<EmpCompensatoryCaseDo> caseDetailList = empCompensatoryCaseDo.getEmpCompensatoryCase(dto.getTenantId(), id);
        Integer status = empCompensatoryCaseApply.getStatus();
        List<Long> idList = caseDetailList.stream().map(EmpCompensatoryCaseDo::getId).distinct().collect(Collectors.toList());
        QueryWrapper<WaCompensatoryQuotaUse> qw = new QueryWrapper<>();
        qw.in("leave_daytime_id", idList);
        WaCompensatoryQuotaUse waCompensatoryQuotaUse = new WaCompensatoryQuotaUse();
        waCompensatoryQuotaUse.setApprovalStatus(status);
        waCompensatoryQuotaUse.setUpdateBy(userId);
        waCompensatoryQuotaUse.setUpdateTime(currentTime * 1000);
        //更新调休配额use表
        waCompensatoryQuotaUseMapper.update(waCompensatoryQuotaUse, qw);
        //更新调休配额
        empCompensatoryQuotaMapper.updateWaEmpCompensatoryQuota(userId, currentTime * 1000, idList, status);
    }

    /**
     * 加班撤销审批回调业务处理
     *
     * @param dto 入参
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfOtRevokeApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("saveWfOtRevokeApproval callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("saveWfOtRevokeApproval callbackType is null");
            return;
        }
        Long revokeId = getBusinessId(dto.getBusinessKey());
        if (revokeId == null) {
            log.info("saveWfOtRevokeApproval businessKey is null!");
            return;
        }
        String tenantId = dto.getTenantId();
        WaWorkflowRevoke workflowRevoke = new WaWorkflowRevoke();
        workflowRevoke.setId(revokeId);
        List<EmpCompensatoryQuotaDo> quotas = Lists.newArrayList();
        WaEmpOvertime ot = null;
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            //审批通过
            workflowRevoke.setStatus(ApprovalStatusEnum.PASSED.getIndex());
            ot = getEmpOvertime(revokeId);
            if (ApprovalStatusEnum.PASSED.getIndex().equals(ot.getStatus().intValue())) {
                Long otDate = DateUtil.getOnlyDate(new Date(ot.getStartTime() * 1000));
                //审批通过需校验是否有加班单已转调休且已被使用，如有则不允许撤销
                quotas = overtimeApplyService.getOvertimeToCompensatoryQuotas(tenantId, ot.getOtId());
                if (CollectionUtils.isEmpty(quotas)) {
                    quotas = empCompensatoryQuotaDo.getQuotaByDate(tenantId, Collections.singletonList(ot.getEmpid()), otDate, otDate, DataSourceEnum.AUTO.name(), Collections.singletonList(2));
                }
                quotas = quotas.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmpCompensatoryQuotaDo::getQuotaId))), ArrayList::new));
                if (checkCompensatoryQuota(quotas)) {
                    String errMsg = overtimeApplyService.checkQuotaUsed(tenantId, quotas);
                    if (StringUtils.isNotBlank(errMsg)) {
                        throw new CDException(errMsg);
                    } else {
                        // 额度已被使用不可撤销
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_REVOKE_NOT_ALLOW, null).getMsg());
                    }
                }
            }
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {
            //审批拒绝
            workflowRevoke.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {
            //审批撤销
            workflowRevoke.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.ERROR == callbackType) {
            //审批过程中发生错误
            log.info("saveWfOtRevokeApproval callbackType is error!");
            return;
        }
        workflowRevoke.setLastApprovalTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        int rowNum = workflowRevokeMapper.updateByPrimaryKeySelective(workflowRevoke);
        if (rowNum > 0 && WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            WaWorkflowRevoke revoke = workflowRevokeMapper.selectByPrimaryKey(revokeId);
            WaSob waSob = waSobService.getWaSob(ot.getEmpid(), ot.getStartTime());
            //撤销加班
            Result<Boolean> result = overtimeApplyService.revokeOvertimeWorkflow(tenantId, SessionHolder.getUserId(), waSob, ot,
                    revoke.getReason(), ApprovalStatusEnum.REVOKED, quotas, false);
            if (null == result || result.getCode() != 0) {
                throw new CDException(Objects.requireNonNull(result).getMsg() != null ? result.getMsg() : "撤销加班流程失败");
            }
        }
    }

    /**
     * 加班废止审批回调业务处理
     *
     * @param dto 入参
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfOtAbolishApproval(WfCallbackResultDto dto) throws Exception {
        log.warn("saveWfOtAbolishApproval callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("saveWfOtAbolishApproval callbackType is null");
            return;
        }
        Long revokeId = getBusinessId(dto.getBusinessKey());
        if (revokeId == null) {
            log.info("saveWfOtAbolishApproval businessKey is null!");
            return;
        }
        String tenantId = dto.getTenantId();
        WaWorkflowRevoke workflowRevoke = new WaWorkflowRevoke();
        workflowRevoke.setId(revokeId);
        List<EmpCompensatoryQuotaDo> quotas = Lists.newArrayList();
        WaEmpOvertime ot = getEmpOvertime(revokeId);
        Long otDate = DateUtil.getOnlyDate(new Date(ot.getStartTime() * 1000));
        quotas = overtimeApplyService.getOvertimeToCompensatoryQuotas(tenantId, ot.getOtId());
        if (CollectionUtils.isEmpty(quotas)) {
            quotas = empCompensatoryQuotaDo.getQuotaByDate(tenantId, Collections.singletonList(ot.getEmpid()), otDate, otDate, DataSourceEnum.AUTO.name(), Arrays.asList(2, 8));
        }
        quotas = quotas.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(EmpCompensatoryQuotaDo::getQuotaId))), ArrayList::new));
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            //审批通过
            workflowRevoke.setStatus(ApprovalStatusEnum.PASSED.getIndex());
            if (ApprovalStatusEnum.PASSED.getIndex().equals(ot.getStatus().intValue())) {
                //审批通过需校验是否有加班单已转调休且已被使用，如有则不允许废止
                if (checkCompensatoryQuota(quotas)) {
                    String errMsg = overtimeApplyService.checkQuotaUsed(tenantId, quotas);
                    if (StringUtils.isNotBlank(errMsg)) {
                        throw new CDException(errMsg);
                    } else {
                        // 额度已被使用不可撤销
                        throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.QUOTA_USED_REVOKE_NOT_ALLOW, null).getMsg());
                    }
                }
            }
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {
            //审批拒绝
            workflowRevoke.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {
            //审批撤销
            workflowRevoke.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.ERROR == callbackType) {
            //审批过程中发生错误
            log.info("saveWfOtAbolishApproval callbackType is error!");
            return;
        }
        workflowRevoke.setUpdateTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        workflowRevoke.setLastApprovalTime(DateUtil.getCurrentTime(true));
        int rowNum = workflowRevokeMapper.updateByPrimaryKeySelective(workflowRevoke);
        if (rowNum > 0 && WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            WaWorkflowRevoke revoke = workflowRevokeMapper.selectByPrimaryKey(revokeId);
            WaSob waSob = waSobService.getWaSob(ot.getEmpid(), ot.getStartTime());
            //废止加班
            Result<Boolean> result = overtimeApplyService.revokeOvertimeWorkflow(tenantId, SessionHolder.getUserId(), waSob, ot,
                    revoke.getReason(), ApprovalStatusEnum.CANCELLATION, quotas, false);
            if (null == result || result.getCode() != 0) {
                throw new CDException(Objects.requireNonNull(result).getMsg() != null ? result.getMsg() : "废止加班流程失败");
            }
        }
        if (WfCallbackTriggerOperationEnum.APPROVED != callbackType) {
            //配额解冻
            quotas.forEach(quota -> {
                long cur = DateUtil.getCurrentTime(true);
                if (quota.getStatus() == 8) {
                    if (cur <= quota.getLastDate()) {
                        quota.setStatus(2);
                    } else {
                        quota.setStatus(1);
                    }
                }
            });
            empCompensatoryQuotaDo.batchUpdate(quotas);
        }
    }

    /**
     * 校验配额是否已被使用
     *
     * @param quotas 调休配额
     * @return
     */
    public boolean checkCompensatoryQuota(List<EmpCompensatoryQuotaDo> quotas) {
        return CollectionUtils.isNotEmpty(quotas) && quotas.stream().anyMatch(q -> q.getUsedDay() > 0 || q.getInTransitQuota() > 0);
    }

    /**
     * 获取加班单
     *
     * @param revokeId 撤销流程主键
     * @return
     */
    public WaEmpOvertime getEmpOvertime(Long revokeId) {
        WaWorkflowRevoke workflowRevoke = workflowRevokeMapper.selectByPrimaryKey(revokeId);
        if (null == workflowRevoke) {
            return null;
        }
        return waEmpOvertimeMapper.selectByPrimaryKey(workflowRevoke.getEntityId().intValue());
    }

    /**
     * 出差撤销审批回调业务处理
     *
     * @param dto 入参
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTravelRevokeApproval(WfCallbackResultDto dto) {
        log.warn("saveTravelRevokeApproval callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("saveTravelRevokeApproval callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("saveTravelRevokeApproval callbackType is error");
            return;
        }
        Long revokeId = getBusinessId(dto.getBusinessKey());
        if (revokeId == null) {
            log.info("saveTravelRevokeApproval businessKey is null!");
            return;
        }
        WaWorkflowRevoke workflowRevoke = new WaWorkflowRevoke();
        workflowRevoke.setId(revokeId);
        if (callbackType == WfCallbackTriggerOperationEnum.REVOKE) {//撤销
            workflowRevoke.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        } else if (callbackType == WfCallbackTriggerOperationEnum.APPROVED) {//审批通过
            workflowRevoke.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        } else if (callbackType == WfCallbackTriggerOperationEnum.REFUSED) {//拒绝
            workflowRevoke.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        }
        workflowRevoke.setLastApprovalTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        int rowNum = workflowRevokeMapper.updateByPrimaryKeySelective(workflowRevoke);
        if (rowNum > 0 && callbackType == WfCallbackTriggerOperationEnum.APPROVED) {//撤销出差流程
            WaWorkflowRevoke revoke = workflowRevokeMapper.selectByPrimaryKey(revokeId);
            Long travelId = revoke.getEntityId();
            Optional<WaEmpTravel> empTravelOpt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(travelId));
            if (!empTravelOpt.isPresent()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, null).getMsg());
            }
            WaEmpTravelDo empTravel = ObjectConverter.convert(empTravelOpt.get(), WaEmpTravelDo.class);
            Optional<WaTravelType> travelTypeOpt = Optional.ofNullable(travelTypeMapper.selectByPrimaryKey(empTravel.getTravelTypeId()));
            if (!travelTypeOpt.isPresent()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST, null).getMsg());
            }
            WaTravelTypeDo travelType = ObjectConverter.convert(travelTypeOpt.get(), WaTravelTypeDo.class);
            //撤销出差
            Result<Boolean> result = empTravelService.revokeTravelWorkflow(dto.getTenantId(), SessionHolder.getUserId(), empTravel, travelType, revoke.getReason(), ApprovalStatusEnum.REVOKED);
            if (null == result || result.getCode() != 0) {
                throw new CDException(Objects.requireNonNull(result).getMsg() != null ? result.getMsg() : "撤销出差流程失败");
            }
        }
    }

    /**
     * 出差废止审批回调业务处理
     *
     * @param dto 入参
     * @throws Exception
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveTravelAbolishApproval(WfCallbackResultDto dto) {
        log.warn("saveTravelAbolishApproval callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("saveTravelAbolishApproval callbackType is null");
            return;
        }
        if (callbackType == WfCallbackTriggerOperationEnum.ERROR) {
            log.info("saveTravelAbolishApproval callbackType is error");
            return;
        }
        Long revokeId = getBusinessId(dto.getBusinessKey());
        if (revokeId == null) {
            log.info("saveTravelAbolishApproval businessKey is null!");
            return;
        }
        WaWorkflowRevoke workflowRevoke = new WaWorkflowRevoke();
        workflowRevoke.setId(revokeId);
        if (callbackType == WfCallbackTriggerOperationEnum.REVOKE) {//撤销
            workflowRevoke.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        } else if (callbackType == WfCallbackTriggerOperationEnum.APPROVED) {//审批通过
            workflowRevoke.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        } else if (callbackType == WfCallbackTriggerOperationEnum.REFUSED) {//拒绝
            workflowRevoke.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        }
        workflowRevoke.setUpdateTime(DateUtil.getCurrentTime(true));
        workflowRevoke.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        workflowRevoke.setLastApprovalTime(DateUtil.getCurrentTime(true));
        int rowNum = workflowRevokeMapper.updateByPrimaryKeySelective(workflowRevoke);
        if (rowNum > 0 && callbackType == WfCallbackTriggerOperationEnum.APPROVED) {//撤销出差流程
            WaWorkflowRevoke revoke = workflowRevokeMapper.selectByPrimaryKey(revokeId);
            Long travelId = revoke.getEntityId();
            Optional<WaEmpTravel> empTravelOpt = Optional.ofNullable(waEmpTravelMapper.selectByPrimaryKey(travelId));
            if (!empTravelOpt.isPresent()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TIME_NOT_EXIST, null).getMsg());
            }
            WaEmpTravelDo empTravel = ObjectConverter.convert(empTravelOpt.get(), WaEmpTravelDo.class);
            Optional<WaTravelType> travelTypeOpt = Optional.ofNullable(travelTypeMapper.selectByPrimaryKey(empTravel.getTravelTypeId()));
            if (!travelTypeOpt.isPresent()) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.TRAVEL_TYPE_NOT_EXIST, null).getMsg());
            }
            WaTravelTypeDo travelType = ObjectConverter.convert(travelTypeOpt.get(), WaTravelTypeDo.class);
            //恢复出差状态
            Result<Boolean> result = empTravelService.revokeTravelWorkflow(dto.getTenantId(), SessionHolder.getUserId(), empTravel, travelType, revoke.getReason(), ApprovalStatusEnum.CANCELLATION);
            if (null == result || result.getCode() != 0) {
                throw new CDException(Objects.requireNonNull(result).getMsg() != null ? result.getMsg() : "撤销出差流程失败");
            }
        }
    }

    /**
     * 假期延期回调业务处理
     *
     * @param dto
     */
    @Transactional(rollbackFor = Exception.class)
    public void saveWfLeaveExtensionApproval(WfCallbackResultDto dto) {
        log.warn("LeaveExtension callbackTime = {},WfCallbackResultDto = {},", System.currentTimeMillis(), FastjsonUtil.toJson(dto));
        WfCallbackTriggerOperationEnum callbackType = dto.getCallbackType();
        if (callbackType == null) {
            log.info("LeaveExtension callbackType is null!");
            return;
        }
        Long id = getBusinessId(dto.getBusinessKey());
        if (id == null) {
            log.info("LeaveExtension businessKey is null!");
            return;
        }
        WaLeaveExtensionDo leaveExtension = waLeaveExtensionDo.selectByPrimaryKey(id);
        if (leaveExtension == null) {
            log.info("saveWfLeaveExtensionApproval异常，假期延期申请单据不存在，id=" + id);
            throw new CDException("假期延期申请单据不存在");
        }
        WaLeaveExtensionDo leaveExtensionUpd = new WaLeaveExtensionDo();
        leaveExtensionUpd.setId(id);
        leaveExtensionUpd.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
        leaveExtensionUpd.setUpdateTime(DateUtil.getCurrentTime(true));
        leaveExtensionUpd.setLastApprovalTime(DateUtil.getCurrentTime(true));
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {//审批通过
            leaveExtensionUpd.setStatus(ApprovalStatusEnum.PASSED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REFUSED == callbackType) {//审批拒绝
            leaveExtensionUpd.setStatus(ApprovalStatusEnum.REJECTED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.REVOKE == callbackType) {//审批撤销
            leaveExtensionUpd.setStatus(ApprovalStatusEnum.REVOKED.getIndex());
        } else if (WfCallbackTriggerOperationEnum.ERROR == callbackType) {
            //审批过程中发生错误
            log.info("callbackType is error!");
            return;
        }
        waLeaveExtensionDo.update(leaveExtensionUpd);
        if (WfCallbackTriggerOperationEnum.APPROVED == callbackType) {
            WaLeaveType leaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveExtension.getLeaveTypeId());
            if (null != leaveType && leaveType.getLeaveType() == 3) {
                WaEmpCompensatoryQuota empCompensatoryQuota = new WaEmpCompensatoryQuota();
                // 延长假期有效期
                empCompensatoryQuota.setQuotaId(leaveExtension.getQuotaId());
                empCompensatoryQuota.setLastDate(leaveExtension.getEndDate());
                empCompensatoryQuota.setUpdateTime(DateUtil.getCurrentTime(true));
                empCompensatoryQuota.setUpdateBy(ConvertHelper.longConvert(SessionHolder.getUserId()));
                empCompensatoryQuotaMapper.updateById(empCompensatoryQuota);
            } else {
                WaEmpQuota empQuota = new WaEmpQuota();
                // 延长假期有效期
                empQuota.setEmpQuotaId(leaveExtension.getQuotaId().intValue());
                empQuota.setLastDate(leaveExtension.getEndDate());
                empQuota.setUpdtime(DateUtil.getCurrentTime(true));
                empQuota.setUpduser(ConvertHelper.longConvert(SessionHolder.getUserId()));
                waEmpQuotaMapper.updateByPrimaryKeySelective(empQuota);
            }
        }
    }
}