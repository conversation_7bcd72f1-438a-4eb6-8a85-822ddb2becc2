package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.ListTool;
import com.caidao1.ioc.api.RemoteImportService;
import com.caidao1.ioc.util.GridUtil;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.system.mybatis.model.SysCorpOrgExample;
import com.caidao1.wa.mybatis.mapper.WaConfigMapper;
import com.caidao1.wa.mybatis.mapper.WaEmpGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.auth.service.WaAuthPubService;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AnalysisDetailDto;
import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.attendance.service.application.enums.ModuleTypeEnum;
import com.caidaocloud.attendance.service.application.enums.OtTypeEnum;
import com.caidaocloud.attendance.service.application.service.IAnalysisRuleService;
import com.caidaocloud.attendance.service.application.service.IGroupService;
import com.caidaocloud.attendance.service.application.service.user.EmployeeGroupService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.ContractCompanyMapper;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.common.VerifyResult;
import com.caidaocloud.attendance.service.interfaces.dto.group.*;
import com.caidaocloud.constant.CommonConstant;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.excption.ErrorCodes;
import com.caidaocloud.excption.ServerException;
import com.caidaocloud.hrpaas.paas.match.ConditionTree;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.StringUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class GroupService implements IGroupService {
    private final String WA_PLAN_GROUP_TYPE = "wa_plan_group_type";

    @Resource
    private WaAttendanceConfigService waConfigService;
    @Resource
    private ISessionService sessionService;
    @Resource
    private WaGroupMapper waGroupMapper;
    @Resource
    private WaEmpGroupMapper waEmpGroupMapper;
    @Resource
    private RemoteImportService importService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Resource
    private WaAuthPubService waAuthPubService;
    @Resource
    private WaConfigMapper waConfigMapper;
    @Resource
    private WaSobDo waSobDo;
    @Autowired
    private IAnalysisRuleService analysisRuleService;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Resource
    private SysEmpInfoDo sysEmpInfoDo;
    @Resource
    private EmployeeGroupService employeeGroupService;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private AttEmpGroupDo attEmpGroupDo;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Resource
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Resource
    private ContractCompanyMapper contractCompanyMapper;
    @Autowired
    private DataBackupDo dataBackupDo;
    @Autowired
    private WaGroupDo waGroupDo;

    public UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    /**
     * 保存考勤方案
     *
     * @param dto
     */
    @Override
    @Transactional
    public Result<Boolean> saveGroup(GroupInfoDto dto) {
        UserInfo userInfo = this.getUserInfo();
        WaGroup record = ObjectConverter.convert(dto, WaGroup.class);
        if (null != dto.getI18nWaGroupName()) {
            record.setI18nWaGroupName(FastjsonUtil.toJson(dto.getI18nWaGroupName()));
        }
        if (null != dto.getGroupExpCondition()) {
            record.setGroupExpCondition(FastjsonUtil.toJson(dto.getGroupExpCondition()));
        }
        String belongOrgId = userInfo.getTenantId();
        /*Result chkResult = checkWaGroupAllEmp(record, belongOrgId);
        if (null != chkResult) {
            return chkResult;
        }*/
        // 初始化部分数据
        doOutLimitControl(record, dto.getOutLimitControl(), dto.getOutLimit());
        WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
        WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
        if (record.getWaGroupId() == null) {
            // 所选适用人员是否已存在于其他方案
            if (!dto.getIsDefault() && CollectionUtils.isNotEmpty(dto.getEmpIds())) {
                criteria.andEmpidIn(dto.getEmpIds());
                List<WaEmpGroup> empGroups = waEmpGroupMapper.selectByExample(empGroupExample);
                if (CollectionUtils.isNotEmpty(empGroups)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.SELECTED_APPLICABLE_ALREADY_OTHER_GROUP, Boolean.FALSE);
                }
            }
            record.setBelongOrgid(belongOrgId);
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            waGroupMapper.insertSelective(record);
            /*if (!dto.getIsDefault() && CollectionUtils.isNotEmpty(dto.getEmpIds())) {
                this.saveBatchEmpGroups(record.getWaGroupId(), dto.getEmpIds());
            }*/
        } else {
            if (!dto.getIsDefault() && CollectionUtils.isNotEmpty(dto.getEmpIds())) {
                // 所选适用人员是否已存在于其他方案
                criteria.andEmpidIn(dto.getEmpIds());
                criteria.andWaGroupIdNotEqualTo(record.getWaGroupId());
                List<WaEmpGroup> empGroups = waEmpGroupMapper.selectByExample(empGroupExample);
                if (CollectionUtils.isNotEmpty(empGroups)) {
                    return ResponseWrap.wrapResult(AttendanceCodes.SELECTED_APPLICABLE_ALREADY_OTHER_GROUP, Boolean.FALSE);
                }
                WaEmpGroupExample example = new WaEmpGroupExample();
                WaEmpGroupExample.Criteria empCriteria = example.createCriteria();
                empCriteria.andWaGroupIdEqualTo(record.getWaGroupId());
                waEmpGroupMapper.deleteByExample(example);
                /*if (!dto.getIsDefault() && CollectionUtils.isNotEmpty(dto.getEmpIds())) {
                    this.saveBatchEmpGroups(record.getWaGroupId(), dto.getEmpIds());
                }*/
            }
            record.setUpduser(userInfo.getUserId());
            record.setUpdtime(DateUtil.getCurrentTime(true));
            waGroupMapper.updateByPrimaryKeySelective(record);
        }
        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    @Override
    @Transactional
    public Result<Boolean> saveGroupInfo(PlanGroupInfoDto dto) {
        UserInfo userInfo = this.getUserInfo();
        WaGroup record = ObjectConverter.convert(dto, WaGroup.class);
        if (null != dto.getI18nWaGroupName()) {
            LogRecordContext.putVariable("name", dto.getI18nWaGroupName().get("default"));
            record.setI18nWaGroupName(FastjsonUtil.toJson(dto.getI18nWaGroupName()));
        }
        if (null != dto.getGroupExpCondition()) {
            record.setGroupExpCondition(FastjsonUtil.toJson(dto.getGroupExpCondition()));
        }
        String belongOrgId = userInfo.getTenantId();
        //校验名称重复
        Result<Boolean> result = checkWaGroupName(record, belongOrgId);
        if (null != result) {
            return result;
        }
        // 初始化部分数据
        doOutLimitControl(record, dto.getOutLimitControl(), dto.getOutLimit());
        //员工分组
        EmployeeGroupDto employeeGroupDto = new EmployeeGroupDto();
        employeeGroupDto.setGroupType(WA_PLAN_GROUP_TYPE);
        employeeGroupDto.bulidExpression(dto.getGroupExp(), dto.getGroupNote());
        if (record.getWaGroupId() == null) {
            LogRecordContext.putVariable("operate", "新增");
            record.setBelongOrgid(belongOrgId);
            record.setCrtuser(userInfo.getUserId());
            record.setCrttime(DateUtil.getCurrentTime(true));
            record.setUpduser(record.getCrtuser());
            record.setUpdtime(record.getCrttime());
            waGroupMapper.insertSelective(record);
            bulidEmployeeGroupDto(employeeGroupDto, record.getWaGroupId());
            employeeGroupService.saveOrUpdate(employeeGroupDto);
            return ResponseWrap.wrapResult(Boolean.TRUE);
        }
        LogRecordContext.putVariable("operate", "编辑");
        record.setUpduser(userInfo.getUserId());
        record.setUpdtime(DateUtil.getCurrentTime(true));
        waGroupMapper.updateByPrimaryKeySelective(record);
        if (null == record.getGroupExpCondition()) {
            waGroupDo.updateGroupExpCondition(record.getWaGroupId(), record.getGroupExpCondition());
        }
        bulidEmployeeGroupDto(employeeGroupDto, record.getWaGroupId());
        employeeGroupService.saveOrUpdate(employeeGroupDto);

        return ResponseWrap.wrapResult(Boolean.TRUE);
    }

    private void bulidEmployeeGroupDto(EmployeeGroupDto employeeGroupDto, Integer waGroupId) {
        employeeGroupDto.setBusinessKey(String.valueOf(waGroupId));
        employeeGroupDto.setGroupName(String.format("%s-%s", WA_PLAN_GROUP_TYPE, employeeGroupDto.getBusinessKey()));
    }

    private Result checkWaGroupAllEmp(WaGroup record, String belongOrgId) {
        // 校验是否存在使用全部员工的方案
        if (!BooleanUtils.isTrue(record.getIsDefault())) {
            return null;
        }

        WaGroupExample example = new WaGroupExample();
        WaGroupExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(belongOrgId);
        criteria.andIsDefaultEqualTo(true);
        if (record.getWaGroupId() != null) {
            criteria.andWaGroupIdNotEqualTo(record.getWaGroupId());
        }

        List<WaGroup> groups = waGroupMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(groups)) {
            Optional<String> optional = groups.stream().map(WaGroup::getWaGroupName).findFirst();
            if (optional.isPresent()) {
                return ResponseWrap.wrapResult(ErrorCodes.UNKNOW_ERROR, "已存在一个【适用全部员工】的考勤方案:" + optional.get(), CommonConstant.FALSE);
            }
        }

        return null;
    }

    private Result<Boolean> checkWaGroupName(WaGroup record, String belongOrgId) {
        if (StringUtils.isBlank(record.getWaGroupName())) {
            return ResponseWrap.wrapResult(AttendanceCodes.WA_GROUP_NAME_NOT_NULL, Boolean.FALSE);
        }
        WaGroupExample example = new WaGroupExample();
        WaGroupExample.Criteria criteria = example.createCriteria();
        criteria.andBelongOrgidEqualTo(belongOrgId);
        criteria.andWaGroupNameEqualTo(record.getWaGroupName().trim());
        if (record.getWaGroupId() != null) {
            criteria.andWaGroupIdNotEqualTo(record.getWaGroupId());
        }
        List<WaGroup> waGroups = waGroupMapper.selectByExample(example);
        if (CollectionUtils.isNotEmpty(waGroups)) {
            return ResponseWrap.wrapResult(AttendanceCodes.WA_GROUP_NAME_EXIST, Boolean.FALSE);
        }
        return null;
    }

    private boolean checkEmpRepeat(Long corpid, PlanGroupInfoDto dto, Integer waGroupId, List<Long> empIds) {
        WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
        WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpid, dto.getGroupExp(), null);
        // 所选适用人员是否已存在于其他方案
        if (!dto.getIsDefault() && CollectionUtils.isNotEmpty(groupEmpIds)) {
            empIds.addAll(groupEmpIds);
            criteria.andEmpidIn(empIds);

            if (null != waGroupId) {
                criteria.andWaGroupIdNotEqualTo(waGroupId);
            }

            List<WaEmpGroup> empGroups = waEmpGroupMapper.selectByExample(empGroupExample);
            if (CollectionUtils.isNotEmpty(empGroups)) {
                return CommonConstant.TRUE;
            }
        }

        return CommonConstant.FALSE;
    }

    private void doOutLimitControl(WaGroup record, Boolean outLimitControl, Integer outLimit) {
        //超出上限控制是否可申请:true 可申请/ false 不可申请
        if (BooleanUtils.isTrue(outLimitControl)) {
            //可申请
            record.setOtToplimit(outLimit);
            record.setIsOpenOtlimitWarn(true);
            record.setIsSetOvertimeLimit(false);
        } else {
            //不可申请
            record.setOvertimeLimit(outLimit);
            record.setIsSetOvertimeLimit(true);
            record.setIsOpenOtlimitWarn(false);
        }
        record.setLeaveGroupId(0);
        record.setOvertimeGroupId(0);
        record.setWaGroupName(record.getWaGroupName().trim());
        if (record.getOtTypeIds() != null && record.getOtTypeIds() instanceof String) {
            int[] ids = Arrays.stream(((String) record.getOtTypeIds()).split(",")).mapToInt(Integer::parseInt).toArray();
            Integer[] ids2 = Arrays.stream(ids).boxed().toArray(Integer[]::new);
            record.setOtTypeIds(ids2);
        }
        if (record.getLeaveTypeIds() != null && record.getLeaveTypeIds() instanceof String) {
            int[] ids = Arrays.stream(((String) record.getLeaveTypeIds()).split(",")).mapToInt(Integer::parseInt).toArray();
            Integer[] ids2 = Arrays.stream(ids).boxed().toArray(Integer[]::new);
            record.setLeaveTypeIds(ids2);
        }
    }

    private void saveBatchEmpGroups(Integer waGroupId, List<Long> empIds) {
        List<WaEmpGroup> groups = new ArrayList<>();
        empIds.forEach(empId -> {
            WaEmpGroup empGroup = new WaEmpGroup();
            empGroup.setEmpid(empId);
            empGroup.setWaGroupId(waGroupId);
            empGroup.setStartTime(1L);
            empGroup.setEndTime(253402185600L);
            groups.add(empGroup);
        });
        importService.fastInsertList(WaEmpGroup.class, "empGroupId", groups);
    }

    /**
     * 查询考勤方案分页列表
     *
     * @param pageBean
     * @return
     */
    @Override
    public AttendancePageResult<GroupDto> getGroupList(PageBean pageBean) {
        List<Map> list = waConfigService.getWaGroupList(pageBean);
        if (CollectionUtils.isEmpty(list)) {
            return new AttendancePageResult<>();
        }
        PageList<Map> pageList = (PageList<Map>) list;
        List<GroupDto> items = JSON.parseArray(JSON.toJSONString(pageList), GroupDto.class);
        items.forEach(item -> {
            item.setWaGroupName(LangParseUtil.getI18nLanguage(item.getI18nWaGroupName(), item.getWaGroupName()));
        });
        return new AttendancePageResult<>(items, pageList.getPaginator(), pageBean.getPage(), pageBean.getCount());
    }

    /**
     * 查询考勤方案详情
     *
     * @param id
     * @return
     * @throws Exception
     */
    @Override
    public GroupDetailDto getGroupInfo(Integer id) throws Exception {
        WaGroup model = waConfigService.getWaGroup(id);
        AnalysisDetailDto analysisDetailDto = analysisRuleService.getGroupInfo(model.getParseGroupId());
        if (analysisDetailDto == null) {
            model.setParseGroupId(null);
        }
        Map map = GridUtil.convertBean2Form(model);
        Map dataMap = (Map) map.get("data");
        String i18nWaGroupName = null;
        if (dataMap.containsKey("i18nWaGroupName")) {
            i18nWaGroupName = (String) dataMap.get("i18nWaGroupName");
            dataMap.remove("i18nWaGroupName");
        }
        String groupExpCondition = null;
        if (dataMap.containsKey("groupExpCondition")) {
            groupExpCondition = (String) dataMap.get("groupExpCondition");
            dataMap.remove("groupExpCondition");
        }
        GroupDetailDto dto = JSON.parseObject(JSON.toJSONString(dataMap), GroupDetailDto.class);
        if (StringUtils.isNotBlank(i18nWaGroupName)) {
            dto.setI18nWaGroupName(FastjsonUtil.toObject(i18nWaGroupName, Map.class));
        } else if (StringUtils.isNotBlank(model.getWaGroupName())) {
            Map<String, String> i18nName = new HashMap<>();
            i18nName.put("default", model.getWaGroupName());
            dto.setI18nWaGroupName(i18nName);
        }
        if (StringUtils.isNotBlank(groupExpCondition)) {
            dto.setGroupExpCondition(FastjsonUtil.toObject(model.getGroupExpCondition(), ConditionTree.class));
        }
        // 初始化部分数据
        if (BooleanUtils.isTrue(model.getIsSetOvertimeLimit())) {
            //加班超出上限不可申请
            dto.setOutLimitControl(false);
            dto.setOutLimit(model.getOvertimeLimit());
        } else if (BooleanUtils.isTrue(model.getIsOpenOtlimitWarn())) {
            //加班超出上限可申请
            dto.setOutLimitControl(true);
            dto.setOutLimit(model.getOtToplimit());
        }
        // 查询方案已分配员工
        WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
        WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
        criteria.andWaGroupIdEqualTo(model.getWaGroupId());
        List<WaEmpGroup> models = waEmpGroupMapper.selectByExample(empGroupExample);
        List<Long> empIds = models.stream().map(WaEmpGroup::getEmpid).distinct().collect(Collectors.toList());
        List<KeyValue> employees = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(empIds)) {
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            SysEmpInfoExample.Criteria empInfoExampleCriteria = empInfoExample.createCriteria();
            empInfoExampleCriteria.andEmpidIn(empIds);
            // 根据empId查询员工详情
            List<SysEmpInfo> empInfos = sysEmpInfoMapper.selectByExample(empInfoExample);
            empInfos.forEach(empInfo -> employees.add(new KeyValue(empInfo.getEmpName(), empInfo.getEmpid())));
        }
        dto.setEmployees(employees);
        dto.setEmpIds(empIds);
        EmployeeGroupDto employeeGroupDto = employeeGroupService.getEmployeeGroup(String.valueOf(id), WA_PLAN_GROUP_TYPE);
        if (null != employeeGroupDto) {
            dto.setGroupExp(employeeGroupDto.getGroupExp());
            dto.setGroupNote(employeeGroupDto.getGroupNote());
        }
        return dto;
    }

    /**
     * 删除考勤方案
     *
     * @param id
     */
    @Override
    @Transactional
    public void deleteGroup(Integer id) {
        UserInfo userInfo = this.getUserInfo();
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(id));
        if (optional.isPresent()) {
            //检查方案是否已被使用
            int count = waSobDo.getSobCountByGroupId(id, ConvertHelper.longConvert(userInfo.getTenantId()), userInfo.getTenantId());
            if (count > 0) {
                //throw new ServerException("考勤方案已使用，不允许删除");
                throw new ServerException(ResponseWrap.wrapResult(AttendanceCodes.DELETE_NOT_ALLOW, null).getMsg());
            }
            WaGroup group = optional.get();
            // 删除分析规则
            if (group.getParseGroupId() != null) {
                analysisRuleService.deleteRuleById(group.getParseGroupId());
            }
            // 删除加班规则
            if (group.getOtTypeIds() != null) {
                Integer[] otTypeIds = (Integer[]) group.getOtTypeIds();
                overTimeTypeDo.deleteOtTypeByIds(Arrays.stream(otTypeIds).collect(Collectors.toList()));
            }
            // 删除已分配员工数据
            WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
            WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
            criteria.andWaGroupIdEqualTo(id);
            waEmpGroupMapper.deleteByExample(empGroupExample);
            // 删除考勤方案
            waConfigService.deleteWaGroup(id);
            // 删除考勤方案对应的员工分组
            employeeGroupService.removeBusKey(String.valueOf(id), WA_PLAN_GROUP_TYPE);
        }
    }

    /**
     * 查询考勤方案下拉框
     *
     * @return
     */
    @Override
    public List<Map> selectGroupOptions() {
        return waConfigService.getWaGroupList(new PageBean(true));
    }

    /**
     * 校验已选员工是否已适用某方案
     *
     * @param planId
     * @param empIds
     * @return
     */
    @Override
    public List<VerifyResult> verifySelectedEmployees(Integer planId, List<Long> empIds) {
        WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
        WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
        criteria.andEmpidIn(empIds);
        // 若是编辑操作，则需要排除实体适用员工
        if (null != planId) {
            criteria.andWaGroupIdNotEqualTo(planId);
        }
        List<WaEmpGroup> examples = waEmpGroupMapper.selectByExample(empGroupExample);
        List<VerifyResult> items = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(examples)) {
            // 查询方案信息
            List<Integer> planIds = examples.stream().map(WaEmpGroup::getWaGroupId).distinct().collect(Collectors.toList());
            WaGroupExample waGroupExample = new WaGroupExample();
            WaGroupExample.Criteria waGroupExampleCriteria = waGroupExample.createCriteria();
            waGroupExampleCriteria.andWaGroupIdIn(planIds);
            List<WaGroup> plans = waGroupMapper.selectByExample(waGroupExample);
            Map<Integer, String> planMap = plans.stream().collect(Collectors.toMap(WaGroup::getWaGroupId, WaGroup::getWaGroupName));
            // 查询员工信息
            List<Long> conflictEmpIds = examples.stream().map(WaEmpGroup::getEmpid).distinct().collect(Collectors.toList());
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            SysEmpInfoExample.Criteria empInfoExampleCriteria = empInfoExample.createCriteria();
            empInfoExampleCriteria.andEmpidIn(conflictEmpIds);
            // 根据empId查询员工详情
            List<SysEmpInfo> employees = sysEmpInfoMapper.selectByExample(empInfoExample);
            Map<Long, String> empMap = employees.stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, SysEmpInfo::getEmpName));
            // 详细信息
            examples.forEach(e -> items.add(new VerifyResult(e.getWaGroupId(), planMap.get(e.getWaGroupId()), e.getEmpid(), empMap.get(e.getEmpid()))));
            empIds.removeAll(conflictEmpIds);
        }
        empIds.forEach(empId -> items.add(new VerifyResult(null, "", empId, "")));
        return items;
    }

    @Override
    @CDText(exp = {"employType:empType" + TextAspect.DICT_E,
            "empStats" + TextAspect.STATUS_ENUM,
            "workCity" + TextAspect.PLACE,
    }, classType = Map.class)
    public List<Map> getEmpGroupList(Integer planId, PageBean pageBean) {
        UserInfo userInfo = getUserInfo();
        Map params = new HashMap();
        params.put("belongId", userInfo.getTenantId());
        params.put("filter", pageBean.getFilter());
        String str = this.waAuthPubService.getDataScope(36, "ei", "belong_org_id='" + userInfo.getTenantId() + "'");
        params.put("datafilter", str + " and eg.wa_group_id=" + planId);
        String order = StringUtils.isBlank(pageBean.getOrder()) ? "emp_group_id" : pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(order));
        List<Map> list = waConfigMapper.getEmpGroupList(pageBounds, params);
        list.stream().forEach(map -> {
            if (map.get("hireDate") != null) {
                Long hireDate = (Long) map.get("hireDate");
                map.put("hireDate", DateUtil.getDateStrByTimesamp(hireDate));
            }
            if (map.get("i18n_wa_group_name") != null) {
                String i18nWaGroupName = (String) map.get("i18n_wa_group_name");
                String i18n = LangParseUtil.getI18nLanguage(i18nWaGroupName, null);
                if (StringUtil.isNotBlank(i18n)) {
                    map.put("wa_group_name", i18n);
                }
            }
        });
        return list;
    }

    /**
     * 更新考勤方案的假期规则
     *
     * @param groupId
     * @param leaveTypeId
     * @param leaveTypeName
     */
    @Override
    public void updateGroupLeaveType(Integer groupId, Integer leaveTypeId, String leaveTypeName) {
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            group.setUpduser(getUserInfo().getUserId());
            group.setUpdtime(System.currentTimeMillis());
            if (group.getLeaveTypeIds() != null) {
                Integer[] leaveTypeIdArr = (Integer[]) group.getLeaveTypeIds();

                List<Integer> allIds = new ArrayList<>(Arrays.asList(leaveTypeIdArr));
                if (!allIds.contains(leaveTypeId)) {
                    allIds.add(leaveTypeId);
                }
                WaLeaveTypeExample example = new WaLeaveTypeExample();
                example.createCriteria().andLeaveTypeIdIn(allIds);
                List<WaLeaveType> leaveTypeList = waLeaveTypeMapper.selectByExample(example);
                if (CollectionUtils.isNotEmpty(leaveTypeList)) {
                    List<String> names = leaveTypeList.stream().map(WaLeaveType::getLeaveName).collect(Collectors.toList());
                    List<Integer> ids = leaveTypeList.stream().map(WaLeaveType::getLeaveTypeId).collect(Collectors.toList());
                    group.setLeaveTypeNames(String.join(",", names));
                    group.setLeaveTypeIds(ids.toArray(new Integer[ids.size()]));
                }
            } else {
                group.setLeaveTypeIds(new Integer[]{leaveTypeId});
                group.setLeaveTypeNames(leaveTypeName);
            }
            waGroupMapper.updateByPrimaryKeySelective(group);
        }
    }

    /**
     * 更新考勤方案的加班规则
     *
     * @param groupId
     * @param otTypeId
     */
    @Override
    public void updateGroupOvertimeType(Integer groupId, Integer otTypeId, Integer dateType) {
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            group.setUpduser(getUserInfo().getUserId());
            group.setUpdtime(System.currentTimeMillis());
            String otTypeName = OtTypeEnum.getDescByIndex(dateType);
            if (group.getOtTypeIds() != null) {
                Integer[] otIdArr = (Integer[]) group.getOtTypeIds();
                if (otIdArr != null && otIdArr.length > 0) {
                    List<Integer> otIds = Arrays.stream(otIdArr).collect(Collectors.toList());
                    if (!otIds.contains(otTypeId)) {
                        otIds.add(otTypeId);
                        group.setOtTypeIds(otIds.toArray(new Integer[otIds.size()]));
                        group.setOtTypeNames(String.format("%s,%s", group.getOtTypeNames(), otTypeName));
                    }
                }
            } else {
                group.setOtTypeIds(new Integer[]{otTypeId});
                group.setOtTypeNames(otTypeName);
            }
            waGroupMapper.updateByPrimaryKeySelective(group);
        }
    }

    /**
     * 删除考勤方案的假期规则
     *
     * @param groupId
     * @param leaveTypeId
     */
    @Override
    public void deleteGroupLeaveType(Integer groupId, Integer leaveTypeId) {
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            if (group.getLeaveTypeIds() != null) {
                group.setUpduser(getUserInfo().getUserId());
                group.setUpdtime(System.currentTimeMillis());
                Integer[] leaveTypeIdArr = (Integer[]) group.getLeaveTypeIds();
                if (leaveTypeIdArr != null && leaveTypeIdArr.length > 0) {
                    List<Integer> leaveTypeIds = Arrays.stream(leaveTypeIdArr).collect(Collectors.toList());
                    if (leaveTypeIds.contains(leaveTypeId)) {
                        String[] leaveTypeNameArr = group.getLeaveTypeNames().split(",");
                        List<String> leaveTypeNames = Arrays.stream(leaveTypeNameArr).collect(Collectors.toList());
                        for (int i = 0; i < leaveTypeIdArr.length; i++) {
                            if (leaveTypeId.equals(leaveTypeIdArr[i])) {
                                leaveTypeNames.remove(leaveTypeNameArr[i]);
                            }
                        }
                        leaveTypeIds.remove(leaveTypeId);
                        if (CollectionUtils.isEmpty(leaveTypeIds) || CollectionUtils.isEmpty(leaveTypeNames)) {
                            group.setLeaveTypeIds(null);
                            group.setLeaveTypeNames(null);
                        } else {
                            group.setLeaveTypeIds(leaveTypeIds.toArray(new Integer[leaveTypeIds.size()]));
                            group.setLeaveTypeNames(String.join(",", leaveTypeNames));
                        }
                    }
                }
                waGroupMapper.updateByPrimaryKeySelective(group);
            }
        }
    }

    /**
     * 删除考勤方案的加班规则
     *
     * @param groupId
     * @param otTypeId
     */
    @Override
    public void deleteGroupOtType(Integer groupId, Integer otTypeId) {
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            if (group.getOtTypeIds() != null) {
                group.setUpduser(getUserInfo().getUserId());
                group.setUpdtime(System.currentTimeMillis());
                Integer[] otTypeIdArr = (Integer[]) group.getOtTypeIds();
                if (otTypeIdArr != null && otTypeIdArr.length > 0) {
                    List<Integer> otTypeIds = Arrays.stream(otTypeIdArr).collect(Collectors.toList());
                    if (otTypeIds.contains(otTypeId)) {
                        String[] otTypeNameArr = group.getOtTypeNames().split(",");
                        List<String> otTypeNames = Arrays.stream(otTypeNameArr).collect(Collectors.toList());
                        for (int i = 0; i < otTypeIdArr.length; i++) {
                            if (otTypeIdArr[i].equals(otTypeId))
                                otTypeNames.remove(otTypeNameArr[i]);
                        }
                        otTypeIds.remove(otTypeId);
                        if (CollectionUtils.isEmpty(otTypeIds) || CollectionUtils.isEmpty(otTypeNames)) {
                            group.setOtTypeIds(null);
                            group.setOtTypeNames(null);
                        } else {
                            group.setOtTypeIds(otTypeIds.toArray(new Integer[otTypeIds.size()]));
                            group.setOtTypeNames(String.join(",", otTypeNames));
                        }
                    }
                }
                waGroupMapper.updateByPrimaryKey(group);
            }
        }
    }

    /**
     * 保存考勤方案的基本信息
     *
     * @param dto
     */
    @Override
    public void saveGroupBaseInfo(GroupBaseInfoDto dto) {
        Optional<WaGroup> opt = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(dto.getWaGroupId()));
        if (opt.isPresent()) {
            WaGroup group = new WaGroup();
            group.setWaGroupId(opt.get().getWaGroupId());
            // 初始化部分数据
            // 超出上限控制是否可申请:true 可申请/ false 不可申请
            Boolean outLimitControl = dto.getOutLimitControl();
            group.setIsOpenOtlimitWarn(outLimitControl);
            group.setIsSetOvertimeLimit(!outLimitControl);
            if (outLimitControl) {
                //可申请
                group.setOtToplimit(dto.getOutLimit());
            } else {
                //不可申请
                group.setOvertimeLimit(dto.getOutLimit());
            }
            if (dto.getReasonMust() != null) {
                group.setReasonMust(dto.getReasonMust());
            }
            group.setOvertimeBelong(dto.getOvertimeBelong());
            group.setOvertimeTypeControl(dto.getOvertimeTypeControl());
            group.setUpduser(getUserInfo().getUserId());
            group.setUpdtime(System.currentTimeMillis());
            group.setOvertimeControl(dto.getOvertimeControl());
            group.setIsOpenTimeControl(dto.getIsOpenTimeControl());
            group.setTimeControlType(dto.getTimeControlType());
            group.setControlTimeDuration(dto.getControlTimeDuration());
            group.setControlTimeUnit(dto.getControlTimeUnit());
            group.setSelectOvertimeDate(dto.getSelectOvertimeDate());
            waGroupMapper.updateByPrimaryKeySelective(group);
        }
    }

    @Override
    public void updateGroupParseGroupId(Integer groupId, Integer parseGroupId) {
        Optional<WaGroup> optional = Optional.ofNullable(waGroupMapper.selectByPrimaryKey(groupId));
        if (optional.isPresent()) {
            WaGroup group = optional.get();
            group.setUpduser(getUserInfo().getUserId());
            group.setUpdtime(System.currentTimeMillis());
            group.setParseGroupId(parseGroupId);
            waGroupMapper.updateByPrimaryKeySelective(group);
        }

    }

    private void doParseFilterList(AttEmpGroupReqDto dto) {
        if (CollectionUtils.isEmpty(dto.getFilterList())) {
            return;
        }
        Iterator<FilterBean> it = dto.getFilterList().iterator();
        while (it.hasNext()) {
            FilterBean filterBean = it.next();
            // 方案状态
            if ("effectiveStatus".equals(filterBean.getField())) {
                if (StringUtils.isNotBlank(filterBean.getMin())) {
                    dto.setEffectiveStatus(filterBean.getMin());
                }
                it.remove();
            }
        }
    }

    @Override
    @CDText(exp = {"empStyle:empStyleName" + TextAspect.DICT_E, "empStatus:empStatusName" + TextAspect.STATUS_ENUM}, classType = AttEmpGroupDto.class)
    public AttendancePageResult<AttEmpGroupDto> getWaEmpGroupList(AttEmpGroupReqDto dto, UserInfo userInfo) {
        dto.setBelongOrgId(userInfo.getTenantId());
        doParseFilterList(dto);
        AttendancePageResult<AttEmpGroupDto> dtoPageResult = new AttendancePageResult<>();
        AttendancePageResult<AttEmpGroupDo> pageResult = attEmpGroupDo.getWaEmpGroupList(dto);
        if (pageResult != null && pageResult.getItems() != null && pageResult.getItems().size() > 0) {
            List<AttEmpGroupDo> doList = pageResult.getItems();
            List<AttEmpGroupDto> dtoList = ObjectConverter.convertList(doList, AttEmpGroupDto.class);
            Long nowDate = DateUtil.getOnlyDate();
            dtoList.forEach(g -> {
                EmpInfoDTO empInfoDTO = ObjectConverter.convert(g, EmpInfoDTO.class);
                empInfoDTO.setName(g.getEmpName());
                empInfoDTO.setWorkno(g.getWorkNo());
                g.setEmpInfo(empInfoDTO);
                g.setWaGroupName(LangParseUtil.getI18nLanguage(g.getI18nWaGroupName(), g.getWaGroupName()));
                g.doSetEffectiveStatus(nowDate);
            });
            dtoPageResult.setItems(dtoList);
            dtoPageResult.setPageNo(pageResult.getPageNo());
            dtoPageResult.setPageSize(pageResult.getPageSize());
            dtoPageResult.setTotal(pageResult.getTotal());
        }
        return dtoPageResult;
    }

    @Override
    public AttEmpGroupDto getWaEmpGroupById(Integer id) {
        AttEmpGroupDo groupDo = attEmpGroupDo.getWaEmpGroupById(id);
        AttEmpGroupDto groupDto = ObjectConverter.convert(groupDo, AttEmpGroupDto.class);
        SysEmpInfo empInfo = sysEmpInfoDo.getEmpInfoById(getUserInfo().getTenantId(), groupDo.getEmpId());
        EmpInfoDTO empInfoDTO = ObjectConverter.convert(empInfo, EmpInfoDTO.class);
        empInfoDTO.setEmpId(empInfo.getEmpid());
        empInfoDTO.setName(empInfo.getEmpName());
        groupDto.setEmpInfo(empInfoDTO);
        return groupDto;
    }

    @Override
    @Transactional
    public void deleteWaEmpGroup(Integer id) {
        Optional<AttEmpGroupDo> opt = Optional.ofNullable(attEmpGroupDo.getWaEmpGroupById(id));
        attEmpGroupDo.deleteWaEmpGroup(Collections.singletonList(id));
        opt.ifPresent(e -> dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_GROUP.name(), ModuleTypeEnum.EMP_GROUP.getTable(), Collections.singletonList(opt.get()), getUserInfo())));
    }

    @Override
    @Transactional
    public void deleteWaEmpGroups(List<Integer> ids) {
        List<AttEmpGroupDo> list = attEmpGroupDo.getWaEmpGroupByIds(ids);
        attEmpGroupDo.deleteWaEmpGroup(ids);
        if (CollectionUtils.isNotEmpty(list)) {
            dataBackupDo.save(new DataBackupDo(ModuleTypeEnum.EMP_GROUP.name(), ModuleTypeEnum.EMP_GROUP.getTable(), list, getUserInfo()));
        }
    }

    @Override
    @Transactional
    public Result<Boolean> saveWaEmpGroup(AttEmpGroupDto dto) {
        UserInfo userInfo = getUserInfo();
        dto.setBelongOrgId(userInfo.getTenantId());
        AttEmpGroupDo attEmpGroup = ObjectConverter.convert(dto, AttEmpGroupDo.class);
        long currentTime = System.currentTimeMillis() / 1000;
        attEmpGroup.setUpdateTime(currentTime);
        attEmpGroup.setUpdateUser(userInfo.getUserId());
        attEmpGroup.setEmpId(dto.getEmpInfo().getEmpId());
        //新增
        if (dto.getEmpGroupId() == null) {
            attEmpGroup.setCreateTime(currentTime);
            attEmpGroup.setCreateUser(userInfo.getUserId());
            AttEmpGroupDto groupDto = new AttEmpGroupDto();
            groupDto.setBelongOrgId(userInfo.getTenantId());
            groupDto.setEmpInfo(dto.getEmpInfo());
            List<AttEmpGroupDo> list = attEmpGroupDo.getWaEmpGroupListByPeriod(groupDto);
            Optional<AttEmpGroupDo> optional = list.stream().max(Comparator.comparing(AttEmpGroupDo::getStartTime));
            //A<C<=B,D>=B 更新B->C-1,插入C到D
            if (optional.isPresent()) {
                AttEmpGroupDo empGroupDo = optional.get();
                if ((attEmpGroup.getStartTime() > empGroupDo.getStartTime()
                        && attEmpGroup.getStartTime() <= empGroupDo.getEndTime()
                        && attEmpGroup.getEndTime() >= empGroupDo.getEndTime())) {
                    empGroupDo.setEndTime(DateUtil.addDate(dto.getStartTime() * 1000, -1));
                    attEmpGroupDo.updateWaEmpGroup(empGroupDo);
                    attEmpGroupDo.saveWaEmpGroup(attEmpGroup);
                } else if (attEmpGroup.getStartTime() > empGroupDo.getEndTime() && attEmpGroup.getEndTime() > empGroupDo.getEndTime()) {
                    attEmpGroupDo.saveWaEmpGroup(attEmpGroup);
                } else {
                    return ResponseWrap.wrapResult(AttendanceCodes.WA_TIME_CONFLICT, Boolean.FALSE);
                }
            } else {
                attEmpGroupDo.saveWaEmpGroup(attEmpGroup);
            }
        } else {
            //修改
            List<AttEmpGroupDo> list = attEmpGroupDo.getWaEmpGroupListByPeriod(dto);
            if (CollectionUtils.isNotEmpty(list)) {
                log.error("员工该时间内已存在考勤方案，请修改原方案时间！");
                return ResponseWrap.wrapResult(AttendanceCodes.WA_TIME_OVERLAP, Boolean.FALSE);
            }
            attEmpGroupDo.updateWaEmpGroup(attEmpGroup);
        }
        return Result.ok(Boolean.TRUE);
    }

    @Transactional
    @Override
    public void synchronizeEmpGroup(String belongOrgId, Long userId, Long corpId) {
        WaGroupExample example = new WaGroupExample();
        example.createCriteria().andBelongOrgidEqualTo(belongOrgId);
        // 查询所有的考勤方案
        log.info("查询所有的考勤方案开始");
        List<WaGroup> waGroups = waGroupMapper.selectByExample(example);
        log.info("查询所有的考勤方案结束，总条数：[{}]", waGroups.size());
        if (CollectionUtils.isNotEmpty(waGroups)) {
            Map<String, String> conditionMap = waGroups.stream().filter(g -> StringUtil.isNotBlank(g.getGroupExpCondition())).collect(Collectors.toMap(g -> g.getWaGroupId().toString(), WaGroup::getGroupExpCondition));
            List<String> businessKeys = waGroups.stream().map(w -> String.valueOf(w.getWaGroupId())).collect(Collectors.toList());
            // 根据考勤方案查规则
            log.info("根据考勤方案查询适用范围规则开始");
            List<EmployeeGroupDto> empGroups = employeeGroupService.getEmployeeGroups(businessKeys, WA_PLAN_GROUP_TYPE, belongOrgId);
            log.info("根据考勤方案查询适用范围规则结束，总条数：[{}]", empGroups.size());
            if (CollectionUtils.isNotEmpty(empGroups)) {
                // 查询已分配员工考勤方案
                log.info("查询已分配员工考勤方案开始");
                AttEmpGroupDto dto = new AttEmpGroupDto();
                dto.setBelongOrgId(belongOrgId);
                List<AttEmpGroupDo> assignedEmpGroups = attEmpGroupDo.getWaEmpGroupListByPeriod(dto);
                log.info("查询已分配员工考勤方案结束，总条数：[{}]", assignedEmpGroups.size());
                List<Long> assignedEmpIds = assignedEmpGroups.stream().map(AttEmpGroupDo::getEmpId).distinct().collect(Collectors.toList());
                log.info("更新已分配员工考勤方案开始");
                updateAssignedEmpGroups(corpId, belongOrgId, userId, assignedEmpIds, assignedEmpGroups, empGroups, conditionMap);
                log.info("更新已分配员工考勤方案结束");
                List<Long> unAssignedEmpIds = Lists.newArrayList();
                List<WaEmpGroup> unAssignedEmpGroups = Lists.newArrayList();
                log.info("筛选未分配考勤方案的员工开始");
                for (EmployeeGroupDto empGroup : empGroups) {
                    if (StringUtils.isNotEmpty(empGroup.getGroupExp())) {
                        // 根据规则筛选员工
                        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, empGroup.getGroupExp(), conditionMap.get(empGroup.getBusinessKey()));
                        if (CollectionUtils.isEmpty(groupEmpIds)) {
                            continue;
                        }
                        groupEmpIds.removeAll(assignedEmpIds);
                        unAssignedEmpIds.addAll(groupEmpIds);
                        for (Long groupEmpId : groupEmpIds) {
                            unAssignedEmpGroups.add(getWaEmpGroup(groupEmpId, userId, Integer.valueOf(empGroup.getBusinessKey()), null));
                        }
                    }
                }
                log.info("筛选未分配考勤方案的员工结束");
                // 将未分配考勤方案的员工自动分配
                log.info("未分配考勤方案的员工自动分配开始");
                if (CollectionUtils.isNotEmpty(unAssignedEmpGroups)) {
                    //人员去重
                    List<Long> empIdList = unAssignedEmpIds.stream().distinct().collect(Collectors.toList());
                    unAssignedEmpGroups = unAssignedEmpGroups.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaEmpGroup::getEmpid))), ArrayList::new));
                    List<SysEmpInfo> unAssignedEmployees = Lists.newArrayList();
                    List<List<Long>> lists = ListTool.split(empIdList, 500);
                    for (List<Long> empInfos : lists) {
                        if (CollectionUtils.isEmpty(empInfos)) {
                            continue;
                        }
                        unAssignedEmployees.addAll(sysEmpInfoDo.getEmpInfoByIds(belongOrgId, empInfos));
                    }
                    if (CollectionUtils.isNotEmpty(unAssignedEmployees)) {
                        // 员工信息Map
                        Map<Long, SysEmpInfo> unAssignedEmployeeMap = unAssignedEmployees.stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, Function.identity(), (k1, k2) -> k2));
                        Iterator<WaEmpGroup> iterator = unAssignedEmpGroups.iterator();
                        while (iterator.hasNext()) {
                            WaEmpGroup empGroup = iterator.next();
                            SysEmpInfo unAssignedEmployee = unAssignedEmployeeMap.get(empGroup.getEmpid());
                            if (unAssignedEmployee != null) {
                                //生效时间默认1970-1-1
                                empGroup.setStartTime(0L);//unAssignedEmployee.getHireDate()
                            } else {
                                iterator.remove();
                            }
                        }
                    }
                    //保存员工分组
                    importService.fastInsertList(WaEmpGroup.class, "empGroupId", unAssignedEmpGroups);
                }
            }
        }
    }

    public void updateAssignedEmpGroups(Long corpId, String tenantId, Long userId, List<Long> assignedEmpIds,
                                        List<AttEmpGroupDo> assignedEmpGroups, List<EmployeeGroupDto> empGroups,
                                        Map<String, String> conditionMap) {
        if (CollectionUtils.isEmpty(assignedEmpIds) || CollectionUtils.isEmpty(assignedEmpGroups) || CollectionUtils.isEmpty(empGroups)) {
            return;
        }
        List<SysEmpInfo> assignedEmployees = Lists.newArrayList();
        List<List<Long>> lists = ListTool.split(assignedEmpIds, 500);
        for (List<Long> list : lists) {
            if (CollectionUtils.isEmpty(list)) {
                continue;
            }
            assignedEmployees.addAll(sysEmpInfoDo.getEmpInfoByIds(tenantId, list));
        }
        if (CollectionUtils.isEmpty(assignedEmployees)) {
            return;
        }
        Map<Long, SysEmpInfo> assignedEmployeeMap = assignedEmployees.stream().collect(Collectors.toMap(SysEmpInfo::getEmpid, Function.identity(), (k1, k2) -> k2));
        List<AttEmpGroupDo> updateEmpGroups = Lists.newArrayList();
        List<Integer> deleteEmpGroups = Lists.newArrayList();
        Map<Long, List<AttEmpGroupDo>> empGroupMap = assignedEmpGroups.stream().collect(Collectors.groupingBy(AttEmpGroupDo::getEmpId));
        List<Long> unAssignedEmpIds = Lists.newArrayList();
        Map<Integer, EmployeeGroupDto> empGroupsMap = empGroups.stream().collect(Collectors.toMap(g -> Integer.valueOf(g.getBusinessKey()), Function.identity(), (k1, k2) -> k2));
        List<WaEmpGroup> addEmpGroups = Lists.newArrayList();
        for (Long empId : assignedEmpIds) {
            if (!assignedEmployeeMap.containsKey(empId)) {
                continue;
            }
            SysEmpInfo empInfo = assignedEmployeeMap.get(empId);
            Long terminationDate = empInfo.getTerminationDate();
            if (null == terminationDate) {
                continue;
            }
            List<AttEmpGroupDo> empGroupList = empGroupMap.get(empId);
            Long hireDate = empInfo.getHireDate();
            if (null != hireDate && empInfo.getHireDate() >= terminationDate) {
                //再入职
                if (assignedEmpGroups.stream().anyMatch(g -> g.getEmpId().equals(empId) && g.getStartTime() >= hireDate)) {
                    continue;
                }
                Optional<AttEmpGroupDo> empGroupOpt = empGroupList.stream().filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime()).max(Comparator.comparing(AttEmpGroupDo::getStartTime));
                if (empGroupOpt.isPresent()) {
                    AttEmpGroupDo empGroup = empGroupOpt.get();
                    if (hireDate > empGroup.getStartTime() && hireDate < empGroup.getEndTime()) {
                        Integer groupId = empGroup.getWaGroupId();
                        if (checkEmpGroup(empId, corpId, groupId, empGroupsMap, conditionMap)) {
                            addEmpGroups.add(getWaEmpGroup(empId, userId, groupId, hireDate));
                        } else {
                            unAssignedEmpIds.add(empId);
                        }
                    } else {
                        unAssignedEmpIds.add(empId);
                    }
                    empGroup.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                    empGroup.setUpdateTime(DateUtil.getCurrentTime(true));
                    updateEmpGroups.add(empGroup);
                } else {
                    unAssignedEmpIds.add(empId);
                }
            } else if (empInfo.getStats() == 1) {
                //已离职
                deleteEmpGroups.addAll(empGroupList.stream().filter(g -> g.getStartTime() > terminationDate).map(AttEmpGroupDo::getEmpGroupId).collect(Collectors.toList()));
                Optional<AttEmpGroupDo> empGroupOpt = empGroupList.stream().filter(g -> terminationDate > g.getStartTime() && terminationDate + 86399 < g.getEndTime()).max(Comparator.comparing(AttEmpGroupDo::getStartTime));
                if (empGroupOpt.isPresent()) {
                    AttEmpGroupDo empGroup = empGroupOpt.get();
                    empGroup.setEndTime(DateUtil.addDate(terminationDate * 1000, 1) - 1);
                    empGroup.setUpdateTime(DateUtil.getCurrentTime(true));
                    updateEmpGroups.add(empGroup);
                }
            }
        }
        //重新分配
        if (CollectionUtils.isNotEmpty(unAssignedEmpIds)) {
            List<WaEmpGroup> unAssignedEmpGroups = Lists.newArrayList();
            for (EmployeeGroupDto empGroup : empGroups) {
                if (StringUtils.isEmpty(empGroup.getGroupExp())) {
                    continue;
                }
                // 根据规则筛选员工
                List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, empGroup.getGroupExp(), conditionMap.get(empGroup.getBusinessKey()));
                if (CollectionUtils.isEmpty(groupEmpIds)) {
                    continue;
                }
                groupEmpIds.retainAll(unAssignedEmpIds);
                for (Long groupEmpId : groupEmpIds) {
                    SysEmpInfo empInfo = assignedEmployeeMap.get(groupEmpId);
                    unAssignedEmpGroups.add(getWaEmpGroup(groupEmpId, userId, Integer.valueOf(empGroup.getBusinessKey()), empInfo.getHireDate()));
                }
            }
            if (CollectionUtils.isNotEmpty(unAssignedEmpGroups)) {
                addEmpGroups.addAll(unAssignedEmpGroups);
            }
        }
        if (CollectionUtils.isNotEmpty(addEmpGroups)) {
            addEmpGroups = addEmpGroups.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(WaEmpGroup::getEmpid))), ArrayList::new));
            //保存员工分组
            importService.fastInsertList(WaEmpGroup.class, "empGroupId", addEmpGroups);
        }
        //离职，在入职，更新
        if (CollectionUtils.isNotEmpty(updateEmpGroups)) {
            //保存员工分组
            WaEmpGroup empGroup = new WaEmpGroup();
            for (AttEmpGroupDo updateEmpGroup : updateEmpGroups) {
                empGroup.setEmpGroupId(updateEmpGroup.getEmpGroupId());
                empGroup.setEndTime(updateEmpGroup.getEndTime());
                empGroup.setUpdtime(updateEmpGroup.getUpdateTime());
                empGroup.setUpduser(userId == null ? 0 : userId);
                waEmpGroupMapper.updateByPrimaryKeySelective(empGroup);
            }
        }
        //离职，删除
        if (CollectionUtils.isNotEmpty(deleteEmpGroups)) {
            List<List<Integer>> deleteLists = ListTool.split(deleteEmpGroups, 50);
            for (List<Integer> list : deleteLists) {
                WaEmpGroupExample empGroupExample = new WaEmpGroupExample();
                WaEmpGroupExample.Criteria criteria = empGroupExample.createCriteria();
                criteria.andEmpGroupIdIn(list);
                waEmpGroupMapper.deleteByExample(empGroupExample);
            }
        }
    }

    public boolean checkEmpGroup(Long empId, Long corpId, Integer groupId, Map<Integer, EmployeeGroupDto> empGroupsMap, Map<String, String> conditionMap) {
        if (!empGroupsMap.containsKey(groupId)) {
            return false;
        }
        EmployeeGroupDto assignedEmpGroup = empGroupsMap.get(groupId);
        if (StringUtils.isEmpty(assignedEmpGroup.getGroupExp())) {
            return false;
        }
        // 根据规则筛选员工
        List<Long> groupEmpIds = sysEmpInfoDo.getEmpIdsByGroupExp(corpId, assignedEmpGroup.getGroupExp(), conditionMap.get(assignedEmpGroup.getBusinessKey()));
        if (CollectionUtils.isEmpty(groupEmpIds)) {
            return false;
        }
        return groupEmpIds.contains(empId);
    }

    @Override
    public void synchronizeEmpGroup() {
        SysCorpOrgExample example = new SysCorpOrgExample();
        example.createCriteria().andOrgtype2EqualTo(1).andStatusEqualTo(1);
        List<SysCorpOrg> items = sysCorpOrgMapper.selectByExample(example);
        for (SysCorpOrg item : items) {
            log.info("自动同步员工考勤方案，参数:[belongOrgId:{}，corpId:{}]", item.getBelongOrgId(), item.getCorpid());
            try {
                synchronizeEmpGroup(item.getOrgid().toString(), null, item.getCorpid());
            } catch (Exception e) {
                log.error("自动同步员工考勤方案:{}", e.getMessage(), e);
            }
            log.info("自动同步员工考勤方案");
        }
    }

    @Override
    public void updateWaGroupLeaveInfo(Integer waGroupId) {
        UserInfo userInfo = this.getUserInfo();
        WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waGroupId);
        if (waGroup == null || waGroup.getLeaveTypeIds() == null) {
            return;
        }

        WaGroup waGroupUpd = new WaGroup();
        waGroupUpd.setWaGroupId(waGroupId);
        waGroupUpd.setUpdtime(DateUtil.getCurrentTime(true));
        waGroupUpd.setUpduser(userInfo.getUserId());

        List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeByGroupId(waGroup.getBelongOrgid(), waGroup.getWaGroupId());
        if (CollectionUtils.isNotEmpty(leaveTypes)) {
            List<Integer> leaveTypeIds = leaveTypes.stream().map(WaLeaveTypeDo::getLeaveTypeId).collect(Collectors.toList());
            List<String> leaveNames = leaveTypes.stream().map(WaLeaveTypeDo::getLeaveName).collect(Collectors.toList());

            waGroupUpd.setLeaveTypeNames(String.join(",", leaveNames));
            waGroupUpd.setLeaveTypeIds(leaveTypeIds.toArray(new Integer[leaveTypeIds.size()]));
        } else {
            waGroupUpd.setLeaveTypeIds(new Integer[0]);
            waGroupUpd.setLeaveTypeNames("");
        }

        waGroupMapper.updateByPrimaryKeySelective(waGroupUpd);
    }

    private WaEmpGroup getWaEmpGroup(Long empId, Long userId, Integer waGroupId, Long hireDate) {
        long currentTime = System.currentTimeMillis() / 1000;
        WaEmpGroup group = new WaEmpGroup();
        group.setWaGroupId(waGroupId);
        group.setEmpid(empId);
        group.setStartTime(hireDate == null ? 1L : hireDate);
        // 9999-12-31
        group.setEndTime(253402185600L);
        group.setCrtuser(userId);
        group.setCrttime(currentTime);
        group.setUpduser(userId);
        group.setUpdtime(currentTime);
        return group;
    }

    /**
     * 获取当前有效期内的员工考勤方案
     *
     * @param belongOrgId
     * @param empId
     * @param currentTime
     */
    @Override
    public GroupDetailDto getEmpGroup(String belongOrgId, Long empId, Long currentTime) {
        List<AttEmpGroupDo> list = attEmpGroupDo.getEmpGroup(belongOrgId, empId, currentTime);
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        Optional<AttEmpGroupDo> optional = list.stream().min(Comparator.comparing(AttEmpGroupDo::getStartTime));
        if (optional.isPresent()) {
            AttEmpGroupDo empGroup = optional.get();
            WaGroup group = waGroupMapper.selectByPrimaryKey(empGroup.getWaGroupId());
            return ObjectConverter.convert(group, GroupDetailDto.class);
        }
        return null;
    }

    @Override
    public List<KeyValue> getConTractCompany(String tenantId) {
        List<Map> contractCompany = contractCompanyMapper.getContractCompany(tenantId);
        List<KeyValue> keyValues = JSON.parseArray(JSON.toJSONString(contractCompany)).toJavaList(KeyValue.class);
        return keyValues;
    }

    @Override
    public List<EmpGroupIdDto> getEmpGroupIdByEmpIds(String belongOrgId, List<Long> empIds, Long currentTime) {
        List<EmpGroupIdDto> result = new ArrayList<>();
        List<AttEmpGroupDo> list = attEmpGroupDo.getEmpGroupByEmpIds(belongOrgId, empIds, currentTime);
        if (CollectionUtils.isNotEmpty(list)) {
            for (AttEmpGroupDo item : list) {
                if (item != null) {
                    EmpGroupIdDto empGroupIdVo = new EmpGroupIdDto();
                    empGroupIdVo.setEmpId(item.getEmpId());
                    empGroupIdVo.setEmpGroupId(item.getWaGroupId());
                    result.add(empGroupIdVo);
                }
            }
        }
        return result;
    }

    @Override
    public List<AttEmpGroupDo> getEmpGroupIdByEmpIds(String tenantId, List<Long> empIds, Long startDate, Long endDate) {
        return attEmpGroupDo.getEmpGroupByEmpIds(tenantId, empIds, startDate, endDate);
    }
}