package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IPerformanceService;
import com.caidaocloud.attendance.service.schedule.application.service.shiftGroup.IWaEmpShiftGroupService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
@RabbitListener(
        bindings = @QueueBinding(
                value = @Queue(value = "attendance.performance.queue", durable = "true"),
                exchange = @Exchange(value = "attendance.performance.fac.direct.exchange"),
                key = {"routingKey.performance"}
        )
)
public class PerformanceEmpSyncSubscribe {
    @Resource
    private IPerformanceService performanceService;

    @RabbitHandler
    public void process(String message) {
        log.info("startPerformanceEmpSyncSubscribe={}", message);
        try {
            Map<String, String> tenantMap = JSON.parseObject(message, Map.class);
            performanceService.clockInitPeriodEmp(ConvertHelper.stringConvert(tenantMap.get("tenantId")), "0");
        } catch (Exception ex) {
            log.error("PerformanceEmpSyncSubscribeErr,{}", ex.getMessage(), ex);
        }
        log.info("endPerformanceEmpSyncSubscribe={}", message);
    }
}
