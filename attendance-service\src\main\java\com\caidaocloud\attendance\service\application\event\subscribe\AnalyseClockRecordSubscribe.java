package com.caidaocloud.attendance.service.application.event.subscribe;

import com.alibaba.fastjson.JSON;
import com.caidao1.commons.utils.DateUtil;
import com.caidaocloud.attendance.core.wa.dto.clock.ClockAnalyseDto;
import com.caidaocloud.attendance.service.application.service.IClockSignService;
import com.caidaocloud.attendance.service.interfaces.dto.BatchClockAnalyseDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.rabbit.annotation.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

@Slf4j
@Component
public class AnalyseClockRecordSubscribe {

    @Autowired
    private IClockSignService clockSignService;

    @RabbitHandler
    @RabbitListener(
            bindings = @QueueBinding(
                    value = @Queue(value = "attendance.clock.analyse.pc.queue", durable = "true"),
                    exchange = @Exchange(value = "attendance.clock.analyse.pc.fac.direct.exchange"),
                    key = {"routingKey.clock.analyse.pc"}
            )
    )
    public void process(String message) {
        log.info("PC:AnalyseClockRecordSubscribe ClockMessage={}", message);
        try {
            ClockAnalyseDto dto = JSON.parseObject(message, ClockAnalyseDto.class);
            List<Long> dateRangeList;
            if (StringUtils.isNotBlank(dto.getDateRange())
                    && CollectionUtils.isNotEmpty(dateRangeList = dto.doGetDateRangeList())
                    && dateRangeList.size() > 1) {
                BatchClockAnalyseDto batchClockAnalyseDto = new BatchClockAnalyseDto();
                batchClockAnalyseDto.setBelongOrgId(dto.getBelongOrgId());
                batchClockAnalyseDto.setStartDate(dateRangeList.get(0));
                batchClockAnalyseDto.setEndDate(dateRangeList.get(1));
                batchClockAnalyseDto.setEmpIds(dto.getEmpIds());
                batchClockAnalyseDto.setType(dto.getType());
                clockSignService.analyseByDateRange(batchClockAnalyseDto);
            } else {
                clockSignService.analyseRegisterRecord(dto.getBelongOrgId(), dto.getEmpIds(), dto.getDate(), null);
            }
            log.info("PC:AnalyseClockRecordSubscribe ClockMessage over time {}", DateUtil.getCurrentTime(true));
        } catch (Exception ex) {
            log.error("消息监听出现异常，异常原因:{}", ex.getMessage(), ex);
        }
    }

}
