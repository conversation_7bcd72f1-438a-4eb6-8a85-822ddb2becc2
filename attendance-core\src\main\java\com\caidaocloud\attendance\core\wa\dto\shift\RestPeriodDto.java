package com.caidaocloud.attendance.core.wa.dto.shift;

import com.caidaocloud.attendance.core.wa.enums.ShiftTimeBelongTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel("班次设置-上班时间范围内的多段休息时间段DTO")
public class RestPeriodDto {
    @ApiModelProperty("开始时间(单位分钟),eg:750")
    private Integer noonRestStart;
    @ApiModelProperty("结束时间(单位分钟),eg:780")
    private Integer noonRestEnd;
    @ApiModelProperty("开始时间归属标记: 1 当日、2 次日")
    private Integer noonRestStartBelong;
    @ApiModelProperty("结束时间归属标记: 1 当日、2 次日")
    private Integer noonRestEndBelong;

    public Integer doGetRealNoonRestStart() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.noonRestStartBelong)) {
            return noonRestStart + 1440;
        }
        return noonRestStart;
    }

    public Integer doGetRealNoonRestEnd() {
        if (ShiftTimeBelongTypeEnum.NEXT_DAY.getIndex().equals(this.noonRestEndBelong)) {
            return noonRestEnd + 1440;
        }
        return noonRestEnd;
    }

    public Integer doGetRestTotalTime() {
        if (this.noonRestStart == null || this.noonRestEnd == null) {
            return 0;
        }
        Integer wStart = this.doGetRealNoonRestStart();
        Integer wEnd = this.doGetRealNoonRestEnd();
        return wEnd - wStart;
    }
}
