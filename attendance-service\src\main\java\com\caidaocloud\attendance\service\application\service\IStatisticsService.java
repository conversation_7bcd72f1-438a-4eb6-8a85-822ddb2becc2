package com.caidaocloud.attendance.service.application.service;

import com.caidao1.report.dto.PageBean;
import com.caidaocloud.attendance.service.application.dto.BaseHeaderDto;
import com.caidaocloud.attendance.service.application.dto.WaStatisticsDto;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.notify.AttendanceSummaryNotify;
import com.caidaocloud.attendance.service.interfaces.vo.StatisticsSummaryVo;
import com.caidaocloud.attendance.service.interfaces.vo.StatisticsWorkInfoVo;
import com.caidaocloud.attendance.service.interfaces.vo.SummaryRateVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import org.apache.commons.lang3.tuple.ImmutablePair;

import java.util.List;
import java.util.Locale;

/**
 * 考勤分析&统计
 *
 * <AUTHOR>
 * @Date 2021/4/16
 */
public interface IStatisticsService {

    StatisticsSummaryVo getSummaryCount(SummaryStaticPageDto dto, String scope);

    PageResult<StatisticsWorkInfoVo> getSummaryWorkList(SummaryDetailDto dto, String scope);

    List<SummaryRateVo> getSummaryWorkRate(SummaryDetailDto dto, String scope);

    List<SummaryRateVo> getSummaryTimeRate(SummaryDetailDto dto, Integer rateType, String scope);

    List<SummaryRateVo> getSummaryLtRate(SummaryDetailDto dto, String scope);

    List getDayAnalyseList(DayAnalysePageDto dto, PageBean pageBean);

    List getDayAnalyseList(DayAnalysePageDto dto, PageBean pageBean, UserInfo userInfo);

    /**
     * 查询考勤月度数据
     *
     * @param dto
     * @param pageBean
     * @return
     */
    List searchMonthRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo);

    List searchRegisterStatisticsAdvance(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo);

    List searchRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo);

    List searchRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, Boolean summary, UserInfo userInfo);

    List<String> getDayHeaders();

    List<String> getSummaryDayHeaders(int statisticsType);

    List<String> getSummaryDynamicHeaders(int statisticsType);

    List<BaseHeaderDto> getDayHeadersForExport(String sobIds, UserInfo userInfo);

    /**
     * 动态列 每日分析表头
     * 
     * @param sobIds
     * @param userInfo
     * @return
     */
    List<BaseHeaderDto> getDayHeadersByDynamicForExport(String sobIds, UserInfo userInfo);

    /**
     * 月度汇总表头
     *
     * @param waGroupId
     * @return
     */
    List<String> getMonthHeaders(Integer waGroupId, ImmutablePair<Long, Long> summaryPeriod);

    List<BaseHeaderDto> getMonthHeadersForExport(Integer waGroupId, UserInfo userInfo,
            ImmutablePair<Long, Long> summaryPeriod);

    /**
     * 动态列 月度汇总表头
     */
    List<BaseHeaderDto> getMonthHeadersByDynamicForExport(Integer waGroupId, UserInfo userInfo,
            ImmutablePair<Long, Long> summaryPeriod);

    void notification(AttendanceSummaryNotify dto, UserInfo userInfo, Locale locale);

    /**
     * 考勤分析
     *
     * @param belongid
     * @param startDate
     * @param endDate
     * @param empids
     * @param waSobId
     * @param dataScope
     * @param userId
     * @param isJob
     * @return
     * @throws Exception
     */
    WaStatisticsDto asyncRegister(String belongid, Long startDate, Long endDate, Long[] empids, Integer waSobId,
            String dataScope, Long userId, boolean isJob) throws Exception;

    List<AnalyzeCalendarDto> getAnalyzeCalendarList(Long empId, Integer searchMonth, String dataScope);

    PageResult<DayAnalyseAbnormalDto> getDayAnalyseAbnormalList(DayAnalyseAbnormalPageDto dto, UserInfo userInfo);

    List<KeyValue> getMonthHeadersKeyValue(Integer waGroupId);

    List<KeyValue> getDayAnalyseHeaderKeyValue();

    DynamicPageDto getMouthAnalyseListForDynamic(MonthAnalysePageDto dto, PageBean pageBean);

    DynamicPageDto getMouthAnalyseListForDynamic(MonthAnalysePageDto requestDto, PageBean pageBean, UserInfo userInfo);

    DynamicPageDto getDayAnalyseListForDynamic(DayAnalysePageDto dto, PageBean pageBean);

    DynamicPageDto getDayAnalyseListForDynamic(DayAnalysePageDto requestDto, PageBean pageBean, UserInfo userInfo);

    List<MetadataVo> fetchStatisticsAvailableColumn(String type);

    UserDynamicConfig resetAvailableColumn(String type);

    DynamicPageDto getRegisterStatisticsAdvancedForDynamic(MonthAnalysePageDto dto, PageBean pageBean,
            UserInfo userInfo);

    /**
     * 动态列 考勤分析表头
     */
    List<BaseHeaderDto> getMonthAdvanceHeadersByDynamicForExport(Integer waGroupId, UserInfo userInfo,
            ImmutablePair<Long, Long> summaryPeriod);

    /**
     * 动态列 月度 对外提供接口
     */
    List<KeyValue> searchMonthHeaderListForDynamic(Integer waGroupId, UserInfo userInfo);

    /**
     * 动态列 每日 对外提供接口
     */
    List<KeyValue> getDayAnalyseHeaderListForDynamic(UserInfo userInfo);

    /**
     * 支持多个考勤分组ID的动态列接口
     */
    DynamicPageDto getRegisterStatisticsAdvancedForDynamicMultiGroup(MonthAnalysePageDtoV2 dto, PageBean pageBean,
            UserInfo userInfo);

    /**
     * 支持多个考勤分组ID的月度表头接口
     */
    List<KeyValue> searchMonthHeaderListForDynamicMultiGroup(List<Integer> waGroupIds, UserInfo userInfo);

    /**
     * 支持多个考勤分组ID的月度表头（用于导出）
     */
    List<BaseHeaderDto> getMonthHeadersByDynamicForExportMultiGroup(List<Integer> waGroupIds, UserInfo user,
            ImmutablePair<Long, Long> summaryPeriod);
}
