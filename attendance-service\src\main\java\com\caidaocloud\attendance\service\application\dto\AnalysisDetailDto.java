package com.caidaocloud.attendance.service.application.dto;

import com.alibaba.fastjson.JSONObject;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class AnalysisDetailDto {
    private Integer parseGroupId;
    private String parseGroupName;
    private Boolean otParse;
    private Boolean lvParse;
    private Integer registerMiss;
    private Boolean isAnalyzeLateEarly;
    private Boolean otSumParse;
    /**
     * 迟到豁免周期： 1 月
     */
    private String lateCycle;

    /**
     * 迟到时长
     */
    private BigDecimal lateCount;

    /**
     * 迟到时长单位：1 小时，2 分钟
     */
    private Integer lateUnit;

    /**
     * 允许迟到次数
     */
    private Integer lateAllowNumber;

    /**
     * 迟到豁免类型：1 按照次数，2 按照分钟
     */
    private Integer lateAllowUnit;

    /**
     * 早退豁免周期： 1 月
     */
    private String earlyCycle;

    /**
     * 早退时长
     */
    private BigDecimal earlyCount;

    /**
     * 早退时长单位：1 小时，2 分钟
     */
    private Integer earlyUnit;

    /**
     * 允许早退次数
     */
    private Integer earlyAllowNumber;

    /**
     * 早退豁免类型：1 按照次数，2 按照分钟
     */
    private Integer earlyAllowUnit;

    private String lateAllowRuleTxt;

    private String earlyAllowRuleTxt;

    /**
     * 异常类型：1早退加迟到，2迟到或早退
     */
    private Integer abnormalType;
    private List<AbsentConditionDto> absentConditionJsonb;

    /**
     * 开启自定义分析规则开关
     */
    private Boolean openCustomPaseRule;

    /**
     * 自定义迟到分析规则
     */
    private WaCustomParseRuleDto customLatePaseRule;
    /**
     * 自定义早退分析规则
     */
    private WaCustomParseRuleDto customEarlyPaseRule;
    /**
     * 自定义旷工分析规则
     */
    private WaCustomParseRuleDto customAbsentPaseRule;
    private List<OtPaseDto> otPaseJsonb;
    private Integer clockType;
    private JSONObject clockRuleDto;

    /**
     * 外勤分析规则：1 出差单 2 外勤打卡 3 出差单联动外勤打卡
     */
    private Integer outParseRule;

    /**
     * 弹性分析开关 1:关闭 2:开启
     */
    private Integer flexibleWorkSwitch;

    /**
     * 弹性分析开关 1:按弹性区间分析 2:按班次分析
     */
    private Integer flexibleWorkType;

    /**
     * 允许打卡日期类型：1(工作日),2(休息日),3(法定假日),4(特殊休日),多个类型逗号分隔
     */
    private String allowedDateType;

    /**
     * 外勤联动班次打卡
     */
    private Boolean fieldClockLinkShift;

    private Integer minLateTime;
    private Short minLateTimeUnit;
    private Integer minEarlyTime;
    private Short minEarlyTimeUnit;
    private Boolean leaveExemptionSwitch;

    /**
     * 统计转换类型：1、按阶梯统计，2、按比例统计，默认值1
     */
    private Integer statisticType;

    /**
     * 迟到早退转旷工按比例转换时长，默认1:1转换
     */
    private Integer convertTime;

    /**
     * 迟到早退转旷工按比例转换比例，默认1:1转换
     */
    private Integer convertScale;

    /**
     * 迟到早退转旷工上限，超过上限转旷工，默认1:1转换
     */
    private Integer convertTimeLimit;

    /**
     * 迟到早退按比例转换迟到早退，默认1:1转换
     */
    private Integer expConvertScale;

    /**
     * 迟到早退按比例转换旷工比例(迟到早退值)，默认1:1转换
     */
    private Integer convertKgScale;
}
