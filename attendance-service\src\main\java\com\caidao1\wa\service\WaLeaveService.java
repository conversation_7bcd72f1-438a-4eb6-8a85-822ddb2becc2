package com.caidao1.wa.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.enums.LeaveStatusEnum;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.service.util.SessionBeanUtil;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.wa.dto.EmpQuoDto;
import com.caidao1.wa.dto.UsableCompensatoryQuotaDto;
import com.caidao1.wa.mybatis.mapper.EmpCompensatoryQuotaMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeDefMapper;
import com.caidao1.wa.mybatis.mapper.WaLeaveTypeMapper;
import com.caidao1.wa.mybatis.mapper.WaMapper;
import com.caidao1.wa.mybatis.model.*;
import com.caidao1.xss.test.cache.RedisService;
import com.caidaocloud.attendance.core.commons.UserContext;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.service.WaLeaveCoreService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.TimeControlPeriod;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.service.impl.WaCustomLogicConfigService;
import com.caidaocloud.attendance.service.domain.entity.LeaveQuotaConfigDo;
import com.caidaocloud.attendance.service.domain.entity.WaCustomLogicConfigDo;
import com.caidaocloud.attendance.service.domain.entity.WaLeaveTypeDo;
import com.caidaocloud.attendance.service.infrastructure.repository.mapper.EmpLeaveMapper;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.em.HalfDayTypeEnum;
import com.caidaocloud.i18n.MessageHandler;
import com.caidaocloud.util.ObjectConverter;
import com.caidaocloud.util.WebUtil;
import com.caidaocloud.web.ResponseWrap;
import com.caidaocloud.web.Result;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Jedis;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WaLeaveService {
    @Autowired
    private EmpCompensatoryQuotaMapper waEmpCompensatoryQuotaMapper;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private EmpLeaveMapper empLeaveMapper;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaLeaveCoreService waLeaveCoreService;
    @Autowired
    private WaLeaveTypeDefMapper waLeaveTypeDefMapper;
    @Autowired
    private LeaveQuotaConfigDo leaveQuotaConfigDo;
    @Autowired
    private WaCustomLogicConfigDo waCustomLogicConfigDo;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WaCustomLogicConfigService waCustomLogicConfigService;

    public Result preCheck(LeaveApplySaveDto saveDto) {
        Result checkResult = LeaveApplySaveDto.checkLeaveApplyParams(saveDto, false, false);
        if (!checkResult.isSuccess()) {
            return checkResult;
        }
        if (saveDto.getDailyDuration() == null) {
            if (saveDto.getShowMin() == 1 && saveDto.getStartTime().equals(saveDto.getEndTime())
                    && saveDto.getStime().equals(saveDto.getEtime())) {
                return ResponseWrap.wrapResult(AttendanceCodes.START_TIME_EQUAL_END, null);
            }
        }
        return Result.ok(Boolean.TRUE);
    }

    /**
     * 员工可请检查
     *
     * @param waLeaveType
     * @return
     */
    public boolean checkLeaveTypeIfEmpShow(WaLeaveType waLeaveType) {
        if (waLeaveType.getIsEmpShow() != null && !waLeaveType.getIsEmpShow()) {
            //员工不可申请
            return true;
        }
        return false;
    }

    /**
     * 休假申请时根据试用期、入职日期判断是否可请
     *
     * @param waLeaveType
     * @param empInfo
     * @return true 不可请 false 可请
     * @throws Exception
     */
    public boolean checkEmpLeaveAvailable(WaLeaveType waLeaveType, SysEmpInfo empInfo) throws Exception {
        // 按照试用期判断
        if (waLeaveType.getLeaveLimitSwitch() == 1 && empInfo.getWorkType() == 1) {
            Boolean isUsed = waLeaveType.getIsUsedInProbation();
            Integer minProTime = waLeaveType.getProbationMinTime();
            if (isUsed != null && isUsed) {
                if (minProTime != null && minProTime > 0) {
                    Long hireDate = empInfo.getHireDate() == null ? DateUtil.getOnlyDate() : empInfo.getHireDate();
                    return DateUtilExt.getDifferenceDay(hireDate, DateUtil.getOnlyDate()) < minProTime;
                }
            } else {
                return true;
            }
        }
        // 按照入职日期判断
        if (waLeaveType.getLeaveLimitSwitch() == 2) {
            Long hireDate = empInfo.getHireDate() == null ? DateUtil.getOnlyDate() : empInfo.getHireDate();
            return DateUtilExt.getDifferenceDay(hireDate, DateUtil.getOnlyDate()) < waLeaveType.getLeaveLimitDays();
        }
        return false;
    }

    /**
     * 申请休假时校验并计算休假时长
     *
     * @param applySaveDto
     * @return
     * @throws Exception
     */
    public Map getLeaveTotalTime(LeaveApplySaveDto applySaveDto) throws Exception {
        Map<String, Object> mapRtn = new HashMap<>();
        if (applySaveDto.isOpenPreCheck()) {
            Result preCheckResult = preCheck(applySaveDto);
            if (!preCheckResult.isSuccess()) {
                mapRtn.put("status", -1);
                mapRtn.put("message", preCheckResult.getMsg());
                return mapRtn;
            }
        }
        UserInfo userInfo = UserContext.getAndCheckUser();
        WaLeaveType waLeaveType = waLeaveTypeMapper.selectByPrimaryKey(applySaveDto.getLeaveTypeId());
        if (waLeaveType == null) {
            mapRtn.put("status", -1);
            mapRtn.put("message", ResponseWrap.wrapResult(AttendanceCodes.LEAVE_TYPE_NOT_EXIST, null).getMsg());
            return mapRtn;
        }
        Result checkForMultiShiftResult = LeaveApplySaveDto.checkForMultiShift(applySaveDto, waLeaveType);
        if (!checkForMultiShiftResult.isSuccess()) {
            mapRtn.put("status", -1);
            mapRtn.put("message", checkForMultiShiftResult.getMsg());
            return mapRtn;
        }
        LeaveApplySaveDto.setLeavePeriod(applySaveDto, waLeaveType);
        applySaveDto.setLeaveTimeList(LeaveApplySaveDto.getTimeJsonList(applySaveDto));
        if (applySaveDto.getEmpid() == null) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L006834", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            return mapRtn;
        }
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(applySaveDto.getEmpid());
        if (empInfo == null) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L006835", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            return mapRtn;
        }
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (empInfo.getWorkType() == null) {
            empInfo.setWorkType(2);
        }
        // 考勤类型判断
        if (tmType == 0) {
            tmType = 1;
        } else if (tmType == 2) {//门店考勤人员
            Jedis jedis = RedisService.getResource();
            String appStoreEnable = jedis.get(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + userInfo.getTenantId() + "_APP_STORE_Enable");
            try {
                if (appStoreEnable == null || (!appStoreEnable.equals("1"))) {// 不支持门店考勤人员请假！
                    mapRtn.put("status", -1);
                    mapRtn.put("message", messageResource.getMessage("L005723", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                    return mapRtn;
                }
            } catch (RuntimeException e) {
                jedis.close();
                log.error(e.getMessage(), e);
            } finally {
                jedis.close();
            }
        }
        empInfo.setTmType(tmType);
        // 校验是否可请
        if (checkLeaveTypeIfEmpShow(waLeaveType)) {
            mapRtn.put("status", -1);
            mapRtn.put("message", MessageHandler.getMessage("caidao.exception.error_201938", WebUtil.getRequest()));
            return mapRtn;
        }
        // 试用期、入职日期判断
        if (checkEmpLeaveAvailable(waLeaveType, empInfo)) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L005728", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            return mapRtn;
        }
        Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(userInfo.getTenantId());
        BigDecimal totalTimeDuration = BigDecimal.ZERO;
        List<WaLeaveDaytime> allDaytimeList = new ArrayList<>();
        // 校验并计算时长
        for (Map<String, Object> leaveTimeMap : applySaveDto.getLeaveTimeList()) {
            String startTimeStr = (String) leaveTimeMap.get("starttime");
            String endTimeStr = (String) leaveTimeMap.get("endtime");
            long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
            long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

            // 查询排班
            Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(userInfo.getTenantId(),
                    applySaveDto.getEmpid(), tmType, startDate - 86400, endDate,
                    empInfo.getWorktimeType(), true);
            if (MapUtils.isEmpty(pbMap)) {
                mapRtn.put("status", -1);
                mapRtn.put("message", ResponseWrap.wrapResult(AttendanceCodes.EMP_NOT_SHIFT, null).getMsg());
                return mapRtn;
            }
            pbMap.forEach((date, worktimeDetail) -> {
                WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
                if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                    worktimeDetail.setDateType(shiftDef.getDateType());
                }
            });

            // 休假校验
            Result<Boolean> checkResult = mobileV16Service.checkLeave(empInfo, shiftMap, pbMap, leaveTimeMap,
                    waLeaveType, Boolean.FALSE, applySaveDto);
            if (!checkResult.isSuccess()) {
                mapRtn.put("status", -1);
                mapRtn.put("message", checkResult.getMsg());
                return mapRtn;
            }

            // 哺乳假申请校验
            String errBRJMsg = mobileV16Service.validateBRJ(waLeaveType, empInfo.getEmpid(), startDate, endDate);
            if (!"".equals(errBRJMsg)) {
                mapRtn.put("status", -1);
                mapRtn.put("message", errBRJMsg);
                return mapRtn;
            }

            // 计算休假总时长
            List<WaLeaveDaytime> daytimeList = new ArrayList<>();
            long leaveDate = startDate;
            while (leaveDate <= endDate) {
                WaWorktimeDetail worktimeDetail = pbMap.get(leaveDate);
                // 查询当天使用的班次集合
                List<Integer> shiftDefIdList = worktimeDetail.doGetShiftDefIdList();
                if (leaveDate == startDate && CollectionUtils.isNotEmpty(applySaveDto.getStartShifts())) {
                    shiftDefIdList = applySaveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList());
                } else if (leaveDate == endDate && CollectionUtils.isNotEmpty(applySaveDto.getEndShifts())) {
                    shiftDefIdList = applySaveDto.getEndShifts().stream().map(Long::intValue).collect(Collectors.toList());
                }
                // 遍历每个班次判断时间重叠以及计算休假时长
                for (Integer shiftDefId : shiftDefIdList) {
                    WaShiftDef shiftDef = shiftMap.get(shiftDefId);
                    WaLeaveDaytime dayTime = mobileV16Service.doBuildLeaveDaytime(shiftMap, pbMap, leaveTimeMap,
                            leaveDate, waLeaveType, applySaveDto.getEmpid(), shiftDefId);
                    // 时间重叠校验
                    boolean timeOverlap = mobileV16Service.checkLeaveDayTimeOverlap(empInfo.getEmpid(), dayTime,
                            shiftDef, waLeaveType, shiftMap);
                    if (timeOverlap) {
                        mapRtn.put("status", -1);
                        mapRtn.put("message", messageResource.getMessage("L005744", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                        return mapRtn;
                    }
                    totalTimeDuration = totalTimeDuration.add(BigDecimal.valueOf(dayTime.getTimeDuration()));
                    daytimeList.add(dayTime);
                }
                leaveDate = leaveDate + 86400;
            }
            allDaytimeList.addAll(daytimeList);

            // 假期时效性校验
            Map<String, Object> validateResultMap = doValidateTimeControl(applySaveDto, leaveTimeMap, waLeaveType,
                    totalTimeDuration, daytimeList, empInfo, pbMap, shiftMap);
            if (null != validateResultMap && Integer.valueOf(validateResultMap.get("status").toString()).equals(-1)) {
                return validateResultMap;
            }
            // 自定义校验
            Map checkRuleParams = new HashMap() {{
                put("startDate", DateUtil.getTimesampByDateStr2(startTimeStr));
                put("endDate", DateUtil.getTimesampByDateStr2(endTimeStr));
                put("shalfday", leaveTimeMap.get("shalfday"));
                put("ehalfday", leaveTimeMap.get("ehalfday"));
            }};
            String errMsg = waLeaveCoreService.checkLeaveType(Long.valueOf(userInfo.getTenantId()),
                    userInfo.getTenantId(), empInfo.getEmpid(), waLeaveType.getLeaveTypeId(), checkRuleParams);
            if (StringUtils.isNotBlank(errMsg)) {
                mapRtn.put("status", -1);
                mapRtn.put("message", messageResource.getMessage(errMsg, new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
                return mapRtn;
            }
            Integer quotaType = waLeaveType.getQuotaType();
            if (waLeaveType.getQuotaType() == null) {
                if (waLeaveType.getLeaveType() == 3) {
                    quotaType = 2;
                } else {
                    quotaType = 1;
                }
            }
            // 上级假期校验
            // 增加自定义执行逻辑
            WaCustomLogicConfigDo customLogicConfigDo = waCustomLogicConfigDo.getByCode(waLeaveType.getBelongOrgid(),
                    CustomLogicBelongBusinessEnum.LEAVE.getCode(), CustomLogicCodeEnum.UPPER_LEVEL_CHECK.getCode());
            if (null != customLogicConfigDo) {
                Map<String, Object> params = new HashMap<>();
                params.put("waLeaveType", waLeaveType);
                params.put("empInfo", empInfo);
                params.put("totalTimeDuration", totalTimeDuration);
                params.put("quotaType", quotaType);
                params.put("startDate", DateUtil.getTimesampByDateStr2(startTimeStr));
                params.put("endDate", DateUtil.getTimesampByDateStr2(endTimeStr));
                Map<String, Object> exeResult = (Map<String, Object>) waCustomLogicConfigService.doExecute(customLogicConfigDo, params);
                if (null != exeResult && (Integer) exeResult.get("status") != 0) {
                    return exeResult;
                }
            } else {// 系统标准逻辑
                Map<String, Object> upperLevelCheckResult = checkUpperLevel(waLeaveType, empInfo, totalTimeDuration, quotaType,
                        DateUtil.getTimesampByDateStr2(startTimeStr), DateUtil.getTimesampByDateStr2(endTimeStr));
                if (null != upperLevelCheckResult && (Integer) upperLevelCheckResult.get("status") != 0) {
                    return upperLevelCheckResult;
                }
            }
        }

        // 两小时请假次数控管，增加请2小时休假，一年上限12次的控管。（适用于除出差以外的所有假期类型）
        Map<String, Object> checkLeaveCountResult = checkLeaveCount(userInfo, empInfo, mapRtn);
        if (checkLeaveCountResult != null) {
            return checkLeaveCountResult;
        }

        // 请假时间不允许为0
        if (totalTimeDuration.floatValue() <= 0) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L005727", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            return mapRtn;
        }

        // 请假配额校验
        if (waLeaveType.getLeaveType() != 4) {
            String errorMsg = mobileV16Service.checkOrDecLeaveQuota(waLeaveType, allDaytimeList, empInfo,
                    userInfo.getUserId(), true,
                    Integer.valueOf(LeaveStatusEnum.LEAVE_STATUS_1.value),
                    applySaveDto.getHomeLeaveType(), applySaveDto.getMarriageStatus());
            if (StringUtils.isNotBlank(errorMsg)) {
                mapRtn.put("status", -1);
                mapRtn.put("message", errorMsg);
                return mapRtn;
            }
        }

        // 判断该假期类型的一次请假的最大最小
        String checkMaxMinLeaveStr = mobileV16Service.checkMaxMinLeave(waLeaveType, totalTimeDuration, SessionBeanUtil.getLanguage());
        if (StringUtils.isNotBlank(checkMaxMinLeaveStr)) {
            mapRtn.put("status", -1);
            mapRtn.put("message", checkMaxMinLeaveStr);
            return mapRtn;
        }
        // 返回值
        if (waLeaveType.getAcctTimeType() == 1) {// 天
            if (waLeaveType.getRoundTimeUnit() != null && totalTimeDuration.floatValue() % waLeaveType.getRoundTimeUnit() > 0) {
                mapRtn.put("status", -1);
                mapRtn.put("message", messageResource.getMessage("L005569", new Object[]{waLeaveType.getLeaveName(), waLeaveType.getRoundTimeUnit()}, new Locale(SessionBeanUtil.getLanguage())));
                return mapRtn;
            }
            mapRtn.put("data", messageResource.getMessage("L005571", new Object[]{totalTimeDuration}, new Locale(SessionBeanUtil.getLanguage())));
        } else {// 小时
            if (waLeaveType.getRoundTimeUnit() != null && totalTimeDuration.floatValue() % (60 * waLeaveType.getRoundTimeUnit()) > 0) {
                mapRtn.put("status", -1);
                mapRtn.put("message", messageResource.getMessage("L005570", new Object[]{waLeaveType.getLeaveName(), waLeaveType.getRoundTimeUnit()}, new Locale(SessionBeanUtil.getLanguage())));
                return mapRtn;
            }
            mapRtn.put("data", totalTimeDuration.floatValue() % 60 > 0 ? messageResource.getMessage("L005572", new Object[]{totalTimeDuration.longValue() / 60, totalTimeDuration.longValue() % 60}, new Locale(SessionBeanUtil.getLanguage()))
                    : messageResource.getMessage("L005573", new Object[]{totalTimeDuration.floatValue() / 60}, new Locale(SessionBeanUtil.getLanguage())));
        }
        mapRtn.put("timeUnit", waLeaveType.getAcctTimeType());
        mapRtn.put("timeUnitTxt", PreTimeUnitEnum.getName(waLeaveType.getAcctTimeType()));
        mapRtn.put("originalDuration", totalTimeDuration.doubleValue());
        if (waLeaveType.getAcctTimeType() == 1) {
            mapRtn.put("durationValue", totalTimeDuration.doubleValue());
        } else {
            BigDecimal durationValue = totalTimeDuration.divide(BigDecimal.valueOf(60), 2, RoundingMode.HALF_UP);
            mapRtn.put("durationValue", durationValue.doubleValue());
        }
        mapRtn.put("status", 1);
        mapRtn.put("message", "success");
        return mapRtn;
    }

    /**
     * 两小时请假次数控管，增加请2小时休假，一年上限12次的控管。（适用于除出差以外的所有假期类型）
     *
     * @param userInfo
     * @param empInfo
     * @param mapRtn
     * @return
     * @throws ParseException
     */
    private Map<String, Object> checkLeaveCount(UserInfo userInfo, SysEmpInfo empInfo, Map<String, Object> mapRtn) throws ParseException {
        String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + userInfo.getTenantId() + RedisKeyDefine.IS_TWOHOUR_LEAVE_CONTROL);
        if (!"1".equals(isOpen)) {
            return null;
        }
        //查询一年中请两小时假的次数
        Map leaveCountParams = new HashMap();
        leaveCountParams.put("empid", empInfo.getEmpid());
        Long curTime = DateUtil.getCurrentTime(true);
        leaveCountParams.put("startTime", DateUtilExt.getYearBeginTime(curTime));
        leaveCountParams.put("endTime", DateUtilExt.getYearsEndTime(curTime));
        Integer leaveCount = waMapper.getEmpTwoHourLeaveCount(leaveCountParams);
        if (leaveCount >= 12) {
            mapRtn.put("status", -1);
            mapRtn.put("message", messageResource.getMessage("L005924", new Object[]{}, new Locale(SessionBeanUtil.getLanguage())));
            return mapRtn;
        }
        return null;
    }

    /**
     * 假期时效性校验
     *
     * @param applySaveDto
     * @param leaveTimeMap
     * @param waLeaveType
     * @param totalTimeDuration
     * @param daytimeList
     * @param empInfo
     * @param pbMap
     * @param shiftMap
     * @return
     * @throws JsonProcessingException
     */
    private Map<String, Object> doValidateTimeControl(LeaveApplySaveDto applySaveDto,
                                                      Map<String, Object> leaveTimeMap,
                                                      WaLeaveType waLeaveType,
                                                      BigDecimal totalTimeDuration,
                                                      List<WaLeaveDaytime> daytimeList,
                                                      SysEmpInfo empInfo,
                                                      Map<Long, WaWorktimeDetail> pbMap,
                                                      Map<Integer, WaShiftDef> shiftMap) throws JsonProcessingException {
        Integer period = (Integer) leaveTimeMap.get("period");
        String startTimeStr = (String) leaveTimeMap.get("starttime");
        String endTimeStr = (String) leaveTimeMap.get("endtime");
        int tmType = empInfo.getTmType();

        if (!applySaveDto.isValidateTimeControl()) {
            return null;
        }
        Optional<Boolean> openTimeControlOpt = Optional.ofNullable(waLeaveType.getIsOpenTimeControl());
        if (!openTimeControlOpt.isPresent() || !openTimeControlOpt.get()) {
            return null;
        }

        Optional<Object> timelinessControlOpt = Optional.ofNullable(waLeaveType.getTimelinessControlJsonb());
        List<TimeControlPeriod> periods = new ArrayList<>();
        if (timelinessControlOpt.isPresent()) {
            //解析时效性规则
            PGobject pGobject = (PGobject) waLeaveType.getTimelinessControlJsonb();
            periods = new ObjectMapper().readValue(pGobject.getValue(), new TypeReference<List<TimeControlPeriod>>() {
            });
        }
        if (CollectionUtils.isEmpty(periods)) {
            return null;
        }

        //有时效性规则方进行休假时效性判断
        float leaveDuration = totalTimeDuration.floatValue();
        Optional<Boolean> continuousApplicationOpt = Optional.ofNullable(waLeaveType.getContinuousApplication());
        //有时效性规则且适用连续日期休假方进行连续性休假判断
        if (continuousApplicationOpt.isPresent() && continuousApplicationOpt.get()) {
            long leaveStartDate = DateUtil.getTimesampByDateStr2(startTimeStr);
            long leaveEndDate = DateUtil.getTimesampByDateStr2(endTimeStr);
            leaveDuration = calContinuousLeaveDuration(leaveDuration, leaveStartDate, leaveEndDate,
                    daytimeList, empInfo, waLeaveType);
        }
        if (waLeaveType.getAcctTimeType() == 2) {
            leaveDuration = leaveDuration / 60;
        }

        // 查询班次
        long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
        WaWorktimeDetail sDetail = pbMap.get(startDate);
        List<Integer> startShiftDefIdList = CollectionUtils.isNotEmpty(applySaveDto.getStartShifts())
                ? applySaveDto.getStartShifts().stream().map(Long::intValue).collect(Collectors.toList())
                : sDetail.doGetShiftDefIdList();
        List<WaShiftDef> startShiftDefList = startShiftDefIdList.stream()
                .map(shiftMap::get)
                .filter(it -> !Objects.isNull(it))
                .sorted(Comparator.comparing(WaShiftDef::doGetRealStartTime)).collect(Collectors.toList());

        for (TimeControlPeriod timeControlPeriod : periods) {
            Float min = Optional.ofNullable(timeControlPeriod.getMin()).orElse(0f);
            Float max = Optional.ofNullable(timeControlPeriod.getMax()).orElse(0f);
            if (min <= leaveDuration && leaveDuration < max) {
                // -1 提前,1延后
                Integer timeControlType = timeControlPeriod.getType();
                //设置时长
                Float controlTimeDuration = timeControlPeriod.getDuration();
                //1、天,2、小时
                Integer controlTimeUnit = timeControlPeriod.getUnit();
                long start = DateUtil.getTimesampByDateStr2(startTimeStr);

                // 查询班次
                WaShiftDef startShift = CollectionUtils.isNotEmpty(startShiftDefList)
                        ? startShiftDefList.get(0)
                        : shiftMap.get(sDetail.getShiftDefId());
                WaShiftDef startShiftWorkTime = CdWaShiftUtil.getShiftWorkTime(startShift);

                // 计算休假开始时间
                String sHalfDay = (String) leaveTimeMap.get("shalfday");
                if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(period)
                        || PeriodTypeEnum.PERIOD_TYPE_FOUR.getIndex().equals(period)) {
                    start = start + startShiftWorkTime.getStartTime() * 60;
                } else if (PeriodTypeEnum.PERIOD_TYPE_THREE.getIndex().equals(period)) {
                    start = DateUtil.convertStringToDateTime(startTimeStr, "yyyy-MM-dd HH:mm", true);
                } else if (PeriodTypeEnum.PERIOD_TYPE_NINE.getIndex().equals(period)) {
                    if (sHalfDay != null && sHalfDay.equals("P")) {
                        if (startShift.getIsHalfdayTime() != null && startShift.getIsHalfdayTime() && startShift.getHalfdayTime() != null && startShift.getHalfdayTime() > 0) {
                            start = start + startShift.getHalfdayTime() * 60;
                        } else if (startShift.getIsNoonRest() != null && startShift.getIsNoonRest()) {
                            start = start + startShift.getNoonRestEnd() * 60;
                        } else {
                            start = start + startShiftWorkTime.getStartTime() * 60;
                        }
                    } else {
                        start = start + startShiftWorkTime.getStartTime() * 60;
                    }
                }

                if (timeControlType != null && controlTimeDuration != null) {
                    if (controlTimeUnit == 1) {//延后
                        controlTimeDuration = getRealControlTimeDuration(start, shiftMap, empInfo, tmType, timeControlPeriod);
                        //如果是天需要*24，后边逻辑不用修改
                        controlTimeDuration *= 24;
                    }
                    Long curDateTime = DateUtil.getCurrentTime(true);
                    Long lastApplyTime = 0L;
                    controlTimeDuration = controlTimeDuration * 60 * 60;
                    if (timeControlType == -1) {
                        //假期时效性设置为提前
                        lastApplyTime = start - controlTimeDuration.intValue();
                    } else if (timeControlType == 1) {
                        //假期时效性设置为延后
                        lastApplyTime = start + controlTimeDuration.intValue();
                    }
                    if (curDateTime > lastApplyTime) {
                        Map<String, Object> mapRtn = new HashMap<>();
                        mapRtn.put("status", -1);
                        mapRtn.put("message", "申请时间超过请假时效性，不允许申请");
                        return mapRtn;
                    }
                }
            }
        }

        return null;
    }

    private Integer getUpperLeaveTypeId(WaLeaveType waLeaveType, Map<Integer, LeaveQuotaConfigDo> upperLeaveQuotaMap,
                                        Map<Integer, Integer> upperLeaveTypeMap) {
        Integer upperLeaveTypeId;
        if (null != waLeaveType.getUpperLevelQuotaTypeId()) {
            LeaveQuotaConfigDo upperQuotaConfigDo = leaveQuotaConfigDo.getConfigById(waLeaveType.getBelongOrgid(), waLeaveType.getUpperLevelQuotaTypeId());
            if (null == upperQuotaConfigDo) {
                return null;
            }
            upperLeaveTypeId = upperQuotaConfigDo.getLeaveTypeId();
            upperLeaveQuotaMap.put(waLeaveType.getLeaveTypeId(), upperQuotaConfigDo);
        } else {
            upperLeaveTypeId = waLeaveType.getUpperLevel();
        }
        upperLeaveTypeMap.put(upperLeaveTypeId, waLeaveType.getLeaveTypeId());
        return upperLeaveTypeId;
    }

    /**
     * 上级假期校验(系统标准逻辑)
     *
     * @param waLeaveType
     * @param empInfo
     * @param totalTimeDuration
     * @param quotaType
     * @param startDate
     * @param endDate
     * @return
     * @throws Exception
     */
    public Map<String, Object> checkUpperLevel(WaLeaveType waLeaveType,
                                               SysEmpInfo empInfo, BigDecimal totalTimeDuration,
                                               Integer quotaType, long startDate, long endDate) throws Exception {
        if (waLeaveType.getUpperLevel() == null && null == waLeaveType.getUpperLevelQuotaTypeId()) {
            return null;
        }
        // 查找所有的上级假期（逐层往上找）
        List<Integer> upperLeaveTypeIds = Lists.newArrayList();
        List<WaLeaveType> upperLeaveTypeList = Lists.newArrayList();
        Map<Integer, LeaveQuotaConfigDo> upperLeaveQuotaMap = new HashMap<>();
        Map<Integer, Integer> upperLeaveTypeMap = new HashMap<>();

        // 查找当前假期类型的上级假期
        Integer upperLeaveTypeId = getUpperLeaveTypeId(waLeaveType, upperLeaveQuotaMap, upperLeaveTypeMap);
        if (null == upperLeaveTypeId) {
            return null;
        }

        boolean upperLevelFlag = true;
        while (upperLevelFlag) {
            WaLeaveType upperLevelLeaveType = waLeaveTypeMapper.selectByPrimaryKey(upperLeaveTypeId);
            if (null == upperLevelLeaveType) {
                upperLevelFlag = false;
                continue;
            }
            //CAIDAOM-1710 铁通需求
            //产假固定额度不生成假期额度，则对上期假期配额进行校验时，
            //若上级假期为产假，则不做校验，反之则进行上级假期额度校验
            if (upperLevelLeaveType.getLeaveType() == 4) {
                upperLevelFlag = false;
                continue;
            }
            if (upperLeaveTypeIds.contains(upperLeaveTypeId)) {
                upperLevelFlag = false;
                continue;
            }
            upperLeaveTypeIds.add(upperLeaveTypeId);
            upperLeaveTypeList.add(upperLevelLeaveType);
            if (null == upperLevelLeaveType.getUpperLevel() && null == upperLevelLeaveType.getUpperLevelQuotaTypeId()) {
                upperLevelFlag = false;
                continue;
            }
            upperLeaveTypeId = getUpperLeaveTypeId(upperLevelLeaveType, upperLeaveQuotaMap, upperLeaveTypeMap);
        }

        if (CollectionUtils.isEmpty(upperLeaveTypeList)) {
            return null;
        }
        /*Map<Long, LeaveQuotaConfigDo> upperLeaveQuotaConfigMap = new HashMap<>();
        List<LeaveQuotaConfigDo> configs = leaveQuotaConfigDo.getConfigListByIds(empInfo.getBelongOrgId(), upperLeaveTypeIds);
        if (CollectionUtils.isNotEmpty(configs)) {
            upperLeaveQuotaConfigMap = configs.stream().collect(Collectors.toMap(LeaveQuotaConfigDo::getConfigId, Function.identity(), (v1, v2) -> v2));
        }*/
        //待申请假期类型配额（非调休）
        List<EmpQuoDto> leaveQuotaList = waMapper.selectUsableEmpQuotaList(empInfo.getEmpid(), waLeaveType.getLeaveTypeId(), startDate, endDate);
        //待申请假期类型配额（调休）
        List<UsableCompensatoryQuotaDto> compensatoryQuotaList = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empInfo.getEmpid(), waLeaveType.getLeaveTypeId(), startDate, endDate);

        for (int i = upperLeaveTypeList.size(); i > 0; i--) {
            WaLeaveType upperLevelLeaveType = upperLeaveTypeList.get(i - 1);
            if (upperLevelLeaveType == null || this.checkLeaveTypeIfEmpShow(upperLevelLeaveType) || this.checkEmpLeaveAvailable(upperLevelLeaveType, empInfo)) {
                continue;
            }
            Integer upperLevelLeaveQuotaType = upperLevelLeaveType.getQuotaType();
            if (upperLevelLeaveQuotaType == null) {
                upperLevelLeaveQuotaType = upperLevelLeaveType.getLeaveType() == 3 ? QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex()
                        : QuotaTypeEnum.ISSUED_ANNUALLY.getIndex();
            }

            // 查找此假期类型是谁的上级
            Integer upperLevelLeaveTypeId = upperLevelLeaveType.getLeaveTypeId();
            if (!upperLeaveTypeMap.containsKey(upperLevelLeaveTypeId) || null == upperLeaveTypeMap.get(upperLevelLeaveTypeId)) {
                continue;
            }
            Integer subLeaveTypeId = upperLeaveTypeMap.get(upperLevelLeaveTypeId);
            // 查找此假期类型的上级类型
            LeaveUpperTypeEnum upperType = LeaveUpperTypeEnum.LEAVE_TYPE;
            LeaveQuotaConfigDo upperQuotaConfigDo = null;
            if (upperLeaveQuotaMap.containsKey(subLeaveTypeId) && null != upperLeaveQuotaMap.get(subLeaveTypeId)) {
                upperQuotaConfigDo = upperLeaveQuotaMap.get(subLeaveTypeId);
                upperType = LeaveUpperTypeEnum.QUOTA_TYPE;
            }

            // 本次休假总时长
            BigDecimal total = totalTimeDuration;
            double usableQuota = 0;

            if (QuotaRestrictionTypeEnum.NO_LIMIT_QUOTA.getIndex().equals(waLeaveType.getQuotaRestrictionType())
                    && CollectionUtils.isEmpty(leaveQuotaList) && CollectionUtils.isEmpty(compensatoryQuotaList)) {

                // 查询上级假期可用额度
                if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(upperLevelLeaveQuotaType)) {
                    List<UsableCompensatoryQuotaDto> upperLevelCompensatoryQuotas = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empInfo.getEmpid(), upperLevelLeaveType.getLeaveTypeId(), startDate, endDate);

                    if (LeaveUpperTypeEnum.QUOTA_TYPE == upperType && null != upperQuotaConfigDo) {
                        LeaveQuotaConfigDo finalUpperQuotaConfigDo = upperQuotaConfigDo;
                        upperLevelCompensatoryQuotas = upperLevelCompensatoryQuotas.stream()
                                .filter(o -> o.getQuotaConfigId().equals(finalUpperQuotaConfigDo.getConfigId()))
                                .collect(Collectors.toList());
                    }

                    usableQuota = upperLevelCompensatoryQuotas.stream().mapToDouble(UsableCompensatoryQuotaDto::getUsableDay).sum();
                } else {
                    List<EmpQuoDto> upperLevelQuotas = waMapper.selectUsableEmpQuotaList(empInfo.getEmpid(), upperLevelLeaveType.getLeaveTypeId(), startDate, endDate);
                    upperLevelQuotas = upperLevelQuotas.stream().filter(quota -> quota.getUsedDay() == null
                            || quota.getUsedDay() <= 0
                            || quota.getUsableQuota() > 0).collect(Collectors.toList());

                    if (LeaveUpperTypeEnum.QUOTA_TYPE == upperType && null != upperQuotaConfigDo) {
                        LeaveQuotaConfigDo finalUpperQuotaConfigDo = upperQuotaConfigDo;
                        upperLevelQuotas = upperLevelQuotas.stream()
                                .filter(o -> o.getQuotaConfigId().equals(finalUpperQuotaConfigDo.getConfigId()))
                                .collect(Collectors.toList());
                    }

                    usableQuota = upperLevelQuotas.stream().mapToDouble(EmpQuoDto::getUsableQuota).sum();
                }

                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(null, usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                if ((Integer) mapRtn.get("status") != 0) {
                    return mapRtn;
                }
            } else {
                if (QuotaTypeEnum.COMPENSATORY_LEAVE.getIndex().equals(upperLevelLeaveQuotaType)) {
                    // 查询上级假期可用额度
                    List<UsableCompensatoryQuotaDto> upperLevelCompensatoryQuotas = waEmpCompensatoryQuotaMapper.selectUsableCompensatoryQuotaList(empInfo.getEmpid(), upperLevelLeaveType.getLeaveTypeId(), startDate, endDate);
                    if (LeaveUpperTypeEnum.QUOTA_TYPE == upperType && null != upperQuotaConfigDo) {
                        LeaveQuotaConfigDo finalUpperQuotaConfigDo = upperQuotaConfigDo;
                        upperLevelCompensatoryQuotas = upperLevelCompensatoryQuotas.stream()
                                .filter(o -> o.getQuotaConfigId().equals(finalUpperQuotaConfigDo.getConfigId()))
                                .collect(Collectors.toList());
                    }

                    if (CollectionUtils.isNotEmpty(upperLevelCompensatoryQuotas)) {
                        //上级假期配额，按年分组
                        Map<Integer, List<UsableCompensatoryQuotaDto>> upperLevelQuotaMap = upperLevelCompensatoryQuotas.stream().collect(Collectors.groupingBy(UsableCompensatoryQuotaDto::getYear));

                        //待申请假期，调休
                        if (CollectionUtils.isNotEmpty(compensatoryQuotaList)) {
                            for (UsableCompensatoryQuotaDto quotaDto : compensatoryQuotaList) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                List<UsableCompensatoryQuotaDto> upperLevelYearQuota = upperLevelQuotaMap.get(quotaDto.getYear());
                                if (CollectionUtils.isEmpty(upperLevelYearQuota)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelYearQuota.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(UsableCompensatoryQuotaDto::getUsableDay).sum();

                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableDay()));
                            }
                        }

                        //待申请假期，非调休
                        if (CollectionUtils.isNotEmpty(leaveQuotaList)) {
                            for (EmpQuoDto quotaDto : leaveQuotaList) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                List<UsableCompensatoryQuotaDto> upperLevelYearQuota = upperLevelQuotaMap.get(quotaDto.getPeriodYear());
                                if (CollectionUtils.isEmpty(upperLevelYearQuota)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelYearQuota.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(UsableCompensatoryQuotaDto::getUsableDay).sum();

                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getPeriodYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableQuota()));
                            }
                        }

                        if ((CollectionUtils.isEmpty(compensatoryQuotaList) && CollectionUtils.isEmpty(leaveQuotaList))
                                || !quotaType.equals(upperLevelLeaveQuotaType)) {
                            for (UsableCompensatoryQuotaDto quotaDto : upperLevelCompensatoryQuotas) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                List<UsableCompensatoryQuotaDto> upperLevelYearQuota = upperLevelQuotaMap.get(quotaDto.getYear());
                                if (CollectionUtils.isEmpty(upperLevelYearQuota)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelYearQuota.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(UsableCompensatoryQuotaDto::getUsableDay).sum();

                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableDay()));
                            }
                        }
                    }
                } else {
                    //非调休,上级配额
                    List<EmpQuoDto> upperLevelQuotas = waMapper.selectUsableEmpQuotaList(empInfo.getEmpid(), upperLevelLeaveType.getLeaveTypeId(), startDate, endDate);
                    upperLevelQuotas = upperLevelQuotas.stream()
                            .filter(quota -> quota.getUsedDay() == null || quota.getUsedDay() <= 0 || quota.getUsableQuota() > 0)
                            .collect(Collectors.toList());
                    if (LeaveUpperTypeEnum.QUOTA_TYPE == upperType && null != upperQuotaConfigDo) {
                        LeaveQuotaConfigDo finalUpperQuotaConfigDo = upperQuotaConfigDo;
                        upperLevelQuotas = upperLevelQuotas.stream()
                                .filter(o -> o.getQuotaConfigId().equals(finalUpperQuotaConfigDo.getConfigId()))
                                .collect(Collectors.toList());
                    }

                    if (CollectionUtils.isNotEmpty(upperLevelQuotas)) {
                        //上级假期配额，按年分组
                        Map<Integer, List<EmpQuoDto>> upperLevelQuotaMap = upperLevelQuotas.stream().collect(Collectors.groupingBy(EmpQuoDto::getPeriodYear));

                        //待申请，非调休
                        if (CollectionUtils.isNotEmpty(leaveQuotaList)) {
                            for (EmpQuoDto quotaDto : leaveQuotaList) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                //如果是结转过来的假期，则不做上级假期的校验
                                /*if (quotaDto.getOriginalQuotaId() != null) {
                                    if (!checkLeaveQuotaIfMerge(upperLeaveQuotaConfigMap.get(quotaDto.getQuotaConfigId()))) {
                                        break;
                                    }
                                }*/
                                List<EmpQuoDto> upperLevelQuotaDtoList = upperLevelQuotaMap.get(quotaDto.getPeriodYear());
                                if (CollectionUtils.isEmpty(upperLevelQuotaDtoList)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelQuotaDtoList.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(EmpQuoDto::getUsableQuota).sum();
                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getPeriodYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableQuota()));
                            }
                        }

                        //待申请，调休
                        if (CollectionUtils.isNotEmpty(compensatoryQuotaList)) {
                            for (UsableCompensatoryQuotaDto quotaDto : compensatoryQuotaList) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                List<EmpQuoDto> upperLevelQuotaDtoList = upperLevelQuotaMap.get(quotaDto.getYear());
                                if (CollectionUtils.isEmpty(upperLevelQuotaDtoList)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelQuotaDtoList.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(EmpQuoDto::getUsableQuota).sum();
                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableDay()));
                            }
                        }

                        if ((CollectionUtils.isEmpty(compensatoryQuotaList) && CollectionUtils.isEmpty(leaveQuotaList))
                                || !quotaType.equals(upperLevelLeaveQuotaType)) {
                            for (EmpQuoDto quotaDto : upperLevelQuotas) {
                                if (total.floatValue() <= 0) {
                                    break;
                                }
                                //如果是结转过来的假期，则不做上级假期的校验
                                /*if (quotaDto.getOriginalQuotaId() != null) {
                                    if (!checkLeaveQuotaIfMerge(upperLeaveQuotaConfigMap.get(quotaDto.getQuotaConfigId()))) {
                                        break;
                                    }
                                }*/
                                List<EmpQuoDto> upperLevelQuotaDtoList = upperLevelQuotaMap.get(quotaDto.getPeriodYear());
                                if (CollectionUtils.isEmpty(upperLevelQuotaDtoList)) {
                                    break;
                                }
                                //配额所属年份的上级假期可用配额
                                usableQuota = upperLevelQuotaDtoList.stream().filter(q -> q.getLastDate() <= quotaDto.getLastDate()).mapToDouble(EmpQuoDto::getUsableQuota).sum();
                                Map<String, Object> mapRtn = checkUpperLevelLeaveQuota(quotaDto.getPeriodYear(), usableQuota, upperLevelLeaveType, upperType, waLeaveType);
                                if ((Integer) mapRtn.get("status") != 0) {
                                    return mapRtn;
                                }
                                total = total.subtract(BigDecimal.valueOf(quotaDto.getUsableQuota()));
                            }
                        }
                    }
                }
            }
        }
        return null;
    }

    private boolean checkLeaveQuotaIfMerge(LeaveQuotaConfigDo config) {
        if (null == config) {
            return false;
        }
        return null != config.getCarryOverTo() && config.getCarryOverTo() == -1 && null != config.getCarryToType() && config.getCarryToType() == 2;
    }

    public Float getRealControlTimeDuration(long startDate,
                                            Map<Integer, WaShiftDef> shiftMap,
                                            SysEmpInfo empInfo,
                                            int tmType,
                                            TimeControlPeriod period) {
        Integer timeControlType = period.getType(); // -1 提前,1延后
        Float controlTimeDuration = period.getDuration(); //设置时长
        Integer controlTimeUnit = period.getUnit();//1.天,2.小时
        Integer limitationUnit = period.getLimitationUnit();
        if (timeControlType == null || controlTimeDuration == null || controlTimeUnit == null || limitationUnit == null) {
            return 0f;
        }
        if (limitationUnit == 2) {
            int workDayCount = 0;
            int ceilingControlTimeDuration = BigDecimal.valueOf(controlTimeDuration).setScale(0, RoundingMode.CEILING).intValue();
            long onlyStartDate = DateUtil.getOnlyDate(new Date(startDate * 1000));
            long onlyEndDate = onlyStartDate;
            Map<Long, WaWorktimeDetail> checkPbMap = new HashMap<>();
            int count = 0;
            boolean flag = true;
            if (timeControlType == -1) {
                while (flag) {
                    onlyStartDate = DateUtil.addDate(onlyEndDate * 1000, -ceilingControlTimeDuration);
                    onlyEndDate = DateUtil.addDate(onlyEndDate * 1000, -1);
                    checkPbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(), empInfo.getEmpid(), tmType, onlyStartDate, onlyEndDate, empInfo.getWorktimeType(), true);
                    if (MapUtils.isNotEmpty(checkPbMap)) {
                        checkPbMap.forEach((date, workTimeDetail) -> {
                            WaShiftDef detailShift = shiftMap.get(workTimeDetail.getShiftDefId());
                            if (DateTypeEnum.DATE_TYP_4.getIndex().equals(workTimeDetail.getDateType())) {
                                workTimeDetail.setDateType(detailShift.getDateType());
                            }
                        });
                        while (onlyStartDate <= onlyEndDate) {
                            count++;
                            if (checkPbMap.containsKey(onlyEndDate)) {
                                WaWorktimeDetail workTimeDetail = checkPbMap.get(onlyEndDate);
                                if (workTimeDetail.getDateType() == 1) {
                                    workDayCount++;
                                }
                                if (workDayCount == ceilingControlTimeDuration) {
                                    flag = false;
                                    break;
                                }
                            } else {
                                flag = false;
                                break;
                            }
                            onlyEndDate = DateUtil.addDate(onlyEndDate * 1000, -1);
                        }
                        onlyEndDate = onlyStartDate;
                    } else {
                        flag = false;
                    }
                }
                if (workDayCount < ceilingControlTimeDuration) {
                    return controlTimeDuration;
                } else {
                    return BigDecimal.valueOf(count).floatValue();
                }
            } else if (timeControlType == 1) {
                while (flag) {
                    onlyStartDate = DateUtil.addDate(onlyStartDate * 1000, 1);
                    onlyEndDate = DateUtil.addDate(onlyStartDate * 1000, ceilingControlTimeDuration - 1);
                    checkPbMap = waCommonService.getEmpWaWorktimeDetail(empInfo.getBelongOrgId(), empInfo.getEmpid(), tmType, onlyStartDate, onlyEndDate, empInfo.getWorktimeType(), true);
                    if (MapUtils.isNotEmpty(checkPbMap)) {
                        checkPbMap.forEach((date, workTimeDetail) -> {
                            WaShiftDef detailShift = shiftMap.get(workTimeDetail.getShiftDefId());
                            if (DateTypeEnum.DATE_TYP_4.getIndex().equals(workTimeDetail.getDateType())) {
                                workTimeDetail.setDateType(detailShift.getDateType());
                            }
                        });
                        while (onlyStartDate <= onlyEndDate) {
                            count++;
                            if (checkPbMap.containsKey(onlyStartDate)) {
                                WaWorktimeDetail workTimeDetail = checkPbMap.get(onlyStartDate);
                                if (workTimeDetail.getDateType() == 1) {
                                    workDayCount++;
                                }
                                if (workDayCount == ceilingControlTimeDuration) {
                                    flag = false;
                                    break;
                                }
                            } else {
                                flag = false;
                                break;
                            }
                            onlyStartDate = DateUtil.addDate(onlyStartDate * 1000, 1);
                        }
                        onlyStartDate = onlyEndDate;
                    } else {
                        flag = false;
                    }
                }
                if (workDayCount < ceilingControlTimeDuration) {
                    return controlTimeDuration;
                } else {
                    return BigDecimal.valueOf(count).floatValue();
                }
            }
        }
        return controlTimeDuration;
    }

    /**
     * 计算连续假期时长
     *
     * @param leaveDuration  休假时长
     * @param leaveType      假期类型
     * @param leaveStartDate 休假开始时间
     * @param leaveEndDate   休假结束时间
     * @param daytimeList    休假单详情
     * @return 返回结果
     */
    private float calContinuousLeaveDuration(float leaveDuration, long leaveStartDate, long leaveEndDate,
                                             List<WaLeaveDaytime> daytimeList, SysEmpInfo empInfo, WaLeaveType leaveType) {
        if (CollectionUtils.isEmpty(daytimeList)) {
            return leaveDuration;
        }
        Integer leaveTypeId = leaveType.getLeaveTypeId();
        Long empId = empInfo.getEmpid();
        WaLeaveTypeDef waLeaveTypeDef = waLeaveTypeDefMapper.selectByPrimaryKey(leaveTypeId);
        // 哺乳假判断
        boolean isBrj = waLeaveTypeDef != null && "BRJ".equals(waLeaveTypeDef.getLeaveTypeDefCode());
        //查询员工已申请的同类型休假单
        List<WaLeaveDaytimeExtPo> leaveDayTimes = waMapper.selectLeaveDaytimeList(empId, null, null, isBrj, leaveTypeId);
        if (CollectionUtils.isNotEmpty(leaveDayTimes)) {
            //休假每日明细升序排序
            daytimeList.sort(Comparator.comparing(WaLeaveDaytime::getLeaveDate));
            if (leaveStartDate == leaveEndDate) {
                leaveDayTimes.add(ObjectConverter.convert(daytimeList.get(0), WaLeaveDaytimeExtPo.class));
            } else {
                leaveDayTimes.add(ObjectConverter.convert(daytimeList.get(0), WaLeaveDaytimeExtPo.class));
                leaveDayTimes.add(ObjectConverter.convert(daytimeList.get(daytimeList.size() - 1), WaLeaveDaytimeExtPo.class));
            }
            //获取实际有效地休假时间段
            List<WaLeaveDaytimeExtPo> validLeaveDayTimes = calRealLeaveDayTimes(empId, leaveDayTimes);
            if (CollectionUtils.isEmpty(validLeaveDayTimes)) {
                return leaveDuration;
            }
            Map<Long, WaLeaveDaytimeExtPo> validLeaveDayTimeMap = validLeaveDayTimes.stream().collect(Collectors.toMap(WaLeaveDaytimeExtPo::getLeaveDate, Function.identity(), (v1, v2) -> v2));
            //当前申请中的休假单开始日期之前的休假单（包含休假开始日期）
            List<WaLeaveDaytimeExtPo> lessLeaveDayTimes = validLeaveDayTimes.stream().filter(l -> leaveStartDate >= l.getLeaveDate()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(lessLeaveDayTimes)) {
                lessLeaveDayTimes = lessLeaveDayTimes.stream().sorted(Comparator.comparing(WaLeaveDaytimeExtPo::getLeaveDate).reversed()).collect(Collectors.toList());
                WaLeaveDaytimeExtPo checkDayTime = lessLeaveDayTimes.get(0);
                long checkDate = checkDayTime.getLeaveDate();
                long endDate = lessLeaveDayTimes.get(lessLeaveDayTimes.size() - 1).getLeaveDate();
                boolean checkFlag = true;
                while (checkFlag) {
                    checkDate = DateUtil.addDate(checkDate * 1000, -1);
                    if (validLeaveDayTimeMap.containsKey(checkDate)) {
                        WaLeaveDaytimeExtPo preCheckDayTime = validLeaveDayTimeMap.get(checkDate);
                        if (HalfDayTypeEnum.A.name().equals(checkDayTime.getShalfDay())) {
                            if (HalfDayTypeEnum.A.name().equals(preCheckDayTime.getShalfDay()) && HalfDayTypeEnum.P.name().equals(preCheckDayTime.getEhalfDay())) {
                                leaveDuration += preCheckDayTime.getTimeDuration();
                            } else if (HalfDayTypeEnum.P.name().equals(preCheckDayTime.getShalfDay()) && HalfDayTypeEnum.P.name().equals(preCheckDayTime.getEhalfDay())) {
                                leaveDuration += preCheckDayTime.getTimeDuration();
                                checkFlag = false;
                            } else {
                                checkFlag = false;
                            }
                            checkDayTime = preCheckDayTime;
                        } else {
                            checkFlag = false;
                        }
                    } else {
                        checkFlag = false;
                    }
                    if (checkDate == endDate) {
                        checkFlag = false;
                    }
                }
            }
            //当前申请中的休假单结束日期之后的休假单（包含休假结束日期）
            List<WaLeaveDaytimeExtPo> greaterLeaveDayTimes = validLeaveDayTimes.stream().filter(l -> leaveEndDate <= l.getLeaveDate()).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(greaterLeaveDayTimes)) {
                greaterLeaveDayTimes = greaterLeaveDayTimes.stream().sorted(Comparator.comparing(WaLeaveDaytimeExtPo::getLeaveDate)).collect(Collectors.toList());
                WaLeaveDaytimeExtPo checkDayTime = greaterLeaveDayTimes.get(0);
                long checkDate = checkDayTime.getLeaveDate();
                long endDate = greaterLeaveDayTimes.get(greaterLeaveDayTimes.size() - 1).getLeaveDate();
                boolean checkFlag = true;
                while (checkFlag) {
                    checkDate = DateUtil.addDate(checkDate * 1000, 1);
                    if (validLeaveDayTimeMap.containsKey(checkDate)) {
                        WaLeaveDaytimeExtPo nextCheckDayTime = validLeaveDayTimeMap.get(checkDate);
                        if (HalfDayTypeEnum.P.name().equals(checkDayTime.getEhalfDay())) {
                            if (HalfDayTypeEnum.A.name().equals(nextCheckDayTime.getShalfDay()) && HalfDayTypeEnum.P.name().equals(nextCheckDayTime.getEhalfDay())) {
                                leaveDuration += nextCheckDayTime.getTimeDuration();
                            } else if (HalfDayTypeEnum.A.name().equals(nextCheckDayTime.getShalfDay()) && HalfDayTypeEnum.A.name().equals(nextCheckDayTime.getEhalfDay())) {
                                leaveDuration += nextCheckDayTime.getTimeDuration();
                                checkFlag = false;
                            } else {
                                checkFlag = false;
                            }
                            checkDayTime = nextCheckDayTime;
                        } else {
                            checkFlag = false;
                        }
                    } else {
                        checkFlag = false;
                    }
                    if (checkDate == endDate) {
                        checkFlag = false;
                    }
                }
            }
        }
        return leaveDuration;
    }

    /**
     * 获取实际有效地休假时间段
     * <p>
     * 根据休假单periodType（1为整天天，9为天，非整天）类型修改修改成半天类型，便于进行是否连续判断
     * 这里休假单详情查询的是未销假完的，如果有多条，则当天休假为全天，修改为AP（整天）
     * 如果只有一条，首先判断销假时长，
     * 已销假：
     * 部分销假，只可能存在部分销假，销假单（有效的）只存在即AA（上半天）/PP（下半天），
     * 销假单AA（上半天），则休假单修改为PP（下半天）
     * 销假单PP（下半天），则休假单修改为AA（上半天）
     * 未销假：
     * periodType=1，修改为AP（整天）
     * periodType=9，半天类型，不做修改
     *
     * @param empId              员工id
     * @param leaveDaytimeExtPos 休假明细
     * @return 结果
     */
    private List<WaLeaveDaytimeExtPo> calRealLeaveDayTimes(Long empId, List<WaLeaveDaytimeExtPo> leaveDaytimeExtPos) {
        List<WaLeaveDaytimeExtPo> leaveDayTimes = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(leaveDaytimeExtPos)) {
            //查询员工已申请的同类型销假单
            List<Integer> leaveIds = leaveDaytimeExtPos.stream().map(WaLeaveDaytimeExtPo::getLeaveId).distinct().collect(Collectors.toList());
            List<WaLeaveCancelDayTime> allLeaveCancelDayTimes = waMapper.selectLeaveCancelDaytimeList(empId, null, null, leaveIds);
            //构造Map
            Map<Long, List<WaLeaveDaytimeExtPo>> leaveDayTimesMap = leaveDaytimeExtPos.stream().collect(Collectors.groupingBy(WaLeaveDaytimeExtPo::getLeaveDate));
            Map<Long, List<WaLeaveCancelDayTime>> leaveCancelDayTimesMap = allLeaveCancelDayTimes.stream().collect(Collectors.groupingBy(WaLeaveCancelDayTime::getLeaveCancelDate));
            for (Map.Entry<Long, List<WaLeaveDaytimeExtPo>> entry : leaveDayTimesMap.entrySet()) {
                Long checkDate = entry.getKey();
                List<WaLeaveDaytimeExtPo> values = entry.getValue();
                WaLeaveDaytimeExtPo dayTime = new WaLeaveDaytimeExtPo();
                dayTime.setLeaveDate(checkDate);
                //休假单每日详情多条，根据不重叠逻辑，整天
                if (values.size() > 1) {
                    dayTime.setShalfDay(HalfDayTypeEnum.A.name());
                    dayTime.setEhalfDay(HalfDayTypeEnum.P.name());
                    dayTime.setTimeDuration(1f);
                } else {
                    dayTime = values.get(0);
                    if (dayTime.getCancelTimeDuration() != null && dayTime.getCancelTimeDuration() > 0) {
                        if (!leaveCancelDayTimesMap.containsKey(checkDate)) {
                            continue;
                        }
                        //整天，部分销假
                        List<WaLeaveCancelDayTime> leaveCancelDayTimes = leaveCancelDayTimesMap.get(checkDate);
                        //销上半天
                        boolean cancelSHalfDay = leaveCancelDayTimes.stream().anyMatch(l -> HalfDayTypeEnum.A.name().equals(l.getShalfDay()) && HalfDayTypeEnum.A.name().equals(l.getEhalfDay()));
                        if (cancelSHalfDay) {
                            dayTime.setShalfDay(HalfDayTypeEnum.P.name());
                            dayTime.setEhalfDay(HalfDayTypeEnum.P.name());
                            dayTime.setTimeDuration(0.5f);
                        }
                        //销下半天
                        boolean cancelEHalfDay = leaveCancelDayTimes.stream().anyMatch(l -> HalfDayTypeEnum.P.name().equals(l.getShalfDay()) && HalfDayTypeEnum.P.name().equals(l.getEhalfDay()));
                        if (cancelEHalfDay) {
                            dayTime.setShalfDay(HalfDayTypeEnum.A.name());
                            dayTime.setEhalfDay(HalfDayTypeEnum.A.name());
                            dayTime.setTimeDuration(0.5f);
                        }
                    } else {
                        if (PeriodTypeEnum.PERIOD_TYPE_ONE.getIndex().equals(Integer.valueOf(dayTime.getPeriodType()))) {
                            dayTime.setShalfDay(HalfDayTypeEnum.A.name());
                            dayTime.setEhalfDay(HalfDayTypeEnum.P.name());
                            dayTime.setTimeDuration(1f);
                        }
                    }
                }
                leaveDayTimes.add(dayTime);
            }
        }
        return leaveDayTimes;
    }

    /**
     * 校验上级假期配额
     *
     * @param year                年份
     * @param usableQuota         可用额度
     * @param upperLevelLeaveType 上级假期类型
     * @param upperType           上级类型
     * @param waLeaveType         当前申请的假期类型
     * @return
     */
    public Map<String, Object> checkUpperLevelLeaveQuota(Integer year, double usableQuota, WaLeaveType upperLevelLeaveType,
                                                         LeaveUpperTypeEnum upperType, WaLeaveType waLeaveType) {
        Map<String, Object> mapRtn = new HashMap<>();
        mapRtn.put("status", 0);
        mapRtn.put("message", "success");

        if (usableQuota <= 0) {
            return mapRtn;
        }

        // 上级假期余额校验
        if (!waLeaveTypeDo.doCheckUpperLevelQuota(usableQuota, waLeaveType, upperLevelLeaveType, upperType)) {
            return mapRtn;
        }

        Float upperRoundTimeUnit = upperLevelLeaveType.getRoundTimeUnit();
        upperRoundTimeUnit = upperRoundTimeUnit == null ? 0 : upperRoundTimeUnit;
        if (upperLevelLeaveType.getAcctTimeType() == 1) { //天
            if (usableQuota >= upperRoundTimeUnit) {
                String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.UPPER_LEAVE_DAY_NO_BALANCE, null).getMsg(), upperLevelLeaveType.getLeaveName(), upperLevelLeaveType.getLeaveName());
                if (null != year && year != 1) {
                    msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_DAY_NO_BALANCE, null).getMsg(), year, upperLevelLeaveType.getLeaveName(), upperLevelLeaveType.getLeaveName());
                }
                mapRtn.put("status", -1);
                mapRtn.put("message", msg);
                return mapRtn;
            }
        } else { //小时
            if (usableQuota >= (60 * upperRoundTimeUnit)) {
                String msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.UPPER_LEAVE_HOUR_NO_BALANCE, null).getMsg(), upperLevelLeaveType.getLeaveName(), upperLevelLeaveType.getLeaveName());
                if (null != year) {
                    msg = String.format(ResponseWrap.wrapResult(AttendanceCodes.LEAVE_HOUR_NO_BALANCE, null).getMsg(), year, upperLevelLeaveType.getLeaveName(), upperLevelLeaveType.getLeaveName());
                }
                mapRtn.put("status", -1);
                mapRtn.put("message", msg);
                return mapRtn;
            }
        }
        mapRtn.put("message", "success");
        return mapRtn;
    }

    public Map getEmpLeaveById(String belongOrgId, Integer leaveId) {
        Map params = new HashMap();
        params.put("belongOrgId", belongOrgId);
        params.put("leaveId", leaveId);
        Map map = waMapper.getEmpLeaveInfo(params);
        val leaveDetail = empLeaveMapper.getHomeLeaveTypeAndMarriageStatus(Long.valueOf(leaveId));
        if (map != null) {
            if (null != leaveDetail) {
                map.putAll(leaveDetail);
            }
            Integer periodType = (Integer) map.get("periodType");
            if (periodType != null) {
                if (periodType == 3) {
                    //小时
                    Long startTime = (Long) map.get("startTime");
                    Long endTime = (Long) map.get("endTime");

                    Long startDate = DateUtilExt.getTimeByPattern(startTime, "yyyy-MM-dd");
                    Long endDate = DateUtilExt.getTimeByPattern(endTime, "yyyy-MM-dd");

                    Long startMin = startTime - startDate;
                    Long endMin = endTime - endDate;

                    map.put("startTime", startDate);
                    map.put("endTime", endDate);
                    map.put("stime", startMin / 60);
                    map.put("etime", endMin / 60);
                } else if (periodType == 9) {
                    if (StringUtils.isBlank((CharSequence) map.get("shalfDay"))) {
                        //小时
                        Long startTime = (Long) map.get("startTime");
                        Long endTime = (Long) map.get("endTime");

                        Long startDate = DateUtilExt.getTimeByPattern(startTime, "yyyy-MM-dd");
                        Long endDate = DateUtilExt.getTimeByPattern(endTime, "yyyy-MM-dd");

                        Long startMin = startTime - startDate;
                        Long endMin = endTime - endDate;

                        map.put("startTime", startDate);
                        map.put("endTime", endDate);
                        map.put("stime", startMin / 60);
                        map.put("etime", endMin / 60);
                        map.put("periodType", 3);
                        map.put("showMin", true);
                        map.put("showDay", false);
                        map.keySet().removeIf(k -> k.equals("shalfDay") || k.equals("ehalfDay"));
                    }
                }
            }
        }
        return map;
    }
}