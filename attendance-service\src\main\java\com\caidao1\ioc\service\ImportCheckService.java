package com.caidao1.ioc.service;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.auth.mybatis.model.SysEmpInfoExample;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.script.ScriptBindable;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.ioc.util.IocUtil;
import com.caidao1.mobile.service.MobileV16Service;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.CdWaShiftUtil;
import com.caidaocloud.attendance.core.workflow.enums.BusinessCodeEnum;
import com.caidaocloud.attendance.service.application.service.IOvertimeApplyService;
import com.caidaocloud.attendance.service.application.service.IWfService;
import com.caidaocloud.attendance.service.interfaces.dto.LeaveApplySaveDto;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.web.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
@Service
public class ImportCheckService implements ScriptBindable {
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaLeaveTypeMapper waLeaveTypeMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private MobileV16Service mobileV16Service;
    @Autowired
    private WaMapper waMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaEmpShiftMapper waEmpShiftMapper;
    @Autowired
    private WaWorktimeMapper waWorktimeMapper;
    @Autowired
    private WaEmpGroupMapper waEmpGroupMapper;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private PlanEmpRelMapper waPlanEmpRelMapper;
    @Autowired
    private ClockPlanMapper waClockPlanMapper;
    @Autowired
    @Lazy
    private IWfService wfService;
    @Autowired
    private IOvertimeApplyService overtimeApplyService;

    public boolean checkEmpShift(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        String workno = row.get(fieldIdxMap.get("empid"));
        String reg_date_time = row.get(fieldIdxMap.get("reg_date_time"));
        String key = BaseConst.EMP_ + belongId + "_" + workno;
        String value = CDCacheUtil.getValue(key);
        if (!StringUtil.isNullOrEmpty(value)) {
            String[] arr = value.split(",");
            Long empid = Long.valueOf(arr[0]);
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("empid", empid);
            String pattern = "yyyy-MM-dd HH:mm:ss";
            if (!DateUtil.isValidDate(reg_date_time, pattern)) {
                return false;
            }
            Long regTime = DateUtil.convertStringToDateTime(reg_date_time, pattern, true);
            Long belongDate = DateUtil.getDateLong(regTime * 1000, "yyyy-MM-dd", true);

            String date = DateUtil.convertDateTimeToStr(regTime, "yyyy-MM-dd", true);

            Integer ym = Integer.valueOf(date.substring(0, date.lastIndexOf("-")).replaceAll("-", ""));
            Integer day = Integer.valueOf(date.substring(date.lastIndexOf("-") + 1));
            params.put("workDate", belongDate);// 年月日的秒
            params.put("startdate", ym);//年月
            params.put("day", day);//日

            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecord(params);
            if (shiftinfos != null && shiftinfos.size() > 0) {
                return true;
            }
        }
        return true;
    }

    /**
     * 校验成本中心
     *
     * @param row
     * @param rowIdx
     * @param rows
     * @param fieldIdxMap
     * @return
     */
    public boolean checkCostCenterSum(List<String> row, int rowIdx, List<List<String>> rows, Map<String, Integer> fieldIdxMap) {
        String curWorkno = row.get(fieldIdxMap.get("empid"));
        BigDecimal total = new BigDecimal(0);
        boolean isLast = true;
        for (int i = 0; i < rows.size(); i++) {
            List<String> rowCell = rows.get(i);
            String workno = rowCell.get(fieldIdxMap.get("empid"));
            BigDecimal scale = new BigDecimal(rowCell.get(fieldIdxMap.get("proportion_scale")));
            if (curWorkno.equals(workno)) {
                total = total.add(scale);
                if (i > rowIdx) {
                    isLast = false;
                    break;
                }
            }
        }
        if (isLast && total.doubleValue() != 100) {
            return false;
        }
        return true;
    }

    /**
     * 请假数据校验汇总
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @param value
     * @return
     */
    public boolean checkEmpLeaveWorkTime(List<String> row, Map<String, Integer> fieldIdxMap, String belongId, Object value) {
        try {
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            empInfoExample.createCriteria()
                    .andBelongOrgIdEqualTo(belongId).andDeletedEqualTo((short) 0)
                    .andStatsEqualTo(0).andWorknoEqualTo(String.valueOf(value));
            List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
            if (CollectionUtils.isNotEmpty(empInfoList)) {
                SysEmpInfo empInfo = empInfoList.get(0);

                int tmType = 1;
                if (empInfo.getTmType() != null) {
                    tmType = empInfo.getTmType().intValue();
                    if (tmType == 0) {
                        tmType = 1;
                    }
                }
                String startTimeStr = "";
                String endTimeStr = "";

                Object startTimeObj = row.get(fieldIdxMap.get("start_time"));
                Object endTimeObj = row.get(fieldIdxMap.get("end_time"));

                if (startTimeObj instanceof String) {
                    startTimeStr = row.get(fieldIdxMap.get("start_time"));
                    endTimeStr = row.get(fieldIdxMap.get("end_time"));

                    Pattern pattern = Pattern.compile("[0-9]*");
                    if (StringUtils.isNotBlank(startTimeStr)) {
                        Matcher isNum = pattern.matcher(startTimeStr);
                        if (isNum.matches()) {
                            startTimeStr = DateUtil.getTimeStrByTimesamp(Long.valueOf(startTimeStr));
                        }
                    }
                    if (StringUtils.isNotBlank(endTimeStr)) {
                        Matcher isNum = pattern.matcher(endTimeStr);
                        if (isNum.matches()) {
                            endTimeStr = DateUtil.getTimeStrByTimesamp(Long.valueOf(endTimeStr));
                        }
                    }
                } else if (startTimeObj instanceof Long) {
                    startTimeStr = DateUtil.getTimeStrByTimesamp((Long) startTimeObj);
                    endTimeStr = DateUtil.getTimeStrByTimesamp((Long) endTimeObj);
                }

                Long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
                Long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

                Long startTime = (Long) IocUtil.getDateValue(startTimeStr, "DATE");
                Long endTime = (Long) IocUtil.getDateValue(endTimeStr, "DATE");

                Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(belongId);
                Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongId, empInfo.getEmpid(), tmType, startDate, endDate, null, true);
                if (pbMap == null || shiftMap == null) {
                    return false;
                }
                pbMap.forEach((date, worktimeDetail) -> {
                    WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
                    if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                        worktimeDetail.setDateType(shiftDef.getDateType());
                    }
                });
                long tmpDate = startDate;
                while (tmpDate <= endDate) {
                    if (pbMap == null || pbMap.get(tmpDate) == null) {//没有排班记录
                        return false;
                    }
                    tmpDate = tmpDate + 24 * 60 * 60;
                }

                String leaveTypeCode = row.get(fieldIdxMap.get("leave_type_id"));
                WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
                leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId).andLeaveCodeEqualTo(leaveTypeCode);
                List<WaLeaveType> waLeaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
                if (CollectionUtils.isNotEmpty(waLeaveTypeList)) {
                    Short period = null;
                    WaLeaveType waLeaveType = waLeaveTypeList.get(0);

                    String shalfDay = "";
                    String ehalfDay = "";

                    if (fieldIdxMap.containsKey("shalf_day") && fieldIdxMap.containsKey("ehalf_day")) {
                        shalfDay = row.get(fieldIdxMap.get("shalf_day"));
                        ehalfDay = row.get(fieldIdxMap.get("ehalf_day"));
                    }

                    Integer endShm = null;

                    //根据假期时间单位、请假开始时间计算period
                    String ehm = startTimeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
                    if (StringUtils.isNotBlank(ehm) && ehm.indexOf(":") != -1) {
                        String[] ehmArr = ehm.split(":");
                        if (ehmArr != null && ehmArr.length > 0) {
                            endShm = Integer.parseInt(ehmArr[0]) * 60 + Integer.parseInt(ehmArr[1]);
                            if (endShm.intValue() > 0) {
                                period = 3;//小时
                            }
                        }
                    }
                    Integer acctimeType = waLeaveType.getAcctTimeType();
                    if (acctimeType.intValue() == 1) {
                        //天
                        if (period != null && period == 3) {
                            return false;
                        }
                        //根据假期时间单位、请假时间计算period
                        if (StringUtils.isNotBlank(shalfDay) && StringUtils.isNotBlank(ehalfDay)) {
                            period = 9;//半天
                        } else {
                            period = 1;//整天
                        }

                    } else if (acctimeType.intValue() == 2) {
                        //小时
                        if (endShm.intValue() == 0) {
                            period = 4;
                        }
                    }
                    //校验请假数据
                    Map<String, Object> leaveTimeMap = getLeaveTimeStr(period, startTime, endTime, shalfDay, ehalfDay);
                    Result<Boolean> checkResult = mobileV16Service.checkLeave(empInfo, shiftMap, pbMap, leaveTimeMap,
                            waLeaveType, Boolean.TRUE, LeaveApplySaveDto.creatEmptyObj());
                    if (!checkResult.isSuccess()) {
                        return false;
                    }
                    Long leaveTmpDate = startDate;
                    while (leaveTmpDate <= endDate) {
                        // 哺乳假校验
                        String errMsg = mobileV16Service.validateBRJ(waLeaveType, empInfo.getEmpid(), leaveTmpDate);
                        if (!"".equals(errMsg)) {
                            return false;
                        }
                        leaveTmpDate += 24 * 60 * 60;
                    }
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            return false;
        }
        return true;
    }

    /**
     * 导入员工请假数据时校验班次
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpLeaveShift(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            empInfoExample.createCriteria()
                    .andDeletedEqualTo((short) 0)
                    .andBelongOrgIdEqualTo(belongId)
                    .andStatsEqualTo(0)
                    .andWorknoEqualTo(workNo);
            List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
            if (CollectionUtils.isNotEmpty(empInfoList)) {
                SysEmpInfo empInfo = empInfoList.get(0);

                String startTimeStr = getTimeStr(row.get(fieldIdxMap.get("start_time")));
                String endTimeStr = getTimeStr(row.get(fieldIdxMap.get("end_time")));
                if (StringUtils.isBlank(startTimeStr) || StringUtils.isBlank(endTimeStr)) {
                    return false;
                }

                Long startTime = (Long) IocUtil.getDateValue(startTimeStr, "BIGINT");
                Long endTime = (Long) IocUtil.getDateValue(endTimeStr, "BIGINT");

                Long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
                Long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);


                Map<Long, WaWorktimeDetail> pbMap = getEmpPb(belongId, empInfo.getTmType(), empInfo.getEmpid(), startDate, endDate);
                if (pbMap == null || pbMap.size() == 0) {
                    return false;
                } else {
                    long tmpDate = startDate;
                    while (tmpDate <= endDate) {
                        if (pbMap.get(tmpDate) == null) {//没有排班记录
                            return false;
                        }
                        tmpDate = tmpDate + 24 * 60 * 60;
                    }
                }
            }
        }
        return true;
    }

    /**
     * 导入员工请假数据时校验假期时间单位
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpLeaveTimeUnit(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("leave_type_id") != null) {
            String leaveTypeCode = row.get(fieldIdxMap.get("leave_type_id"));

            WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
            leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId).andLeaveCodeEqualTo(leaveTypeCode);
            WaLeaveTypeExample.Criteria criteria = leaveTypeExample.createCriteria();
            criteria.andBelongOrgidEqualTo(belongId).andLeaveNameEqualTo(leaveTypeCode);
            leaveTypeExample.or(criteria);
            List<WaLeaveType> waLeaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
            if (CollectionUtils.isNotEmpty(waLeaveTypeList)) {
                WaLeaveType waLeaveType = waLeaveTypeList.get(0);

                String shalfDay = "";
                String ehalfDay = "";
                Integer acctimeType = waLeaveType.getAcctTimeType();

                if (fieldIdxMap.containsKey("shalf_day") && fieldIdxMap.containsKey("ehalf_day")) {
                    shalfDay = row.get(fieldIdxMap.get("shalf_day"));
                    ehalfDay = row.get(fieldIdxMap.get("ehalf_day"));
                }

                String startTimeStr = getTimeStr(row.get(fieldIdxMap.get("start_time")));
                if (StringUtils.isBlank(startTimeStr)) {
                    return false;
                }
                //获取假期时间类型
                Short period = getLeavePeriod(startTimeStr, acctimeType, shalfDay, ehalfDay);
                if (period == null) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 校验时间
     *
     * @param row
     * @param fieldIdxMap
     * @return
     */
    public boolean checkLeaveDate(List<String> row, Map<String, Integer> fieldIdxMap) {
        String start_time = row.get(fieldIdxMap.get("start_time"));
        String end_time = row.get(fieldIdxMap.get("end_time"));
        String regular = "^((\\d{2}(([02468][048])|([13579][26]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|([1-2][0-9])))))|(\\d{2}(([02468][1235679])|([13579][01345789]))[-|/]?((((0?[13578])|(1[02]))[-|/]?((0?[1-9])|([1-2][0-9])|(3[01])))|(((0?[469])|(11))[-|/]?((0?[1-9])|([1-2][0-9])|(30)))|(0?2[-|/]?((0?[1-9])|(1[0-9])|(2[0-8]))))))(\\s(((0?[0-9])|([1][0-9])|([2][0-4]))\\:([0-5]?[0-9])((\\s)|(\\:([0-5]?[0-9])))))?$";
        Pattern r = Pattern.compile(regular);
        Matcher a = r.matcher(start_time);
        Matcher b = r.matcher(end_time);
        if (!a.matches() || !b.matches()) {
            return false;
        }
        return true;
    }

    /**
     * 导入员工请假数据时校验时间
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpLeaveTime(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") != null && row.get(fieldIdxMap.get("start_time")) != null && row.get(fieldIdxMap.get("end_time")) != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            String leaveTypeCode = row.get(fieldIdxMap.get("leave_type_id"));
            String startTimeStr = getTimeStr(row.get(fieldIdxMap.get("start_time")));
            String endTimeStr = getTimeStr(row.get(fieldIdxMap.get("end_time")));
            Long startTime = (Long) IocUtil.getDateValue(startTimeStr, "DATE");
            Long endTime = (Long) IocUtil.getDateValue(endTimeStr, "DATE");

            String startTimeStr1 = DateUtil.getTimeStrByTimesamp4(startTime);
            String endTimeStr1 = DateUtil.getTimeStrByTimesamp4(endTime);

            Long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
            Long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);

            String shalfDay = "";
            String ehalfDay = "";
            if (fieldIdxMap.containsKey("shalf_day") && fieldIdxMap.containsKey("ehalf_day")) {
                shalfDay = row.get(fieldIdxMap.get("shalf_day"));
                ehalfDay = row.get(fieldIdxMap.get("ehalf_day"));
                for (String key : BaseConst.LEAVE_HALF_MAPS.keySet()) {
                    String value = BaseConst.LEAVE_HALF_MAPS.get(key);
                    if (value.equals(shalfDay)) {
                        shalfDay = key;
                    }
                    if (value.equals(ehalfDay)) {
                        ehalfDay = key;
                    }
                }
            }
            //如果接入数据状态是作废状态的话，检查数据库中是否存在该单据，如果存在不继续执行时间重叠的校验
            if (fieldIdxMap.containsKey("time_slot") && row.get(fieldIdxMap.get("time_slot")) != null) {
                String timeSlot = row.get(fieldIdxMap.get("time_slot"));
                if (fieldIdxMap.get("status") != null && row.get(fieldIdxMap.get("status")) != null) {
                    Short status = Short.valueOf(row.get(fieldIdxMap.get("status")));
                    if (status.intValue() == 4) {
                        Map<String, Object> leaveParams = new HashMap<>();
                        leaveParams.put("belongId", belongId);
                        leaveParams.put("workno", workNo);
                        leaveParams.put("leaveCode", leaveTypeCode);
                        leaveParams.put("timeSlot", timeSlot);
                        List<Integer> listLeaveId = waMapper.getWaEmpLeaveIdList(leaveParams);
                        if (CollectionUtils.isNotEmpty(listLeaveId)) {
                            return true;
                        }
                    }
                }
            }
            WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
            leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId).andLeaveCodeEqualTo(leaveTypeCode);
            WaLeaveTypeExample.Criteria criteria = leaveTypeExample.createCriteria();
            criteria.andBelongOrgidEqualTo(belongId).andLeaveNameEqualTo(leaveTypeCode);
            leaveTypeExample.or(criteria);
            List<WaLeaveType> waLeaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
            if (CollectionUtils.isNotEmpty(waLeaveTypeList)) {
                WaLeaveType waLeaveType = waLeaveTypeList.get(0);
                //获取假期时间类型
                Short period = getLeavePeriod(startTimeStr1, waLeaveType.getAcctTimeType(), shalfDay, ehalfDay);
                if (period == null) {
                    return false;
                }
                //校验请假数据
                Map<String, Object> leaveTimeMap = getLeaveTimeStr(period, startTime, endTime, shalfDay, ehalfDay);

                SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
                empInfoExample.createCriteria().andBelongOrgIdEqualTo(belongId)
                        .andDeletedEqualTo((short) 0)
                        .andWorknoEqualTo(workNo);
                List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
                if (CollectionUtils.isNotEmpty(empInfoList)) {
                    SysEmpInfo empInfo = empInfoList.get(0);
                    Map<Integer, WaShiftDef> shiftMap = waCommonService.getCorpAllShiftDef(belongId);
                    Map<Long, WaWorktimeDetail> pbMap = getEmpPb(belongId, empInfo.getTmType(), empInfo.getEmpid(), startDate, endDate);
                    if (pbMap == null || null == shiftMap) {
                        return false;
                    }
                    pbMap.forEach((date, worktimeDetail) -> {
                        WaShiftDef shiftDef = shiftMap.get(worktimeDetail.getShiftDefId());
                        if (DateTypeEnum.DATE_TYP_4.getIndex().equals(worktimeDetail.getDateType())) {
                            worktimeDetail.setDateType(shiftDef.getDateType());
                        }
                    });
                    Result<Boolean> checkResult = mobileV16Service.checkLeave(empInfo, shiftMap, pbMap, leaveTimeMap,
                            waLeaveType, Boolean.TRUE, LeaveApplySaveDto.creatEmptyObj());
                    if (!checkResult.isSuccess()) {
                        log.info("ImportCheckService.checkEmpLeaveTime checkResult={}", checkResult.getMsg());
                        return false;
                    }
                } else {
                    log.info("empInfoList is empty workNo={}", workNo);
                }
            }
        }
        return true;
    }

    /**
     * 校验假期配额
     *
     * @param row
     * @param fieldIdxMap
     * @param corpId
     * @param belongId
     * @return
     * @throws Exception
     */
    public boolean checkEmpQuota(List<String> row, Map<String, Integer> fieldIdxMap, Long corpId, String belongId) throws Exception {
        String workNo = row.get(fieldIdxMap.get("empid"));
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        empInfoExample.createCriteria().andBelongOrgIdEqualTo(belongId)
                .andWorknoEqualTo(workNo)
                .andDeletedEqualTo((short) 0);
        List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
        if (CollectionUtils.isNotEmpty(empInfoList)) {
            SysEmpInfo empInfo = empInfoList.get(0);
            Integer period = null;
            WaLeaveType waLeaveType = null;
            String leaveTypeCode = row.get(fieldIdxMap.get("leave_type_id"));

            WaLeaveTypeExample leaveTypeExample = new WaLeaveTypeExample();
            leaveTypeExample.createCriteria().andBelongOrgidEqualTo(belongId).andLeaveCodeEqualTo(leaveTypeCode);
            List<WaLeaveType> waLeaveTypeList = waLeaveTypeMapper.selectByExample(leaveTypeExample);
            if (CollectionUtils.isNotEmpty(waLeaveTypeList)) {
                waLeaveType = waLeaveTypeList.get(0);

                String shalfDay = "";
                String ehalfDay = "";
                Integer acctimeType = waLeaveType.getAcctTimeType();

                if (fieldIdxMap.containsKey("shalf_day") && fieldIdxMap.containsKey("ehalf_day")) {
                    shalfDay = row.get(fieldIdxMap.get("shalf_day"));
                    ehalfDay = row.get(fieldIdxMap.get("ehalf_day"));
                }

                String startTimeStr = getTimeStr(row.get(fieldIdxMap.get("start_time")));
                if (StringUtils.isBlank(startTimeStr)) {
                    return false;
                }
                //获取假期时间类型
                Short periodType = getLeavePeriod(startTimeStr, acctimeType, shalfDay, ehalfDay);
                if (periodType == null) {
                    return false;
                }
                period = periodType.intValue();
            }
            String startTimeStr = getTimeStr(row.get(fieldIdxMap.get("start_time")));
            String endTimeStr = getTimeStr(row.get(fieldIdxMap.get("end_time")));
            Long startDate = DateUtil.getTimesampByDateStr2(startTimeStr);
            Long endDate = DateUtil.getTimesampByDateStr2(endTimeStr);

            Map<Long, WaWorktimeDetail> pbMap = getEmpPb(belongId, empInfo.getTmType(), empInfo.getEmpid(), startDate, endDate);
            if (pbMap == null) {
                return false;
            }

            //查询公司全部班次
            Map<Integer, WaShiftDef> shiftMap = new HashMap<>();
            WaShiftDefExample shiftDefExample = new WaShiftDefExample();
            shiftDefExample.createCriteria().andBelongOrgidEqualTo(belongId);
            List<WaShiftDef> allShiftDefList = waShiftDefMapper.selectByExample(shiftDefExample);
            if (CollectionUtils.isNotEmpty(allShiftDefList)) {
                for (WaShiftDef waShiftDef : allShiftDefList) {
                    shiftMap.put(waShiftDef.getShiftDefId(), waShiftDef);
                }
            }

            //CLOUD-8200 & 8414 查询公司默认工作日历
            String isOpenStoreempUseDefaultcalendar = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongId + RedisKeyDefine.IS_OPEN_STOREEMP_VACATION_USE_DEFAULTCALENDAR);
            Map<Long, Integer> defaultWorkCalendar = mobileV16Service.getDefaultWorkCalendar(belongId);

            //计算请假总额
            BigDecimal totalTimeDuration = new BigDecimal(0);
            boolean flag = true;
            List<WaLeaveDaytime> daytimeList = new ArrayList<>();
            while (flag) {//查看每天的日期
                WaWorktimeDetail startDateDetail = pbMap.get(startDate);//取得当天排班

                if ("1".equals(isOpenStoreempUseDefaultcalendar) && Integer.valueOf(2).equals(empInfo.getTmType()) && Integer.valueOf(3).equals(defaultWorkCalendar.get(startDate))) {
                    startDateDetail.setDateType(defaultWorkCalendar.get(startDate));
                }

                WaLeaveDaytime dayTime = new WaLeaveDaytime();
                dayTime.setLeaveDate(startDate);
                dayTime.setPeriodType(period.shortValue());
                if (period == 1 || period == 9) {//天
                    dayTime.setTimeUnit((short) 1);
                } else if (period == 4 || period == 3) {//小时
                    dayTime.setTimeUnit((short) 2);
                }
                dayTime.setDateType(startDateDetail.getDateType());
//                Integer tmpS = 0, tmpE = 0;
                if (startDateDetail.getDateType() == 2 && (!waLeaveType.getIsRestDay())) {//日期类型为休息日，且休息日不连续计算
                    dayTime.setTimeDuration(0f);
                } else if ((startDateDetail.getDateType() == 3 || startDateDetail.getDateType() == 5) && (!waLeaveType.getIsLegalHoliday())) {//日期类型为法定假日，且法定假日不连续计算
                    dayTime.setTimeDuration(0f);
                } else {
                    if (period == 1) {
                        dayTime.setTimeDuration(1f);
                    } else if (period == 3) {//3时间单位为小时的非整天。
                        mobileV16Service.processLeaveByPeriod3(waLeaveType, startDate, endDate, startTimeStr,
                                endTimeStr, startDateDetail, dayTime, shiftMap.get(startDateDetail.getShiftDefId()));

                        if (waLeaveType.getIsAdjustWorkHour() != null && waLeaveType.getIsAdjustWorkHour() && shiftMap.get(startDateDetail.getShiftDefId()) != null) {
                            //工时调整
                            Float timeDuration = mobileV16Service.getAdjustWorkTime(dayTime.getStartTime(), dayTime.getEndTime(), shiftMap.get(startDateDetail.getShiftDefId()));
                            if (timeDuration != null) {
                                dayTime.setTimeDuration(timeDuration);
                            }
                        }
                    } else if (period == 4) {//时间单位为小时的整天
                        //要看具体工作时间
                        WaShiftDef shiftDef = shiftMap.get(startDateDetail.getShiftDefId());
                        mobileV16Service.processLeaveByPeriod4(waLeaveType, startDateDetail, shiftDef, dayTime);
                    } else if (period == 9) {//上半天，下半天，时间单位为天的非整天
//                        mobileV16Service.processLeaveByPeriod9(waLeaveType, startDateDetail, dayTime, map.get("shalfday").toString(), map.get("ehalfday").toString(), startDate, DateUtil.getTimesampByDateStr2(startTimeStr), DateUtil.getTimesampByDateStr2(endTimeStr));
                    }
                }
                if (startDate.equals(endDate)) {
                    flag = false;
                } else {
                    startDate = startDate + 24 * 60 * 60;
                }
                totalTimeDuration = totalTimeDuration.add(new BigDecimal(dayTime.getTimeDuration()));
                daytimeList.add(dayTime);
            }

            List<Integer> leaveYearList = new ArrayList<>();
            Integer startLeaveYear = DateUtilExt.getTimeYear(startDate);
            leaveYearList.add(startLeaveYear);
            Integer endLeaveYear = DateUtilExt.getTimeYear(endDate);
            if (!startLeaveYear.equals(endLeaveYear)) {
                leaveYearList.add(endLeaveYear);
            }
            String s2 = mobileV16Service.checkEmpLeaveQuotaDetail(daytimeList, empInfo.getEmpid(), waLeaveType, null, "zh_CN", leaveYearList);
            if (!s2.equals("")) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取员工排班
     *
     * @param belongId
     * @param tmType
     * @param empId
     * @param startDate
     * @param endDate
     * @return
     */
    public Map getEmpPb(String belongId, Integer tmType, Long empId, Long startDate, Long endDate) {
        Map<Long, WaWorktimeDetail> pbMap = null;
        if (tmType == null || tmType == 0) {
            tmType = 1;
        }
        return waCommonService.getEmpWaWorktimeDetail(belongId, empId, tmType, startDate, endDate, null, true);
    }


    /**
     * 获取时间字符串
     *
     * @param time
     * @return
     */
    public String getTimeStr(Object time) {
        String timeStr = "";
        if (time != null) {
            if (time instanceof String) {
                timeStr = (String) time;

                Pattern pattern = Pattern.compile("[0-9]*");
                if (StringUtils.isNotBlank(timeStr)) {
                    Matcher isNum = pattern.matcher(timeStr);
                    if (isNum.matches()) {
                        timeStr = DateUtil.getTimeStrByTimesamp(Long.valueOf(timeStr));
                    }
                }
            } else if (time instanceof Long) {
                timeStr = DateUtil.getTimeStrByTimesamp((Long) time);
            }
        }
        return timeStr;
    }

    /**
     * 获取请假类型时间单位
     *
     * @param timeStr
     * @param acctimeType
     * @param shalfDay
     * @param ehalfDay
     * @return
     */
    public Short getLeavePeriod(String timeStr, Integer acctimeType, String shalfDay, String ehalfDay) {
        Short period = null;
        Integer endShm = null;

        //根据假期时间单位、请假开始时间计算period
//      String ehm = timeStr.substring(10).trim();//如2015-09-16 09:15  会变成09:15
        String[] arr = timeStr.split(" ");
        if (arr.length > 1) {
            String ehm = arr[1];
            if (StringUtils.isNotBlank(ehm) && ehm.indexOf(":") != -1) {
                String[] ehmArr = ehm.split(":");
                if (ehmArr != null && ehmArr.length > 0) {
                    endShm = Integer.parseInt(ehmArr[0]) * 60 + Integer.parseInt(ehmArr[1]);
                    if (endShm.intValue() > 0) {
                        period = 3;//小时
                    }
                }
            }
        }
        if (acctimeType.intValue() == 1) {
            //天
            if (period != null && period == 3) {
                return null;
            }
            //根据假期时间单位、请假时间计算period
            if (StringUtils.isNotBlank(shalfDay) && StringUtils.isNotBlank(ehalfDay)) {
                period = 9;//半天
            } else {
                period = 1;//整天
            }
        } else if (acctimeType.intValue() == 2) {
            //小时
            if (endShm == null || endShm.intValue() == 0) {
                period = 4;
            }
        }
        return period;
    }

    /**
     * 获取请假时间字符串
     *
     * @param period
     * @param startTime
     * @param endTime
     * @param shalfDay
     * @param ehalfDay
     * @return
     */
    public Map<String, Object> getLeaveTimeStr(Short period, Long startTime, Long endTime, String shalfDay, String ehalfDay) {
        Map<String, Object> map = new HashMap();
        if (period == 1) {
            map.put("period", 1);
            map.put("starttime", DateUtil.getDateStrByTimesamp(startTime));
            map.put("endtime", DateUtil.getDateStrByTimesamp(endTime));
        } else if (period == 9) {
            map.put("period", 9);
            map.put("starttime", DateUtil.getDateStrByTimesamp(startTime));
            map.put("endtime", DateUtil.getDateStrByTimesamp(endTime));
            map.put("shalfday", shalfDay);
            map.put("ehalfday", ehalfDay);
        } else if (period == 3) {
            map.put("period", 3);
            map.put("starttime", DateUtil.getTimeStrByTimesamp4(startTime));
            map.put("endtime", DateUtil.getTimeStrByTimesamp4(endTime));
        } else if (period == 4) {
            map.put("period", 4);
            map.put("starttime", DateUtil.getTimeStrByTimesamp4(startTime));
            map.put("endtime", DateUtil.getTimeStrByTimesamp4(endTime));
        }
        return map;
    }

    /**
     * 导入员工加班数据时校验时间
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpOverTime(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") == null || row.get(fieldIdxMap.get("start_time")) == null
                || row.get(fieldIdxMap.get("end_time")) == null) {
            return true;
        }

        String workNo = row.get(fieldIdxMap.get("empid"));
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        empInfoExample.createCriteria().andDeletedEqualTo((short) 0)
                .andBelongOrgIdEqualTo(belongId)
                .andWorknoEqualTo(workNo);
        List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
        if (CollectionUtils.isEmpty(empInfoList)) {
            return true;
        }
        SysEmpInfo empInfo = empInfoList.get(0);
        int tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
        if (tmType == 0) {
            tmType = 1;
        }
        empInfo.setTmType(tmType);

        long startTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("start_time"))), "DATE");
        long endTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("end_time"))), "DATE");
        long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
        long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);
        long preDate = startDate - 1440L * 60;

        // 查询排班
        Map<Integer, WaShiftDef> shiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
        Map<Long, WaWorktimeDetail> pbDetailMap = waCommonService.getEmpWaWorktimeDetail(belongId, empInfo.getEmpid(), tmType, preDate, endDate);
        if (MapUtils.isEmpty(pbDetailMap) || MapUtils.isEmpty(shiftDefMap)) {
            return true;
        }
        WaWorktimeDetail startWorkTimeDetail = pbDetailMap.get(startDate);
        WaWorktimeDetail secondDayWorkTimeDetail = pbDetailMap.get(endDate);
        if (null == startWorkTimeDetail || null == secondDayWorkTimeDetail) {
            return true;
        }

        // 检查加班时间是否归属于前一天（在前一天班次的班次范围内）
        boolean isSecondDay = startDate < endDate;
        WaWorktimeDetail preWorkTimeDetail = pbDetailMap.get(preDate);
        boolean belongPreDay = isSecondDay ? Boolean.FALSE
                : overtimeApplyService.checkIfBelongPreDay(shiftDefMap, preWorkTimeDetail, startTime);
        if (belongPreDay) {
            // 检查加班时间是否包含上班时间
            if (DateTypeEnum.DATE_TYP_1.getIndex().equals(preWorkTimeDetail.getDateType()) && CdWaShiftUtil.checkCrossNightV3(preWorkTimeDetail, shiftDefMap)) {
                return overtimeApplyService.checkIfNotInWorkTime(shiftDefMap, preWorkTimeDetail, startTime, endTime);
            }
        } else {
            // 检查加班时间是否包含上班时间
            boolean notInWorkTime = overtimeApplyService.checkIfNotInWorkTime(shiftDefMap, startWorkTimeDetail, startTime, endTime);
            if (!notInWorkTime) {
                return false;
            }
            if (isSecondDay) {
                notInWorkTime = overtimeApplyService.checkIfNotInWorkTime(shiftDefMap, secondDayWorkTimeDetail, startTime, endTime);
                if (!notInWorkTime) {
                    return false;
                }
            }
        }
        return true;
    }

    /**
     * 导入员工加班校验重叠
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpOverTimeDate(List<String> row, Map<String, Integer> fieldIdxMap, String belongId, List<List<String>> rows) {
        log.info("rows:{}", JSONUtils.ObjectToJson(rows));
        if (rows != null && !rows.isEmpty()) {
            for (int i = 0; i < rows.size(); i++) {
                List<String> rowCell = rows.get(i);
                String workNo = rowCell.get(fieldIdxMap.get("empid"));
                long startTime = (Long) IocUtil.getDateValue(getTimeStr(rowCell.get(fieldIdxMap.get("start_time"))), "DATE");
                long endTime = (Long) IocUtil.getDateValue(getTimeStr(rowCell.get(fieldIdxMap.get("end_time"))), "DATE");
                for (int j = 0; j < rows.size(); j++) {
                    if (i != j) {
                        List<String> oneRow = rows.get(j);
                        String workNo2 = oneRow.get(fieldIdxMap.get("empid"));
                        long startTime2 = (Long) IocUtil.getDateValue(getTimeStr(oneRow.get(fieldIdxMap.get("start_time"))), "DATE");
                        long endTime2 = (Long) IocUtil.getDateValue(getTimeStr(oneRow.get(fieldIdxMap.get("end_time"))), "DATE");
                        if (workNo.equals(workNo2)
                                && ((startTime2 >= startTime && startTime2 < endTime)
                                || (endTime2 > startTime && endTime2 <= endTime)
                                || (startTime2 <= startTime && endTime2 >= endTime))) {
                            return false;
                        }
                    }
                }
            }
        }
        if (fieldIdxMap.get("empid") != null && row.get(fieldIdxMap.get("start_time")) != null
                && row.get(fieldIdxMap.get("end_time")) != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            long startTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("start_time"))), "DATE");
            long endTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("end_time"))), "DATE");
            Map<String, Object> params = new HashMap<>();
            params.put("workno", workNo);
            params.put("start", startTime);
            params.put("end", endTime);
            params.put("belongOrgId", belongId);
            Integer total = waMapper.checkOtRepeat(params);
            return !(total > 0);
        }
        return true;
    }

    /**
     * 导入员工加班数据时校验日历
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpOverTimeWorktime(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") != null && row.get(fieldIdxMap.get("start_time")) != null
                && row.get(fieldIdxMap.get("end_time")) != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            empInfoExample.createCriteria()
                    .andBelongOrgIdEqualTo(belongId)
                    .andWorknoEqualTo(workNo)
                    .andDeletedEqualTo((short) 0);
            List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
            if (CollectionUtils.isNotEmpty(empInfoList)) {
                long endTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("end_time"))), "DATE");
                long startTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("start_time"))), "DATE");
                long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);
                SysEmpInfo empInfo = empInfoList.get(0);
                long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
                Map<Long, WaWorktimeDetail> pbMap = waCommonService.getEmpWaWorktimeDetail(belongId, empInfo.getEmpid(), empInfo.getTmType(), startDate, endDate);
                return !(MapUtils.isEmpty(pbMap) || pbMap.get(startDate) == null || pbMap.get(endDate) == null);
            }
        }
        return true;
    }

    /**
     * 导入员工排班（日历）自动定界校验
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpShiftConflicts(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            List<SysEmpInfo> empInfoList = this.getEmpInfoList(workNo, belongId);
            if (CollectionUtils.isEmpty(empInfoList)) {
                return false;
            }
            Long empId = empInfoList.get(0).getEmpid();
            // 查询即将导入员工的涉及到的所有排班
            WaEmpShiftExample example = new WaEmpShiftExample();
            example.createCriteria().andBelongOrgidEqualTo(belongId).andEmpidEqualTo(empId);
            List<WaEmpShift> empShiftList = waEmpShiftMapper.selectByExample(example);
            if (CollectionUtils.isNotEmpty(empShiftList)) {
                Optional<WaEmpShift> optional = empShiftList.stream().max(Comparator.comparing(WaEmpShift::getStartTime));
                if (!optional.isPresent()) {
                    return true;
                }
                // 数据库已有员工排班（日历）
                WaEmpShift waEmpShift = optional.get();
                Long dbStartTime = waEmpShift.getStartTime();
                Long dbEndTime = waEmpShift.getEndTime();
                Optional<WaWorktime> waWorkTimeOpt = Optional.ofNullable(waWorktimeMapper.selectByPrimaryKey(waEmpShift.getWorkCalendarId()));
                if (!waWorkTimeOpt.isPresent()) {
                    return false;
                }
                // 导入员工排班（日历）
                Long endTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("end_time")), "DATE") + 86399;
                Long startTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("start_time")), "DATE");
                String workCalendarName = row.get(fieldIdxMap.get("work_calendar_id"));
                if (waWorkTimeOpt.get().getWorkCalendarName().equals(workCalendarName)) {
                    // 工作日历相同
                    return startTime.equals(dbStartTime) || (startTime > dbStartTime && startTime <= dbEndTime && endTime >= dbEndTime) || (startTime > dbEndTime && endTime > dbEndTime);
                } else {
                    // 工作日历不同
                    return (startTime > dbStartTime && startTime <= dbEndTime && endTime >= dbEndTime) || (startTime > dbEndTime && endTime > dbEndTime);
                }
            }
        }
        return true;
    }

    private Long getDateFromStringToLong(String date, String dataType) {
        return (Long) IocUtil.getDateValue(getTimeStr(date), dataType);
    }

    /**
     * 员工考勤方案自动定界校验
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpGroupRule(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        if (fieldIdxMap.get("empid") != null && fieldIdxMap.get("wa_group_id") != null && row.get(fieldIdxMap.get("start_time")) != null
                && row.get(fieldIdxMap.get("end_time")) != null) {
            String workNo = row.get(fieldIdxMap.get("empid"));
            log.info("workNo========={}", workNo);
            String waGroupName = row.get(fieldIdxMap.get("wa_group_id"));
            Long startTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("start_time"))), "DATE");
            Long endTime = (Long) IocUtil.getDateValue(getTimeStr(row.get(fieldIdxMap.get("end_time"))), "DATE");
            Long startDate = DateUtil.getDateLong(startTime * 1000, "yyyy-MM-dd", true);
            Long endDate = DateUtil.getDateLong(endTime * 1000, "yyyy-MM-dd", true);
            //查员工
            SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
            empInfoExample.createCriteria().andDeletedEqualTo((short) 0)
                    .andBelongOrgIdEqualTo(belongId).andWorknoEqualTo(workNo);
            List<SysEmpInfo> empInfoList = sysEmpInfoMapper.selectByExample(empInfoExample);
            if (CollectionUtils.isNotEmpty(empInfoList)) {
                SysEmpInfo empInfo = empInfoList.get(0);
                WaEmpGroupExample example = new WaEmpGroupExample();
                example.createCriteria().andEmpidEqualTo(empInfo.getEmpid());
                List<WaEmpGroup> waEmpGroups = waEmpGroupMapper.selectByExample(example);
                Optional<WaEmpGroup> optional = waEmpGroups.stream().max(Comparator.comparing(WaEmpGroup::getStartTime));
                if (!optional.isPresent()) {
                    return true;
                }
                WaEmpGroup empGroup = optional.get();
                WaGroup waGroup = waGroupMapper.selectByPrimaryKey(empGroup.getWaGroupId());
                long start = empGroup.getStartTime();
                long end = empGroup.getEndTime();
                if (waGroup != null) {
                    if (waGroupName.equalsIgnoreCase(waGroup.getWaGroupName())) {
                        return startDate.equals(start) || (startDate > start && startDate <= end && endDate >= end) || (startDate > end && endDate > end);
                    } else {
                        return (startDate > start && startDate <= end && endDate >= end) || (startDate > end && endDate > end);
                    }
                }
            }

        }
        return true;
    }

    private List<SysEmpInfo> getEmpInfoList(String workNo, String belongId) {
        SysEmpInfoExample empInfoExample = new SysEmpInfoExample();
        empInfoExample.createCriteria().andDeletedEqualTo((short) 0).andWorknoEqualTo(workNo).andBelongOrgIdEqualTo(belongId);
        return sysEmpInfoMapper.selectByExample(empInfoExample);
    }

    /**
     * 导入员工打卡方案自动定界校验
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpClockPlanConflicts(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        log.info("row:[{}],fieldIdxMap:[{}]", JSON.toJSONString(row), JSON.toJSONString(fieldIdxMap));
        if (fieldIdxMap.get("emp_id") != null) {
            String workNo = row.get(fieldIdxMap.get("emp_id"));
            List<SysEmpInfo> empInfoList = this.getEmpInfoList(workNo, belongId);
            if (CollectionUtils.isEmpty(empInfoList)) {
                return false;
            }
            Long empId = empInfoList.get(0).getEmpid();
            // 查询即将导入员工的涉及到的所有排班
            List<PlanEmpRel> empShiftList = waPlanEmpRelMapper.queryByEmpIds(Arrays.asList(empId), belongId);
            if (CollectionUtils.isNotEmpty(empShiftList)) {
                Optional<PlanEmpRel> optional = empShiftList.stream().max(Comparator.comparing(PlanEmpRel::getStartTime));
                if (!optional.isPresent()) {
                    return false;
                }
                // 数据库已有员工打卡方案
                PlanEmpRel waPlanEmpRel = optional.get();
                Long dbStartTime = waPlanEmpRel.getStartTime();
                Long dbEndTime = waPlanEmpRel.getEndTime();
                Optional<ClockPlan> waClockPlanOpt = Optional.ofNullable(waClockPlanMapper.selectByPrimaryKey(waPlanEmpRel.getPlanId()));
                if (!waClockPlanOpt.isPresent()) {
                    return false;
                }
                // 导入员工打卡方案
                Long startTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("start_time")), "DATE");
                Long endTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("end_time")), "DATE") + 86399;
                String planName = row.get(fieldIdxMap.get("plan_id"));
                if (waClockPlanOpt.get().getPlanName().equals(planName)) {
                    // 员工打卡方案相同
                    return startTime.equals(dbStartTime) || (startTime > dbStartTime && startTime <= dbEndTime && endTime >= dbEndTime) || (startTime > dbEndTime && endTime > dbEndTime);
                } else {
                    // 员工打卡方案不同
                    return (startTime > dbStartTime && startTime <= dbEndTime && endTime >= dbEndTime) || (startTime > dbEndTime && endTime > dbEndTime);
                }
            }
        }
        return true;
    }

    /**
     * 校验有效期是否合法
     *
     * @param row
     * @param fieldIdxMap
     * @return
     */
    public boolean checkValidity(List<String> row, Map<String, Integer> fieldIdxMap) {
        if (fieldIdxMap.get("start_time") != null && fieldIdxMap.get("end_time") != null) {
            Long startTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("start_time")), "DATE");
            Long endTime = this.getDateFromStringToLong(row.get(fieldIdxMap.get("end_time")), "DATE");
            return startTime < endTime;
        }
        return true;
    }

    /**
     * 导入员工休假校验工作流是否开启
     * 如导入审批状态为审批中的则校验是否开启工作流校验
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpLeaveWorkflow(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
        try {
            if (row.get(fieldIdxMap.get("status")) != null) {
                String statusName = row.get(fieldIdxMap.get("status"));
                if (statusName.equals("")) {
                    return false;
                }
                Integer status = ApprovalStatusEnum.getIndexByName(statusName);
                if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
                    Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.LEAVE.getCode());
                    if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                        return false;
                    }
                    return (Boolean) checkWorkflowEnableResult.getData();
                }
            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return true;
    }

    /**
     * 导入员工加班校验工作流是否开启
     * 如导入审批状态为审批中的则校验是否开启工作流校验
     *
     * @param row
     * @param fieldIdxMap
     * @param belongId
     * @return
     */
    public boolean checkEmpOverTimeWorkflow(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        SecurityUserInfo securityUserInfo = SecurityUserUtil.getSecurityUserInfo();
        SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
        try {
            if (row.get(fieldIdxMap.get("status")) != null) {
                String statusName = row.get(fieldIdxMap.get("status"));
                if (statusName.equals("")) {
                    return false;
                }
                Integer status = ApprovalStatusEnum.getIndexByName(statusName);
                if (ApprovalStatusEnum.IN_APPROVAL.getIndex().equals(status)) {
                    Result<?> checkWorkflowEnableResult = wfService.checkWorkflowEnabled(BusinessCodeEnum.OVERTIME.getCode());
                    if (null == checkWorkflowEnableResult || !checkWorkflowEnableResult.isSuccess()) {
                        return false;
                    }
                    return (Boolean) checkWorkflowEnableResult.getData();
                }
            }
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return true;
    }

    public boolean checkAnnualLeaveRepeat(List<String> row, Map<String, Integer> fieldIdxMap, String belongId) {
        /*if (row.get(fieldIdxMap.get("empid")) != null || row.get(fieldIdxMap.get("leave_type_id")) != null
                || row.get(fieldIdxMap.get("config_id")) != null || row.get(fieldIdxMap.get("period_year")) != null) {
            Integer leaveTypeId = ConvertHelper.intConvert(row.get(fieldIdxMap.get("leave_type_id")));
            Long empId = ConvertHelper.longConvert(row.get(fieldIdxMap.get("empid")));
            Long configId = ConvertHelper.longConvert(row.get(fieldIdxMap.get("config_id")));
            Integer periodYear = ConvertHelper.intConvert(row.get(fieldIdxMap.get("period_year")));
            WaLeaveType leaveType = waLeaveTypeMapper.selectByPrimaryKey(leaveTypeId);
            if (!QuotaTypeEnum.ISSUED_ANNUALLY.getIndex().equals(leaveType.getQuotaType())) {
                return true;
            }
            List<LeaveQuotaDo> quotas = quotaMapper.getNotCarryAnnualLeaveList(belongId, null, empId, leaveTypeId, configId, leaveType.getQuotaType(), periodYear);
            return CollectionUtils.isEmpty(quotas);
        }*/
        return true;
    }
}