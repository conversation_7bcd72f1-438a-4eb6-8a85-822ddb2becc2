package com.caidaocloud.attendance.core.wa.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 请假每日明细扩展DTO
 *
 * <AUTHOR>
 * @Date 2022/12/16
 */
@Data
@Accessors(chain = true)
public class WaLeaveDaytimeExtDto {
    /**
     * 请假开始时间
     */
    private Long leaveStartTime;
    /**
     * 请假结束时间
     */
    private Long leaveEndTime;

    /**
     * 是否为销假
     */
    private boolean xj;

    /**
     * 创建时间
     */
    private Long createTime;

    /**
     * 员工ID
     */
    private Long empId;
}
