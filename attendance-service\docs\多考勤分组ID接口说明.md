# 支持多个考勤分组ID的动态列接口

## 概述

基于现有的动态列方法，新增了支持多个考勤分组ID的接口，允许用户在查询考勤统计数据时传入多个考勤分组ID。

## 新增接口

### 1. 获取考勤明细汇总表——动态列（支持多个考勤分组ID）

**接口地址：** `POST /searchRegisterStatisticsAdvance/dynamicMultiGroup`

**请求参数：** `MonthAnalysePageDtoV2`

```json
{
  "timePeriod": "THIS_MONTH",
  "startDate": 1672531200,
  "endDate": 1675123199,
  "groupId": [1, 2, 3],  // 支持多个考勤分组ID
  "pageNo": 1,
  "pageSize": 20,
  "empIds": [100, 200, 300],
  "filterList": [],
  "statisticsType": 0
}
```

**响应数据：** `DynamicPageDto`

### 2. 获取考勤统计月度汇总字段-动态列（支持多个考勤分组ID）

**接口地址：** `GET /searchMonthHeaderList/dynamicMultiGroup`

**请求参数：**
- `waGroupIds`: 考勤分组ID列表，例如：`[1,2,3]`

**响应数据：** `List<KeyValue>`

## 实现说明

### 数据传输对象

使用 `MonthAnalysePageDtoV2` 作为请求参数，其中 `groupId` 字段类型为 `List<Integer>`，支持传入多个考勤分组ID。

### 服务层实现

1. **IStatisticsService 接口新增方法：**
   - `getRegisterStatisticsAdvancedForDynamicMultiGroup()` - 支持多个考勤分组ID的动态列数据查询
   - `searchMonthHeaderListForDynamicMultiGroup()` - 支持多个考勤分组ID的表头查询
   - `getMonthHeadersByDynamicForExportMultiGroup()` - 支持多个考勤分组ID的导出表头

2. **StatisticsService 实现类：**
   - `searchRegisterStatisticsMultiGroup()` - 支持多个考勤分组ID的底层查询方法
   - 当前实现暂时使用第一个groupId，后续需要在底层数据访问层支持多个groupId

### 控制器层

在 `StatisticsController` 中新增两个接口方法，提供对外的REST API。

## 使用示例

### 查询多个考勤分组的考勤明细

```javascript
// 请求示例
const requestData = {
  timePeriod: "THIS_MONTH",
  groupId: [1, 2, 3], // 查询考勤分组1、2、3的数据
  pageNo: 1,
  pageSize: 20
};

fetch('/searchRegisterStatisticsAdvance/dynamicMultiGroup', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify(requestData)
})
.then(response => response.json())
.then(data => {
  console.log('考勤明细数据:', data);
});
```

### 获取多个考勤分组的表头信息

```javascript
// 请求示例
fetch('/searchMonthHeaderList/dynamicMultiGroup?waGroupIds=1,2,3')
.then(response => response.json())
.then(data => {
  console.log('表头信息:', data);
});
```

## 注意事项

1. **当前限制：** 由于底层数据访问层暂未完全支持多个groupId，当前实现使用第一个groupId进行查询。
2. **后续优化：** 需要在 `WorkOvertimeService.searchRegMonthStatistics` 方法中增加对多个groupId的支持。
3. **兼容性：** 新接口与现有单个groupId接口并存，不影响现有功能。

## 后续开发计划

1. 在 `WorkOvertimeService` 中创建 `searchRegMonthStatisticsMultiGroup` 方法
2. 修改底层SQL查询以支持多个考勤分组ID的IN查询
3. 优化数据聚合逻辑，确保多个分组的数据正确合并
4. 添加单元测试验证多分组查询的正确性
