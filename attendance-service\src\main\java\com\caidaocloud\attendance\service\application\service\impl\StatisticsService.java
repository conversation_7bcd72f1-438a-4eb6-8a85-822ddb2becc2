package com.caidaocloud.attendance.service.application.service.impl;

import com.alibaba.fastjson.JSON;
import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.exception.CDException;
import com.caidao1.commons.message.MessageResource;
import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.commons.utils.ConvertHelper;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.SpringUtils;
import com.caidao1.commons.utils.StringUtil;
import com.caidao1.ioc.util.SessionHolder;
import com.caidao1.report.dto.FilterBean;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.mapper.WaGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaParseGroupMapper;
import com.caidao1.wa.mybatis.mapper.WaSobMapper;
import com.caidao1.wa.mybatis.model.WaGroup;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidao1.wa.mybatis.model.WaShiftDef;
import com.caidao1.wa.mybatis.model.WaSob;
import com.caidaocloud.attendance.core.annoation.CDText;
import com.caidaocloud.attendance.core.annoation.aspect.TextAspect;
import com.caidaocloud.attendance.core.commons.utils.DateUtilExt;
import com.caidaocloud.attendance.core.wa.dto.CalendarEventDto;
import com.caidaocloud.attendance.core.wa.dto.MobileEnum;
import com.caidaocloud.attendance.core.wa.enums.ApprovalStatusEnum;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.service.WaAttendanceConfigService;
import com.caidaocloud.attendance.core.wa.service.WaCommonService;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.attendance.service.application.constant.AttendanceCodes;
import com.caidaocloud.attendance.service.application.dto.AnalyzeResultCalculateDto;
import com.caidaocloud.attendance.service.application.dto.AttendanceAvailableColumn;
import com.caidaocloud.attendance.service.application.dto.BaseHeaderDto;
import com.caidaocloud.attendance.service.application.dto.WaStatisticsDto;
import com.caidaocloud.attendance.service.application.enums.*;
import com.caidaocloud.attendance.service.application.event.publish.AttendanceWorkflowMsgPublish;
import com.caidaocloud.attendance.service.application.event.publish.MessageParams;
import com.caidaocloud.attendance.service.application.service.IStatisticsService;
import com.caidaocloud.attendance.service.domain.entity.*;
import com.caidaocloud.attendance.service.infrastructure.util.BeanMapUtils;
import com.caidaocloud.attendance.service.infrastructure.util.PageUtil;
import com.caidaocloud.attendance.service.interfaces.dto.*;
import com.caidaocloud.attendance.service.interfaces.dto.common.EmpInfoDTO;
import com.caidaocloud.attendance.service.interfaces.dto.notify.AttendanceSummaryNotify;
import com.caidaocloud.attendance.service.interfaces.vo.StatisticsSummaryVo;
import com.caidaocloud.attendance.service.interfaces.vo.StatisticsWorkInfoVo;
import com.caidaocloud.attendance.service.interfaces.vo.SummaryRateVo;
import com.caidaocloud.dto.KeyValue;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.UserInfo;
import com.caidaocloud.hrpaas.metadata.sdk.dto.*;
import com.caidaocloud.hrpaas.metadata.sdk.filter.DataFilter;
import com.caidaocloud.hrpaas.metadata.sdk.query.DataQuery;
import com.caidaocloud.hrpaas.metadata.sdk.service.MetadataOperatorService;
import com.caidaocloud.hrpaas.metadata.sdk.util.SnowUtil;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataPropertyVo;
import com.caidaocloud.hrpaas.metadata.sdk.vo.MetadataVo;
import com.caidaocloud.hrpaas.paas.common.dto.DynamicPageDto;
import com.caidaocloud.hrpaas.paas.common.dto.UserDynamicConfig;
import com.caidaocloud.hrpaas.paas.common.feign.DynamicFeignClient;
import com.caidaocloud.message.sdk.enums.NoticeType;
import com.caidaocloud.record.core.context.LogRecordContext;
import com.caidaocloud.security.dto.SecurityUserInfo;
import com.caidaocloud.security.service.ISessionService;
import com.caidaocloud.security.util.SecurityUserUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.web.ResponseWrap;
import com.github.miemiedev.mybatis.paginator.domain.Order;
import com.github.miemiedev.mybatis.paginator.domain.PageList;
import com.github.miemiedev.mybatis.paginator.domain.Paginator;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.googlecode.totallylazy.Sequence;
import com.googlecode.totallylazy.Sequences;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.tuple.ImmutablePair;
import org.json.JSONArray;
import org.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class StatisticsService implements IStatisticsService {
    /**
     * caidaocloud.data.newwaanalyze 考勤分析新逻辑开启配置-此配置已不在使用，默认按照新逻辑进行分析
     */
    @Autowired
    private ISessionService sessionService;
    @Autowired
    private WaLeaveTypeDo waLeaveTypeDo;
    @Autowired
    private WorkOvertimeService workOvertimeService;
    @Autowired
    private WaCommonService waCommonService;
    @Autowired
    private WaGroupMapper waGroupMapper;
    @Autowired
    private WaParseGroupMapper waParseGroupMapper;
    @Autowired
    private WaSobMapper waSobMapper;
    @Autowired
    private WaSobDo waSobDo;
    @Autowired
    private WaAttendanceConfigService waConfigService;
    @Autowired
    private MessageResource messageResource;
    @Autowired
    private WaAnalyzeDo waAnalyzeDo;
    @Autowired
    private WaRegisterRecordDo waRegisterRecordDo;
    @Autowired
    private WaTravelTypeDo waTravelTypeDo;
    @Autowired
    private AttendanceWorkflowMsgPublish attendanceWorkflowMsgPublish;
    @Autowired
    private AnalyzeResultCalculateService analyzeResultCalculateService;
    @Autowired
    private WaLeaveDaytimeDo waLeaveDaytimeDo;
    @Autowired
    private WaEmpOvertimeDo waEmpOvertimeDo;
    @Autowired
    private OverTimeTypeDo overTimeTypeDo;
    @Resource
    private TextAspect textAspect;
    @Autowired
    private WaEmpTravelDaytimeDo empTravelDaytimeDo;
    @Resource
    private DynamicFeignClient dynamicFeignClient;
    @Resource
    private MetadataOperatorService metadataService;
    @Resource
    private SysEmpInfoMapper sysEmpInfoMapper;

    public final static List<String> day = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");
    public final static List<String> advance = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>","<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");
    public final static List<String> month = Arrays.asList("<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>");
    private final static int PAGE_SIZE = 500;
    @Value("${postTxt.showCode:enabled}")
    private String postTxtShowCode;

    private UserInfo getUserInfo() {
        return sessionService.getUserInfo();
    }

    @Override
    public StatisticsSummaryVo getSummaryCount(SummaryStaticPageDto dto, String dataScope) {
        Map<String, Object> summaryMap = waAnalyzeDo.getSummaryCountData(getUserInfo().getTenantId(), dto.getOrgIds(),
                dto.getStartDate(), dto.getEndDate(), dataScope);
        return FastjsonUtil.toObject(FastjsonUtil.toJson(summaryMap), StatisticsSummaryVo.class);
    }

    @Override
    public PageResult<StatisticsWorkInfoVo> getSummaryWorkList(SummaryDetailDto dto, String scope) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate());
        params.put("scope", scope);
        params.put("belongOrgId", getUserInfo().getTenantId());
        params.put("workType", dto.getWorkType());
        params.put("keywords", dto.getKeywords());
        params.put("orgIds", dto.getOrgIds());
        PageList<Map> pageResult = waAnalyzeDo.getSummaryWorkList(dto, params);
        List<StatisticsWorkInfoVo> workList = Optional.ofNullable(pageResult).map(it -> it.stream()
                .map(o1 -> FastjsonUtil.toObject(FastjsonUtil.toJson(o1), StatisticsWorkInfoVo.class))
                .collect(Collectors.toList())).orElse(Lists.newArrayList());
        return new PageResult<>(workList, dto.getPageNo(), dto.getPageSize(), pageResult == null ? 0 : pageResult.getPaginator().getTotalCount());
    }

    @Override
    public List<SummaryRateVo> getSummaryWorkRate(SummaryDetailDto dto, String scope) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate());
        params.put("scope", scope);
        params.put("belongOrgId", getUserInfo().getTenantId());
        params.put("orgIds", dto.getOrgIds());
        List<Map> mapList = waAnalyzeDo.getSummaryWorkRate(params);
        return mapList.stream().map(it -> {
            SummaryRateVo rateVo = new SummaryRateVo();
            rateVo.setLabel((String) it.get("fullname"));
            BigDecimal workCount = new BigDecimal(Objects.toString(it.get("workCount")));
            if (workCount.compareTo(BigDecimal.ZERO) <= 0) {
                rateVo.setRate(0);
            } else {
                BigDecimal rate = new BigDecimal(Objects.toString(it.get("registerCount")))
                        .divide(new BigDecimal(Objects.toString(it.get("workCount"))), 2, RoundingMode.HALF_UP);
                rateVo.setRate(rate.floatValue());
            }
            return rateVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SummaryRateVo> getSummaryTimeRate(SummaryDetailDto dto, Integer rateType, String scope) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate());
        params.put("scope", scope);
        params.put("belongOrgId", getUserInfo().getTenantId());
        params.put("orgIds", dto.getOrgIds());
        List<Map> mapList = waAnalyzeDo.getSummaryTimeRate(params);
        return mapList.stream().map(it -> {
            SummaryRateVo rateVo = new SummaryRateVo();
            rateVo.setLabel((String) it.get("fullname"));
            BigDecimal empWorkCount = new BigDecimal(Objects.toString(it.get("empCount")));
            boolean zeroEmpCount = empWorkCount.compareTo(BigDecimal.ZERO) <= 0;
            BigDecimal rate;
            if (rateType != null && rateType == 0) {
                rate = zeroEmpCount ? BigDecimal.ZERO : new BigDecimal(Objects.toString(it.get("workTime")))
                        .divide(new BigDecimal(Objects.toString(it.get("empCount"))), 2, RoundingMode.HALF_UP);
            } else {
                rate = zeroEmpCount ? BigDecimal.ZERO : new BigDecimal(Objects.toString(it.get("otTime")))
                        .divide(new BigDecimal(Objects.toString(it.get("empCount"))), 2, RoundingMode.HALF_UP);
            }
            rateVo.setRate(rate.floatValue());
            return rateVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List<SummaryRateVo> getSummaryLtRate(SummaryDetailDto dto, String scope) {
        Map<String, Object> params = Maps.newHashMap();
        params.put("startDate", dto.getStartDate());
        params.put("endDate", dto.getEndDate() + (24 * 3600 - 1));
        params.put("scope", scope);
        params.put("belongOrgId", getUserInfo().getTenantId());
        params.put("orgIds", dto.getOrgIds());
        List<Map> mapList = waAnalyzeDo.getSummaryLtRate(params);
        return mapList.stream().map(it -> {
            SummaryRateVo rateVo = new SummaryRateVo();
            rateVo.setLabel((String) it.get("leaveName"));
            rateVo.setRate(Float.parseFloat(Objects.toString(it.get("empCount"))));
            return rateVo;
        }).collect(Collectors.toList());
    }

    @Override
    public List getDayAnalyseList(DayAnalysePageDto dto, PageBean pageBean) {
        return SpringUtils.getBean(StatisticsService.class).getDayAnalyseList(dto, pageBean, getUserInfo());
    }

    @Override
    @CDText(exp = {"employ_type:employ_type_name" + TextAspect.DICT_E, "emp_status:emp_status_name" + TextAspect.STATUS_ENUM}, classType = Map.class)
    public List getDayAnalyseList(DayAnalysePageDto dto, PageBean pageBean, UserInfo user) {
        UserInfo userInfo = user != null ? user : getUserInfo();
        String belongId;
        if (userInfo != null) {
            belongId = userInfo.getTenantId();
        } else {
            belongId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        Long corpId = ConvertHelper.longConvert(belongId);
        Map<String, Object> paramsMap = new HashMap<>();
        //类型：如迟到、早退
        //前端筛选只传filterList 此处用于转换，将filterlist中值转换成如 lat:true
        if (CollectionUtils.isNotEmpty(dto.getFilterList()) && dto.getFilterList() != null) {
            for (FilterBean bean : dto.getFilterList()) {
                if ("type".equals(bean.getField()) && StringUtils.isNotBlank(bean.getMin())) {
                    String[] strs = bean.getMin().split(",");
                    for (String str : strs) {
                        paramsMap.put(StatisticsTypeEnum.getCode(Integer.parseInt(str)), true);
                    }
                    bean.setMin("");
                }
            }
        }
        paramsMap.put("filter", pageBean.getFilter());
        paramsMap.put("corpid", corpId);
        paramsMap.put("belongid", belongId);
        paramsMap.put("types", null);
        paramsMap.put("waGroupIds", dto.getWaGroupIds());
        Integer endDate = dto.getEndDate();
        paramsMap.put("startDate", dto.getStartDate());
        paramsMap.put("endDate", endDate);
        paramsMap.put("waGroupId", null);
        paramsMap.put("waSobId", dto.getSobId());
        paramsMap.put("isOrigin", dto.isOrigin());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            paramsMap.put("keywords", dto.getKeywords());
        }
        if (CollectionUtils.isNotEmpty(dto.getEmpIds()) && dto.getEmpIds() != null) {
            paramsMap.put("empIds", dto.getEmpIds());
        }
        paramsMap.put("notOrderBy", dto.getNotOrderBy());
        String[] statusOptions = dto.getStatusOptions();
        if (statusOptions != null && statusOptions.length > 0) {
            for (String k : statusOptions) {
                paramsMap.put(k, true);
            }
        }
        paramsMap.put("datafilter", dto.getDataScope());
        return this.searchRegisterWaAnalysePageList(pageBean, paramsMap, dto.getMergeWorknoToEmpName(), dto.getIsCount());
    }

    public List searchRegisterWaAnalyseList(PageBean pageBean, Map<String, Object> paramsMap, Boolean mergeWorknoToEmpName, Boolean isCount) {
        String belongId = (String) paramsMap.get("belongid");
        Long startDate = Long.valueOf(paramsMap.get("startDate").toString());
        Long endDate = Long.valueOf(paramsMap.get("endDate").toString());
        boolean isDefault = false;
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        paramsMap.put("isDefault", isDefault);
        Map<String, List<String>> fieldlist = generateColumns(paramsMap, 1, belongId);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        if (StringUtils.isNotBlank(filter) && filter.contains("analyze_result")) {
            filter = filter.replaceAll("\"analyze_result\"", "temp.analyze_result");
        }
        paramsMap.put("filter", filter);
        List<String> otherList = new ArrayList<>(Arrays.asList("late_time", "early_time", "work_time", "actual_work_time", "register_time", "kg_work_time", "abnormal_appeal_time"));
        fieldlist.put("other", otherList);
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        PageList<Map> list = waAnalyzeDo.selectWaAnalyseListByWaGroup(pageBounds, paramsMap);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (null != isCount && isCount) {
            return list;
        }
        List<String> setDefaultValueKeyList = new ArrayList<>(Arrays.asList("org_name", "employ_type_name", "emp_status_name", "shift_def_name", "shiftStartTime", "shiftEndTime"));

        for (Map map : list) {
            //单位转化 分钟-小时
            convertToTime(fieldlist, map, false);
            Long belongDate = (Long) map.get("belong_date");
            if (belongDate != null) {
                String week = DateUtil.getWeekLangCode(new Date(belongDate * 1000));
                if (StringUtils.isNotBlank(week)) {
                    String lang = StringUtils.isBlank(SessionHolder.getLang()) ? "zh_CN" : SessionHolder.getLang();
                    map.put("belong_date_week", messageResource.getMessage(week, new Object[]{}, new Locale(lang)));
                }
            }
            // TODO 设置出勤规则 clock_type 字段已经冗余到了考勤分析结果表中了，可以直接使用，下面代码是补偿代码，后面待数据稳定后，可以去掉
            if (!map.containsKey("clock_type")) {
                setClockType(map, belongId);
            }
            if (!map.containsKey("bid")) {
                map.put("bid", SnowUtil.nextId());
            }

            EmpInfoDTO empInfoDTO = FastjsonUtil.convertObject(map, EmpInfoDTO.class);
            empInfoDTO.setName((String) map.get("emp_name"));
            map.put("empInfo", empInfoDTO);

            if (BooleanUtils.isTrue(mergeWorknoToEmpName)) {
                map.put("emp_name", map.get("workno") + "(" + map.get("emp_name") + ")");
            }

            if (map.get("shiftStartTime") != null && map.get("shiftEndTime") != null) {
                Integer start = (Integer) map.get("shiftStartTime");
                Integer end = (Integer) map.get("shiftEndTime");
                map.put("shiftStartTime", DateUtil.convertMinuteToTime(start));
                if (start <= end) {
                    map.put("shiftEndTime", DateUtil.convertMinuteToTime(end));
                } else {
                    map.put("shiftEndTime", "次日" + DateUtil.convertMinuteToTime(end));
                }
            }
            if (map.get("date_type") != null) {
                String dateType = map.get("date_type").toString();
                if (StringUtils.isNotBlank(dateType)) {
                    map.put("date_type", DateTypeEnum.getName(Integer.parseInt(dateType)));
                }
            }
            if (map.get("clock_type") != null) {
                Integer clockType = Integer.valueOf(map.get("clock_type").toString());
                map.put("clock_type", ParseGroupClockTypeEnum.getName(clockType));
            }
            setDefaultValue(setDefaultValueKeyList, map);
        }
        return list;
    }

    /**
     * 返回分页形式
     *
     * @param pageBean
     * @param paramsMap
     * @param mergeWorkNoToEmpName
     * @param isCount
     * @return
     */
    public PageList<Map> searchRegisterWaAnalysePageList(PageBean pageBean, Map<String, Object> paramsMap, Boolean mergeWorkNoToEmpName, Boolean isCount) {
        String belongId = (String) paramsMap.get("belongid");
        Long startDate = Long.valueOf(paramsMap.get("startDate").toString());
        Long endDate = Long.valueOf(paramsMap.get("endDate").toString());
        boolean isDefault = false;
        paramsMap.put("startDate", startDate);
        paramsMap.put("endDate", endDate);
        paramsMap.put("isDefault", isDefault);
        Map<String, List<String>> fieldlist = generateColumns(paramsMap, 1, belongId);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        if (StringUtils.isNotBlank(filter) && filter.contains("analyze_result")) {
            filter = filter.replaceAll("\"analyze_result\"", "temp.analyze_result");
        }
        paramsMap.put("filter", filter);
        List<String> otherList = new ArrayList<>(Arrays.asList("late_time", "early_time", "work_time", "actual_work_time", "register_time", "kg_work_time", "abnormal_appeal_time"));
        fieldlist.put("other", otherList);
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        PageList<Map> list = waAnalyzeDo.selectWaAnalyseListByWaGroup(pageBounds, paramsMap);
        if (CollectionUtils.isEmpty(list)) {
            return list;
        }
        if (null != isCount && isCount) {
            return list;
        }
        List<String> setDefaultValueKeyList = new ArrayList<>(Arrays.asList("org_name", "employ_type_name", "emp_status_name", "shift_def_name", "shiftStartTime", "shiftEndTime"));
        //班次
        Map<Integer, WaShiftDef> corpShiftDefMap = waCommonService.getCorpAllShiftDef(belongId);
        for (Map map : list) {
            //单位转化 分钟-小时
            convertToTime(fieldlist, map, false);
            Long belongDate = (Long) map.get("belong_date");
            if (belongDate != null) {
                String week = DateUtil.getWeekLangCode(new Date(belongDate * 1000));
                if (StringUtils.isNotBlank(week)) {
                    String lang = StringUtils.isBlank(SessionHolder.getLang()) ? "zh_CN" : SessionHolder.getLang();
                    map.put("belong_date_week", messageResource.getMessage(week, new Object[]{}, new Locale(lang)));
                }
            }
            if (!map.containsKey("clock_type")) {
                setClockType(map, belongId);
            }
            if (!map.containsKey("bid")) {
                map.put("bid", SnowUtil.nextId());
            }
            EmpInfoDTO empInfoDTO = FastjsonUtil.convertObject(map, EmpInfoDTO.class);
            empInfoDTO.setName((String) map.get("emp_name"));
            map.put("empInfo", empInfoDTO);
            if (BooleanUtils.isTrue(mergeWorkNoToEmpName)) {
                map.put("emp_name", map.get("workno") + "(" + map.get("emp_name") + ")");
            }
            if (map.get("shiftStartTime") != null && map.get("shiftEndTime") != null) {
                Integer start = (Integer) map.get("shiftStartTime");
                Integer end = (Integer) map.get("shiftEndTime");
                map.put("shiftStartTime", DateUtil.convertMinuteToTime(start));
                if (start <= end) {
                    map.put("shiftEndTime", DateUtil.convertMinuteToTime(end));
                } else {
                    map.put("shiftEndTime", "次日" + DateUtil.convertMinuteToTime(end));
                }
            }
            if (map.get("date_type") != null) {
                String dateType = map.get("date_type").toString();
                if (StringUtils.isNotBlank(dateType)) {
                    map.put("date_type", DateTypeEnum.getName(Integer.parseInt(dateType)));
                }
            }
            if (map.get("clock_type") != null) {
                Integer clockType = Integer.valueOf(map.get("clock_type").toString());
                map.put("clock_type", ParseGroupClockTypeEnum.getName(clockType));
            }
            if (map.get("i18n_shift_def_name") != null) {
                String i18nShiftDefName = map.get("i18n_shift_def_name").toString();
                String i18n = LangParseUtil.getI18nLanguage(i18nShiftDefName, null);
                if (com.caidaocloud.util.StringUtil.isNotBlank(i18n)) {
                    map.put("shift_def_name", i18n);
                    map.remove("i18n_shift_def_name");
                }
            }
            if (map.get("shift_def_ids") != null) {
                String shiftDefIds = map.get("shift_def_ids").toString();
                if (shiftDefIds.contains(",")) {
                    String shiftDefName = Arrays.stream(shiftDefIds.split(",")).map(Integer::valueOf).map(corpShiftDefMap::get).filter(Objects::nonNull)
                            .map(shift -> LangParseUtil.getI18nLanguage(shift.getI18nShiftDefName(), shift.getShiftDefName()))
                            .collect(Collectors.joining(","));
                    map.put("shift_def_name", shiftDefName);
                }
            }
            setDefaultValue(setDefaultValueKeyList, map);
        }
        return list;
    }

    @Deprecated
    private void setClockType(Map map, String belongId) {
        List<Long> list = new ArrayList<>();
        Long empId = Long.valueOf(map.get("empid").toString());
        list.add(empId);
        Long startDate = Long.valueOf(map.get("belong_date").toString());
        Long endDate = startDate;
        if (endDate != null) {
            int m = (23 * 60 * 60) + (59 * 60) + 59;
            endDate += m;
        }
        String anyEmpids = "'{" + org.apache.commons.lang3.StringUtils.join(list, ",").concat("}'");
        Map params = new HashMap();
        params.put("belongid", belongId);
        params.put("anyEmpids", anyEmpids);
        params.put("startDate", startDate);
        params.put("endDate", endDate);
        List<Map> parseGroups = waParseGroupMapper.getEmpWaGroupAndParseGrops(params);
        if (CollectionUtils.isNotEmpty(parseGroups)) {
            map.put("clock_type", parseGroups.get(0).get("clock_type"));
        }
    }

    /**
     * 生成 请假加班列
     *
     * @param paramsMap
     * @param type      1 sum  2 非sum
     * @param belongid
     */
    private Map<String, List<String>> generateColumns(Map<String, Object> paramsMap, int type, String belongid) {
        List<String> ltField = new ArrayList<>();
        List<String> otField = new ArrayList<>();
        List<String> extField = new ArrayList<>();
        StringBuffer columns = new StringBuffer();
        Boolean isOrigin = (Boolean) paramsMap.get("isOrigin");
        String leaveField = "level_column_jsonb";
        if (isOrigin != null && isOrigin) {
            leaveField = "origin_level_column_jsonb";
        }
        List<Map> leaveTypes = waConfigService.listLeaveTypeDef(belongid, SessionHolder.getLang());
        if (leaveTypes != null && leaveTypes.size() > 0) {
            for (Map lt : leaveTypes) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String alias = "lt_" + lt.get("value") + "_key";
                ltField.add(alias);

                String timeUnit = "lt_" + lt.get("value") + "_key_unit";
                columns.append("cast(" + leaveField + " ->> '" + timeUnit + "' as integer)  as \"" + timeUnit + "\",");
                if (type == 1) {
                    columns.append("cast(" + leaveField + " ->> '" + alias + "' as numeric)  as \"" + alias + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + leaveField + " ->> '" + alias + "' as numeric)/60,2)  as \"" + alias + "\"");
                }
            }
        }

        //查询出差类型
        String travelField = "travel_column_jsonb";
        if (isOrigin != null && isOrigin) {
            travelField = "origin_travel_column_jsonb";
        }
        List<WaTravelTypeDo> travelTypeList = waTravelTypeDo.getWaTravelTypeList(belongid);
        if (CollectionUtils.isNotEmpty(travelTypeList)) {
            for (WaTravelTypeDo travelTypeDo : travelTypeList) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String alias = "lt_" + travelTypeDo.getTravelTypeId() + "_key";
                ltField.add(alias);

                String timeUnit = "lt_" + travelTypeDo.getTravelTypeId() + "_key_unit";
                columns.append("cast(" + travelField + " ->> '" + timeUnit + "' as integer)  as \"" + timeUnit + "\",");
                if (type == 1) {
                    columns.append("cast(" + travelField + " ->> '" + alias + "' as numeric)  as \"" + alias + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + travelField + " ->> '" + alias + "' as numeric)/60,2)  as \"" + alias + "\"");
                }
            }
        }

        String overtimeField = "ot_column_jsob";
        if (isOrigin != null && isOrigin) {
            overtimeField = "origin_ot_column_jsonb";
        }
        // 加班类型+补偿规则
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(belongid, null, DateUtil.getCurrentTime(true));
        if (CollectionUtils.isNotEmpty(overtimeTypes)) {
            for (OverTimeTypeDo overtimeType : overtimeTypes) {
                if (columns.length() > 0 && !columns.toString().endsWith(",")) {
                    columns.append(",");
                }
                String alias = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
                String timeUnit = "ot_" + overtimeType.getOvertimeTypeId() + "_key_unit";
                columns.append("cast(" + overtimeField + " ->> '" + timeUnit + "' as integer)  as \"" + timeUnit + "\",");
                otField.add(alias);
                if (type == 1) {
                    columns.append("cast(" + overtimeField + " ->> '" + alias + "' as numeric)  as \"" + alias + "\"");
                } else if (type == 2) {
                    columns.append("round(cast(" + overtimeField + " ->> '" + alias + "' as numeric)/60,2)  as \"" + alias + "\"");
                }
            }
        }
        if (columns.length() > 0) {
            columns.append(",a.signin_id");
            columns.append(",a.signoff_id");
            paramsMap.put("columns", "," + columns);
        }
        Map fieldMap = new HashMap();
        fieldMap.put("lt", ltField);
        fieldMap.put("ot", otField);
        fieldMap.put("ext", extField);
        return fieldMap;
    }

    private void convertToTime(Map<String, List<String>> fieldList, Map map, Boolean isReturnNegative) {
        boolean finalIsReturnNegative = !(isReturnNegative == null) && isReturnNegative;//返回值是否允许是负数，默认false
        // 标准列
        String excludeKey = "is_exp|is_shift|is_kg";
        // 标准列
        List<String> other = fieldList.get("other");
        other.forEach(key -> {
            //时间转换
            if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                Double dou = Double.valueOf(map.get(key).toString());
                map.put(key.concat("_minute"), dou);
                BigDecimal v = new BigDecimal(dou).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                map.put(key, v.doubleValue());
                if (!finalIsReturnNegative && v.doubleValue() < 0) {
                    map.put(key, 0);
                }
            }
            //初始化值
            if (!excludeKey.contains(key) && StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });
        // 加班列
        List<String> otList = fieldList.get("ot");
        otList.forEach((key) -> {
            //时间转换
            if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                if (map.get(key) instanceof BigDecimal) {
                    BigDecimal decimal = (BigDecimal) map.get(key);
                    map.put(key.concat("_simple"), decimal);
                    map.put(key.concat("_minute"), decimal.longValue());
                    //BigDecimal v = decimal.divide(new BigDecimal(60), 2, BigDecimal.ROUND_HALF_UP);
                    BigDecimal v = decimal.setScale(2, RoundingMode.HALF_UP);
                    map.put(key, v.doubleValue());
                    if (!finalIsReturnNegative && v.doubleValue() < 0) {
                        map.put(key, 0);
                    }
                } else {
                    map.put(key, map.get(key));
                }
            }
            //初始化值
            if (StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });
        otList.forEach((key) -> {
            if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                if (!key.contains("_key_unit")) {
                    Integer unit = (Integer) map.get(key + "_unit");
                    if (null != unit) {
                        String unitName = PreTimeUnitEnum.getName(unit);
                        map.put(key, map.get(key).toString() + unitName);
                    }
                }
            }
        });
        //请假列
        List<String> ltList = fieldList.get("lt");
        ltList.forEach(key -> {
            //时间转换
            if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                Integer unit = (Integer) map.get(key + "_unit");
                if (unit != null && unit == 2) {
                    //小时
                    Double dou = Double.valueOf(map.get(key).toString());
                    map.put(key.concat("_minute"), dou);

                    BigDecimal v = new BigDecimal(dou).divide(new BigDecimal(60), 2, RoundingMode.HALF_UP);
                    map.put(key, v.doubleValue());

                    if (!finalIsReturnNegative && v.doubleValue() < 0) {
                        map.put(key, 0);
                    }
                }
            }
            //初始化值
            if (StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
                map.put(key.concat("_minute"), 0);
            }
        });
        List<String> extList = fieldList.get("ext");
        extList.forEach(key -> {
            if (StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, 0);
            }
        });
    }

    private void replaceValue(Map map, String register_type, String result_type, String result_desc) {
        Integer type = (Integer) map.get(register_type);
        if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, register_type)) {
            String key = register_type + "_name";
            if (type != null) {
                if (type == 1) {
                    map.put(key, "签到");
                } else if (type == 2) {
                    map.put(key, "签退");
                } else if (type == 3) {
                    map.put(key, "外勤签到");
                }
            }
        }
        if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, result_type)) {
            String key = result_type + "_name";
            Integer rtype = (Integer) map.get(result_type);
            if (type != null) {
                if (type != 3) {
                    if (rtype == 1) {
                        map.put(key, "正常");
                    } else {
                        map.put(key, "异常");
                    }
                }
            }

        }

        if (!StringUtil.isNotContainsKeyOrisNullOrEmpty(map, result_desc)) {
            String result = map.get(result_desc).toString();
            String[] des = result.split(",");
            StringBuilder value = new StringBuilder();
            for (int i = 0; i < des.length; i++) {
                String err = des[i].trim();
                if (value.length() > 0) {
                    value.append(",");
                }
                if (err.equals(MobileEnum.RegisterErrorType.LOCAL_ERR.toString())) {
                    value.append("地点异常");
                } else if (err.equals(MobileEnum.RegisterErrorType.TIME_ERR.toString())) {
                    value.append("时间异常");
                } else if (err.equals(MobileEnum.RegisterErrorType.DEVICE_ERR.toString())) {
                    value.append("设备号异常");
                } else {
                    value.append(des[i]);
                }
            }
            map.put(result + "_name", value.toString());
            map.put(result_desc + "_txt", value.toString());
        }
    }

    private void setDefaultValue(List<String> fieldList, Map map) {
        fieldList.forEach(key -> {
            if (StringUtil.isNotContainsKeyOrisNullOrEmpty(map, key)) {
                map.put(key, "-");
            }
        });
    }

    @Override
    public List searchMonthRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo) {
        return searchRegisterStatistics(dto, pageBean, userInfo);
    }

    @Override
    public List searchRegisterStatisticsAdvance(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo) {
        return searchRegisterStatistics(dto, pageBean, true, userInfo);
    }

    public List searchRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, Boolean summary, UserInfo user) {
        UserInfo userInfo = user != null ? user : getUserInfo();
        String tenantId;
        if (userInfo != null) {
            tenantId = userInfo.getTenantId();
        } else {
            tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        pageBean.setFilterList(dto.getFilterList());
        // 员工签到数据结合排班数据进行分析
        Map<String, Object> paramsMap = new HashMap<>();
        //自定义过滤
        if (CollectionUtils.isNotEmpty(pageBean.getFilterList())) {
            List<FilterBean> filterList = pageBean.getFilterList();
            List<FilterBean> empFilterList = new ArrayList<>();
            List<FilterBean> waFilterList = new ArrayList<>();
            List<String> empFieldList = new ArrayList<>(Arrays.asList("workno", "emp_name", "orgid"));
            filterList.forEach(filterBean -> {
                if (!"status".equals(filterBean.getField())) {
                    if (empFieldList.contains(filterBean.getField())) {
                        empFilterList.add(filterBean);
                    } else {
                        waFilterList.add(filterBean);
                    }
                }
                //考勤状态
                if ("status".equals(filterBean.getField())) {
                    paramsMap.put("status", filterBean.getMin());
                }
            });
            paramsMap.keySet().removeIf("filter"::equals);
            if (CollectionUtils.isNotEmpty(waFilterList)) {
                pageBean.setFilterList(waFilterList);
                paramsMap.put("wafilter", pageBean.getFilter());
            }
            if (CollectionUtils.isNotEmpty(empFilterList)) {
                pageBean.setFilterList(empFilterList);
                paramsMap.put("filter", pageBean.getFilter());
            }
        }

        paramsMap.put("corpid", ConvertHelper.longConvert(tenantId));
        paramsMap.put("belongid", tenantId);
        if (CollectionUtils.isNotEmpty(dto.getEmpIds())) {
            paramsMap.put("empIds", dto.getEmpIds());
        }
        paramsMap.put("ym", dto.getYm());
        Long startDate = dto.getStartDate();
        Long endDate = dto.getEndDate();
        if (startDate != null && endDate != null) {
            //如果是按时间区间查询
            paramsMap.put("startDate", startDate);
            paramsMap.put("endDate", endDate);
            paramsMap.put("isInterval", 1);
        }
        paramsMap.put("waGroupId", dto.getGroupId());
        paramsMap.put("isOrigin", dto.isOrigin());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            paramsMap.put("keywords", dto.getKeywords());
        }
        paramsMap.put("statisticsType", dto.getStatisticsType());
        paramsMap.put("datafilter", dto.getDataScope());
        List<Map> list = workOvertimeService.searchRegMonthStatistics(pageBean, paramsMap, new ImmutablePair<>(dto.getStartDate(), dto.getEndDate()), summary, userInfo);
        if (CollectionUtils.isNotEmpty(list) && BooleanUtils.isTrue(dto.getMergeWorknoToEmpName())) {
            List<String> setDefaultValueKeyList = new ArrayList<>(Arrays.asList("org_name", "employ_type_name", "emp_status_name"));
            list.forEach(m -> {
                m.put("emp_name", m.get("workno") + "(" + m.get("emp_name") + ")");
                setDefaultValue(setDefaultValueKeyList, m);
            });
        }
        return list;
    }

    @Override
    public List searchRegisterStatistics(MonthAnalysePageDto dto, PageBean pageBean, UserInfo user) {
        return searchRegisterStatistics(dto, pageBean, false, user);
    }

    @Override
    public List<String> getSummaryDayHeaders(int statisticsType) {
        List<String> list = new ArrayList<>();
        list.add("{\"id\": \"belong_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"" + "所属日期" + "\"}");
        list.add("{\"id\": \"workno\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "工号" + "\"}");
        list.add("{\"id\": \"emp_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"姓名\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"org_name\",\"width\":\"200px\",\"type\":\"ro\",\"sort\":\"str\",\"value\":\"" + "任职组织" + "\",\"filter\":{\"type\":\"combo\",\"name\":\"orgid\",\"attach\":{\"type\":\"tree\",\"config\":{\"connector\":\"org/getOrgTreeJson\",\"lazy\":\"org/getSubOrgTreeJson\"}}},\"ellipsis\":true}");
        list.add("{\"id\": \"org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "组织全路径" + "\"}");
        list.add("{\"id\": \"ax_org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "AX组织全路径" + "\"}");
        list.add("{\"id\": \"emp_status_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工状态\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"shift_def_name\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "班次名称" + "\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"date_type\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"日期类型\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"clock_type\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"出勤规则\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"signInTime\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"打卡时间1\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"signOffTime\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"打卡时间2\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"wa_group_name\", \"width\": \"200px\", \"type\": \"ro\", \"value\": \"" + "考勤方案" + "\"}");
        list.add("{\"id\": \"work_time\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(小时)" + "\"}");
        list.add("{\"id\": \"work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(天)" + "\"}");
        list.add("{\"id\": \"register_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(小时)" + "\"}");
        list.add("{\"id\": \"register_time_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(天)" + "\"}");
        list.add("{\"id\": \"actual_work_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际工作时长(小时)" + "\"}");
        list.add("{\"id\": \"leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（小时）" + "\"}");
        list.add("{\"id\": \"leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（天）" + "\"}");
        list.add("{\"id\": \"full_paid_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（小时）" + "\"}");
        list.add("{\"id\": \"full_paid_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（天）" + "\"}");
        list.add("{\"id\": \"deduction_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（小时）" + "\"}");
        list.add("{\"id\": \"deduction_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（天）" + "\"}");
        list.add("{\"id\": \"travel_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（小时）" + "\"}");
        list.add("{\"id\": \"travel_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（天）" + "\"}");
        if (statisticsType == 0) {
            list.add("{\"id\": \"late_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到时长(分钟)" + "\"}");
        } else {
            list.add("{\"id\": \"early_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退时长(分钟)" + "\"}");
        }
        return list;
    }

    @Override
    public List<String> getSummaryDynamicHeaders(int statisticsType) {
        UserInfo userInfo = getUserInfo();
        Sequence<String> headers = Sequences.sequence(
                /*"{\"id\": \"belong_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"" + "所属日期" + "\"}",*/
                "{\"id\": \"workno\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "工号" + "\"}",
                "{\"id\": \"emp_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"姓名\",\"filter\":{\"type\":\"str\"}}",
                "{\"id\": \"org_name\",\"width\":\"200px\",\"type\":\"ro\",\"sort\":\"str\",\"value\":\"" + "任职组织" + "\",\"filter\":{\"type\":\"combo\",\"name\":\"orgid\",\"attach\":{\"type\":\"tree\",\"config\":{\"connector\":\"org/getOrgTreeJson\",\"lazy\":\"org/getSubOrgTreeJson\"}}},\"ellipsis\":true}",
                "{\"id\": \"org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "组织全路径" + "\"}",
                "{\"id\": \"ax_org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "AX组织全路径" + "\"}",
                "{\"id\": \"emp_status_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工状态\",\"filter\":{\"type\":\"str\"}}"
        );
        if (statisticsType == 0) {
            return headers.join(Optional.of(waConfigService.listLeaveTypeDef(userInfo.getTenantId(), SessionHolder.getLang()).stream().map(
                    it -> "{\"id\": \"lt_" + it.get("value") + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + it.get("text") + "\"}"
            ).collect(Collectors.toList())).orElse(Lists.newArrayList())).toList();
        } else if (statisticsType == 1) {
            // 出差
            return headers.join(Optional.of(waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId()).stream().map(
                    it -> "{\"id\": \"tr_" + it.getTravelTypeId() + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(it.getI18nTravelTypeName(), it.getTravelTypeName()) + "\"}"
            ).collect(Collectors.toList())).orElse(Lists.newArrayList())).toList();
        } else if (statisticsType == 2) {
            // 加班
            return headers.join(Optional.of(overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), null, DateUtil.getCurrentTime(true)).stream().map(
                    it -> "{\"id\": \"ot_" + it.getOvertimeTypeId() + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(it.getI18nTypeName(), it.getTypeName()) + "\"}"
            ).collect(Collectors.toList())).orElse(Lists.newArrayList())).toList();
        } else if (statisticsType == 3) {
            // 迟到
            return headers.join(Sequences.sequence(
                    "{\"id\": \"latecount\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到次数" + "\"}",
                    "{\"id\": \"late_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到时长(分钟)" + "\"}"
            )).toList();
        } else if (statisticsType == 4) {
            // 早退
            return headers.join(Sequences.sequence(
                    "{\"id\": \"earlycount\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退次数" + "\"}",
                    "{\"id\": \"early_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退时长(分钟)" + "\"}"
            )).toList();
        }
        return headers.toList();
    }

    //CAIDAOM-3208 打卡时间1 打卡时间2 调整
    @Override
    public List<String> getDayHeaders() {
        List<String> list = new ArrayList<>();
        list.add("{\"id\": \"analyze_result\", \"width\": \"100px\", \"type\": \"ro\", \"value\": \"考勤状态\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"belong_date\", \"width\": \"60px\", \"type\": \"date\", \"value\": \"" + "考勤日期" + "\"}");
        list.add("{\"id\": \"emp_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"姓名\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"org_name\",\"width\":\"200px\",\"type\":\"ro\",\"sort\":\"str\",\"value\":\"" + "任职组织" + "\",\"filter\":{\"type\":\"combo\",\"name\":\"orgid\",\"attach\":{\"type\":\"tree\",\"config\":{\"connector\":\"org/getOrgTreeJson\",\"lazy\":\"org/getSubOrgTreeJson\"}}},\"ellipsis\":true}");
        list.add("{\"id\": \"org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "组织全路径" + "\"}");
        list.add("{\"id\": \"ax_org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "AX组织全路径" + "\"}");
        list.add("{\"id\": \"emp_status_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工状态\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"employ_type_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工类型\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"hire_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"入职日期\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"emp_status_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工状态\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"shift_def_name\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "班次名称" + "\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"date_type\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"日期类型\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"clock_type\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"出勤规则\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"signInTime\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"签到时间\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"signOffTime\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"签退时间\",\"filter\":{\"type\":\"str\"}}");
        list.add("{\"id\": \"work_time\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(小时)" + "\"}");
        list.add("{\"id\": \"work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(天)" + "\"}");
        list.add("{\"id\": \"register_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(小时)" + "\"}");
        list.add("{\"id\": \"register_time_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(天)" + "\"}");
        list.add("{\"id\": \"actual_work_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际工作时长(小时)" + "\"}");
        list.add("{\"id\": \"leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（小时）" + "\"}");
        list.add("{\"id\": \"leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（天）" + "\"}");
        list.add("{\"id\": \"full_paid_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（小时）" + "\"}");
        list.add("{\"id\": \"full_paid_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（天）" + "\"}");
        list.add("{\"id\": \"deduction_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（小时）" + "\"}");
        list.add("{\"id\": \"deduction_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（天）" + "\"}");
        list.add("{\"id\": \"travel_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（小时）" + "\"}");
        list.add("{\"id\": \"travel_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（天）" + "\"}");
        list.add("{\"id\": \"late_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到时长(分钟)" + "\"}");
        list.add("{\"id\": \"early_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退时长(分钟)" + "\"}");
        list.add("{\"id\": \"kg_work_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "旷工时长(分钟)" + "\"}");
        UserInfo userInfo = getUserInfo();
        String tenantId;
        if (userInfo != null) {
            tenantId = userInfo.getTenantId();
        } else {
            tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        // 请假类型
        List<Map> mapList = waConfigService.listLeaveTypeDef(tenantId, SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> list.add("{\"id\": \"lt_" + lt.get("value") + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + lt.get("text") + "\"}"));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(tenantId);
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> list.add("{\"id\": \"lt_" + lt.getTravelTypeId() + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()) + "\"}"));
        }
        //加班类型
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(tenantId, null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            list.add("{\"id\": \"" + columnName + "\", \"width\": \"160px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()) + "\"}");
        }
        return list;
    }

    /**
     * 获取组装请假类型key
     *
     * @param sobIds
     * @return
     */
    @Deprecated
    public Map<String, String> getLeaveTypeMap(String sobIds, UserInfo user) {
        Map<String, String> leaveTypeMap = new HashMap<>();
        UserInfo userInfo = user != null ? user : getUserInfo();
        if (StringUtils.isNotBlank(sobIds)) {
            Integer sobId;
            if (sobIds.contains(",")) {
                sobId = Integer.valueOf(sobIds.split(",")[0]);
            } else {
                sobId = Integer.valueOf(sobIds);
            }
            List<WaLeaveTypeDo> leaveTypes = waLeaveTypeDo.getLeaveTypeBySobId(userInfo.getTenantId(), sobId);
            if (CollectionUtils.isNotEmpty(leaveTypes)) {
                leaveTypes.forEach(lt -> leaveTypeMap.put("lt_" + lt.getLeaveTypeId() + "_key", "lt_" + lt.getLeaveType() + "_key"));
            }
        }
        return leaveTypeMap;
    }

    @Override
    public List<BaseHeaderDto> getDayHeadersForExport(String sobIds, UserInfo user) {
        List<BaseHeaderDto> head = new ArrayList<>();
        head.add(new BaseHeaderDto("analyze_result", "ro", "考勤状态"));
        head.add(new BaseHeaderDto("belong_date", "date", "考勤日期"));
        /*head.add(new BaseHeaderDto("workno", "ro", "账号"));*/
        head.add(new BaseHeaderDto("emp_name", "ro", "姓名"));
        head.add(new BaseHeaderDto("org_name", "ro", "任职组织"));
        head.add(new BaseHeaderDto("org_full_path", "ro", "组织全路径"));
        head.add(new BaseHeaderDto("ax_org_full_path", "ro", "AX组织全路径"));
        head.add(new BaseHeaderDto("employ_type_name", "ro", "员工类型"));
        head.add(new BaseHeaderDto("hire_date", "date", "入职日期"));
        head.add(new BaseHeaderDto("emp_status_name", "ro", "员工状态"));
        /* head.add(new BaseHeaderDto("belong_date_week", "ro", "星期"));*/
        head.add(new BaseHeaderDto("shift_def_name", "ro", "班次名称"));
        head.add(new BaseHeaderDto("date_type", "ro", "日期类型"));
        head.add(new BaseHeaderDto("clock_type", "ro", "出勤规则"));
        head.add(new BaseHeaderDto("signInTime", "ro", "打卡时间1"));
        head.add(new BaseHeaderDto("signOffTime", "ro", "打卡时间2"));
        head.add(new BaseHeaderDto("work_time", "ro", "应出勤时长(小时)"));
        head.add(new BaseHeaderDto("work_time_day", "ro", "应出勤时长(天)"));
        head.add(new BaseHeaderDto("register_time", "ro", "实际出勤时长(小时)"));
        head.add(new BaseHeaderDto("register_time_day", "ro", "实际出勤时长(天)"));
        head.add(new BaseHeaderDto("actual_work_time", "ro", "实际工作时长(小时)"));
        head.add(new BaseHeaderDto("leave_duration", "ro", "请休假总时长（小时）"));
        head.add(new BaseHeaderDto("leave_duration_day", "ro", "请休假总时长（天）"));
        head.add(new BaseHeaderDto("full_paid_leave_duration", "ro", "全薪假期总时长（小时）"));
        head.add(new BaseHeaderDto("full_paid_leave_duration_day", "ro", "全薪假期总时长（天）"));
        head.add(new BaseHeaderDto("deduction_leave_duration", "ro", "扣薪假期总时长（小时）"));
        head.add(new BaseHeaderDto("deduction_leave_duration_day", "ro", "扣薪假期总时长（天）"));
        head.add(new BaseHeaderDto("travel_duration", "ro", "出差总时长（小时）"));
        head.add(new BaseHeaderDto("travel_duration_day", "ro", "出差总时长（天）"));
        head.add(new BaseHeaderDto("late_time_minute", "ro", "迟到时长(分钟)"));
        head.add(new BaseHeaderDto("early_time_minute", "ro", "早退时长(分钟)"));
        head.add(new BaseHeaderDto("kg_work_time_minute", "ro", "旷工时长(分钟)"));
        //请假类型
        UserInfo userInfo = user != null ? user : getUserInfo();
        List<Map> mapList = waConfigService.listLeaveTypeDef(userInfo.getTenantId(), SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> head.add(new BaseHeaderDto("lt_" + lt.get("value") + "_key", "ro", lt.get("text").toString())));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> head.add(new BaseHeaderDto("lt_" + lt.getTravelTypeId() + "_key", "ro", LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()))));
        }
        //加班类型
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            head.add(new BaseHeaderDto(columnName, "ro", LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName())));
        }
        return head;
    }

    /**
     * 每日统计 动态列导出
     *
     * @param sobIds
     * @param user
     * @return
     */
    @Override
    public List<BaseHeaderDto> getDayHeadersByDynamicForExport(String sobIds, UserInfo user) {
        List<BaseHeaderDto> head = new ArrayList<>();
        head.add(new BaseHeaderDto("analyze_result", "ro", "考勤状态"));
        head.add(new BaseHeaderDto("belong_date", "ro", "考勤日期"));
        head.add(new BaseHeaderDto("emp_name", "ro", "姓名"));
        initShowHeadList("DAILYSUM", head, user);
        return head;
    }

    /**
     * 处理动态列表头，如果不存在checkList 就找默认初始化的json字段
     */
    private void initShowHeadList(String code, List<BaseHeaderDto> head, UserInfo userInfo) {
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            securityUserInfo.setUserId(userInfo.getUserId());
            securityUserInfo.setTenantId(userInfo.getTenantId());
            securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            //应该展示的字段
            val userDynamicSet = dynamicFeignClient.userDynamicTableLoad(code).getData();
            val dynamicSet = dynamicFeignClient.dynamicTableLoad(code).getData();
            List<String> checkedList = userDynamicSet.getCheckedList();
            for (Map column : userDynamicSet.getColumns()) {
                if (checkedList.contains(column.get("key").toString())) {
                    head.add(new BaseHeaderDto(column.get("key").toString(), "ro", ((Map<String, Object>) column.get("props")).get("title").toString()));
                }
            }
            if (checkedList == null || checkedList.size() == 0) {
                List<String> standardList;
                if (code.equals("DAILYSUM")) {
                    standardList = day;
                } else if (code.equals("MOUTHLYSUM")) {
                    standardList = month;
                } else {
                    standardList = advance;
                }
                for (MetadataPropertyDto metadataPropertyDto : dynamicSet) {
                    if (standardList.contains(metadataPropertyDto.getProperty())) {
                        head.add(new BaseHeaderDto(metadataPropertyDto.getProperty(), "ro", metadataPropertyDto.getName()));
                    }
                }
            }
        } catch (Exception e) {
            log.error("initShowHeadList execute fail,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
    }

    @Override
    public List<String> getMonthHeaders(Integer waGroupId, ImmutablePair<Long, Long> summaryPeriod) {
        UserInfo userInfo = getUserInfo();
        String tenantId;
        if (userInfo != null) {
            tenantId = userInfo.getTenantId();
        } else {
            tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        /*基本信息*/
        List<String> list = Lists.newArrayList(
                "{\"id\": \"status\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"考勤状态\",\"filter\":{\"type\":\"str\"}}",
                "{\"id\": \"emp_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"姓名\",\"filter\":{\"type\":\"str\"}}",
                "{\"id\": \"org_name\",\"width\":\"200px\",\"type\":\"ro\",\"sort\":\"str\",\"value\":\"" + "任职组织" + "\",\"filter\":{\"type\":\"combo\",\"name\":\"orgid\",\"attach\":{\"type\":\"tree\",\"config\":{\"connector\":\"org/getOrgTreeJson\",\"lazy\":\"org/getSubOrgTreeJson\"}}},\"ellipsis\":true}",
                "{\"id\": \"org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "组织全路径" + "\"}",
                "{\"id\": \"ax_org_full_path\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "AX组织全路径" + "\"}",
                "{\"id\": \"employ_type_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工类型\",\"filter\":{\"type\":\"str\"}}",
                "{\"id\": \"hire_date\", \"width\": \"150px\", \"type\": \"date\", \"value\": \"入职日期\",\"filter\":{\"type\":\"str\"}}",
                "{\"id\": \"emp_status_name\", \"width\": \"150px\", \"type\": \"ro\", \"value\": \"员工状态\",\"filter\":{\"type\":\"str\"}}"
        );
        /*考勤分析字段*/
        List<String> staticList = Lists.newArrayList(
                "{\"id\": \"wa_group_name\", \"width\": \"200px\", \"type\": \"ro\", \"value\": \"" + "考勤方案" + "\"}",
                // 应出勤时长（单位分钟，后端转为小时返回给前端）
                "{\"id\": \"work_time\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(小时)" + "\"}",
                // 应出勤时长（单位天）
                "{\"id\": \"work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(天)" + "\"}",
                // 实际出勤时长（单位分钟）
                "{\"id\": \"register_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(小时)" + "\"}",
                // 实际出勤时长（单位天）
                "{\"id\": \"register_time_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(天)" + "\"}",
                "{\"id\": \"actual_work_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际工作时长(小时)" + "\"}",
                "{\"id\": \"leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（小时）" + "\"}",
                "{\"id\": \"leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（天）" + "\"}",
                "{\"id\": \"full_paid_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（小时）" + "\"}",
                "{\"id\": \"full_paid_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（天）" + "\"}",
                "{\"id\": \"deduction_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（小时）" + "\"}",
                "{\"id\": \"deduction_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（天）" + "\"}",
                "{\"id\": \"travel_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（小时）" + "\"}",
                "{\"id\": \"travel_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（天）" + "\"}",
                "{\"id\": \"latecount\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "迟到次数" + "\"}",
                "{\"id\": \"late_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到时长(分钟)" + "\"}",
                "{\"id\": \"earlycount\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "早退次数" + "\"}",
                "{\"id\": \"early_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退时长(分钟)" + "\"}",
                "{\"id\": \"is_kg\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "旷工次数" + "\"}",
                "{\"id\": \"kg_work_time_minute\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "旷工时长(分钟)" + "\"}",
                "{\"id\": \"kg_work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "旷工天数" + "\"}",
                "{\"id\": \"bdk_count\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "补卡次数" + "\"}",
                "{\"id\": \"compensatory\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "调休转付现" + "\"}"
        );
        if (summaryPeriod != null) {
            Long startDate = summaryPeriod.getLeft();
            Long endDate = summaryPeriod.   getRight();
            do {
                final String labelKey = String.format("summary_%s", startDate);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd EEEE", Locale.CHINESE);
                final String labelName = dateFormat.format(startDate * 1000);
                list.add("{\"id\": \"" + labelKey + "\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + labelName + "\"}");
                startDate = com.caidaocloud.attendance.service.infrastructure.util.DateUtil.getNextDayInstant(startDate);
            } while (startDate <= endDate);
        }
        list.addAll(staticList);
        // 请假类型、关联当前考勤方案
        List<Map> mapList = waConfigService.listLeaveTypeDef(tenantId, SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> list.add("{\"id\": \"lt_" + lt.get("value") + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + lt.get("text") + "\"}"));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(tenantId);
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> list.add("{\"id\": \"tr_" + lt.getTravelTypeId() + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()) + "\"}"));
        }
        //加班类型
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(tenantId, null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            list.add("{\"id\": \"" + columnName + "\", \"width\": \"160px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()) + "\"}");
        }
        return list;
    }

    @Override
    public List<BaseHeaderDto> getMonthHeadersForExport(Integer waGroupId, UserInfo user, ImmutablePair<Long, Long> summaryPeriod) {
        List<BaseHeaderDto> head = new ArrayList<>();
        head.add(new BaseHeaderDto("status", "ro", "考勤状态"));
        head.add(new BaseHeaderDto("workno", "ro", "账号"));
        head.add(new BaseHeaderDto("emp_name", "ro", "姓名"));
        head.add(new BaseHeaderDto("org_name", "ro", "任职组织"));
        head.add(new BaseHeaderDto("org_full_path", "ro", ""));
        head.add(new BaseHeaderDto("ax_org_full_path", "ro", "AX组织全路径"));
        head.add(new BaseHeaderDto("employ_type_name", "ro", "员工类型"));
        head.add(new BaseHeaderDto("hire_date", "date", "入职日期"));
        head.add(new BaseHeaderDto("emp_status_name", "ro", "员工状态"));
        if (summaryPeriod != null) {
            Long startDate = summaryPeriod.getLeft();
            Long endDate = summaryPeriod.getRight();
            do {
                final String labelKey = String.format("summary_%s", startDate);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd EEEE", Locale.CHINESE);
                final String labelName = dateFormat.format(startDate * 1000);
                head.add(new BaseHeaderDto(labelKey, "ro", labelName));
                startDate = com.caidaocloud.attendance.service.infrastructure.util.DateUtil.getNextDayInstant(startDate);
            } while (startDate <= endDate);
        }

        head.add(new BaseHeaderDto("wa_group_name", "ro", "考勤方案"));
        head.add(new BaseHeaderDto("work_time", "ro", "应出勤时长(小时)"));
        head.add(new BaseHeaderDto("work_time_day", "ro", "应出勤时长(天)"));
        head.add(new BaseHeaderDto("register_time", "ro", "实际出勤时长(小时)"));
        head.add(new BaseHeaderDto("register_time_day", "ro", "实际出勤时长(天)"));
        head.add(new BaseHeaderDto("actual_work_time", "ro", "实际工作时长(小时)"));
        head.add(new BaseHeaderDto("leave_duration", "ro", "请休假总时长（小时）"));
        head.add(new BaseHeaderDto("leave_duration_day", "ro", "请休假总时长（天）"));
        head.add(new BaseHeaderDto("full_paid_leave_duration", "ro", "全薪假期总时长（小时）"));
        head.add(new BaseHeaderDto("full_paid_leave_duration_day", "ro", "全薪假期总时长（天）"));
        head.add(new BaseHeaderDto("deduction_leave_duration", "ro", "扣薪假期总时长（小时）"));
        head.add(new BaseHeaderDto("deduction_leave_duration_day", "ro", "扣薪假期总时长（天）"));
        head.add(new BaseHeaderDto("travel_duration", "ro", "出差总时长（小时）"));
        head.add(new BaseHeaderDto("travel_duration_day", "ro", "出差总时长（天）"));
        head.add(new BaseHeaderDto("latecount", "ro", "迟到次数"));
        head.add(new BaseHeaderDto("late_time_minute", "ro", "迟到时长(分钟)"));
        head.add(new BaseHeaderDto("earlycount", "ro", "早退次数"));
        head.add(new BaseHeaderDto("early_time_minute", "ro", "早退时长(分钟)"));
        head.add(new BaseHeaderDto("is_kg", "ro", "旷工次数"));
        head.add(new BaseHeaderDto("kg_work_time_minute", "ro", "旷工时长(分钟)"));
        head.add(new BaseHeaderDto("kg_work_time_day", "ro", "旷工天数"));
        head.add(new BaseHeaderDto("bdk_count", "ro", "补卡次数"));
        // 请假类型、关联考勤方案
        UserInfo userInfo = user != null ? user : getUserInfo();
        List<Map> mapList = waConfigService.listLeaveTypeDef(userInfo.getTenantId(), SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> head.add(new BaseHeaderDto("lt_" + lt.get("value") + "_key", "ro", String.valueOf(lt.get("text")))));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> head.add(new BaseHeaderDto("tr_" + lt.getTravelTypeId() + "_key", "ro", LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()))));
        }
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            head.add(new BaseHeaderDto(columnName, "ro", LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName())));
        }
        return head;
    }

    @Override
    public List<BaseHeaderDto> getMonthHeadersByDynamicForExport(Integer waGroupId, UserInfo user, ImmutablePair<Long, Long> summaryPeriod) {
        List<BaseHeaderDto> head = new ArrayList<>();
        head.add(new BaseHeaderDto("status", "ro", "考勤状态"));
        head.add(new BaseHeaderDto("emp_name", "ro", "姓名"));
        //应该展示的字段
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            securityUserInfo.setUserId(user.getUserId());
            securityUserInfo.setTenantId(user.getTenantId());
            securityUserInfo.setEmpId(user.getEmpid() != null ? user.getEmpid().longValue() : 0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            //应该展示的字段
            initShowHeadList("MOUTHLYSUM", head, user);
        } catch (Exception e) {
            log.error("dynamicFeignClient execute fail,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return head;
    }

    public List<BaseHeaderDto> getMonthAdvanceHeadersByDynamicForExport(Integer waGroupId, UserInfo user, ImmutablePair<Long, Long> summaryPeriod) {
        List<BaseHeaderDto> head = new ArrayList<>();
        head.add(new BaseHeaderDto("status", "ro", "考勤状态"));
        head.add(new BaseHeaderDto("emp_name", "ro", "姓名"));
        //应该展示的字段
        initShowHeadList("MOUTHLYADVANCET", head, user);
        if (summaryPeriod != null) {
            Long startDate = summaryPeriod.getLeft();
            Long endDate = summaryPeriod.getRight();
            do {
                final String labelKey = String.format("summary_%s", startDate);
                SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd EEEE", Locale.CHINESE);
                final String labelName = dateFormat.format(startDate * 1000);
                head.add(new BaseHeaderDto(labelKey, "ro", labelName));
                startDate = com.caidaocloud.attendance.service.infrastructure.util.DateUtil.getNextDayInstant(startDate);
            } while (startDate <= endDate);
        }
        return head;
    }

    @Override
    public List<KeyValue> searchMonthHeaderListForDynamic(Integer waGroupId, UserInfo userInfo) {
        List<KeyValue> result = new ArrayList<>();
        List<BaseHeaderDto> monthHeadersByDynamicForExport = getMonthHeadersByDynamicForExport(null, userInfo, null);
        for (BaseHeaderDto headerDto : monthHeadersByDynamicForExport) {
            KeyValue keyValue = new KeyValue();
            if (headerDto.getId().startsWith("ot")) {
                String replaceValue = headerDto.getId().replace("@", "_simple@");
                keyValue.setValue(replaceValue);
            } else {
                keyValue.setValue(headerDto.getId());
            }
            keyValue.setText(headerDto.getValue());
            result.add(keyValue);
        }
        return result;
    }

    @Override
    public List<KeyValue> getDayAnalyseHeaderListForDynamic(UserInfo userInfo) {
        List<KeyValue> result = new ArrayList<>();
        List<BaseHeaderDto> dayHeadersByDynamicForExport = getDayHeadersByDynamicForExport(null, userInfo);
        for (BaseHeaderDto headerDto : dayHeadersByDynamicForExport) {
            KeyValue keyValue = new KeyValue();
            if (headerDto.getId().startsWith("ot")) {
                String replaceValue = headerDto.getId().replace("@", "_simple@");
                keyValue.setValue(replaceValue);
            } else {
                keyValue.setValue(headerDto.getId());
            }
            keyValue.setText(headerDto.getValue());
            result.add(keyValue);
        }
        return result;
    }


    @Async
    @Override
    public void notification(AttendanceSummaryNotify dto, UserInfo userInfo, Locale locale) {
        ResponseWrap.setThreadLocale(locale);
        try {
            if (dto.getEmpIds() == null || dto.getEmpIds().isEmpty()) {
                LogRecordContext.putVariable("content", "发送了所有成员考勤结果");
            } else {
                List<SysEmpInfo> sysEmpInfos = sysEmpInfoMapper.selectBatchIds(dto.getEmpIds());
                StringBuilder builder = new StringBuilder();
                sysEmpInfos.forEach(st -> builder.append(st.getWorkno()).append("(").append(st.getEmpName()).append(")").append("、"));
                if (builder.length() > 1) {
                    builder.deleteCharAt(builder.length() - 1);
                }
                LogRecordContext.putVariable("content", builder.toString());
            }
            log.info("Start to sending attendance result message reminder，params：[{}]", FastjsonUtil.toJson(dto));
            MonthAnalysePageDto monthAnalysePageDto = getMonthAnalysePageDto(dto);
            sendMsg(monthAnalysePageDto, userInfo);
            log.info("Send attendance result message to remind finished");
        } finally {
            ResponseWrap.clearThreadLocale();
        }
    }

    @Override
    public WaStatisticsDto asyncRegister(String belongId, Long startDate, Long endDate,
                                         Long[] empIds, Integer waSobId, String dataScope,
                                         Long userId, boolean isJob) throws Exception {
        // 分析签到签退数据
        long send = endDate;
        Long currentDate = endDate;
        if (currentDate == null) {
            currentDate = DateUtil.getOnlyDate(new Date());
        }
        if (endDate != null) {
            int m = (23 * 60 * 60) + (59 * 60) + 59;
            endDate += m;
        }
        Integer row;
        if (startDate == null || endDate == null) {
            startDate = DateUtil.addDate(DateUtil.getOnlyDate(new Date()) * 1000, -1);
            endDate = startDate;
            if (endDate != null) {
                int m = (23 * 60 * 60) + (59 * 60) + 59;
                endDate += m;
            }
        }
        Boolean isDefault = false;
        Integer waGroupId;
        Long sobEndDate;
        WaStatisticsDto result = new WaStatisticsDto();
        result.setStartDate(startDate);
        result.setEndDate(endDate);
        if (null != empIds && empIds.length > 0) {
            result.setEmpList(Arrays.asList(empIds));
        }
        if (waSobId != null) {
            WaSob waSob = waSobMapper.selectByPrimaryKey(waSobId);
            if (null == waSob) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.WA_SOB_NOT_EXIST, null).getMsg());
            }
            if (waSob.getStatus() != null && waSob.getStatus() == 1) {
                throw new CDException(ResponseWrap.wrapResult(AttendanceCodes.SEALED_SOB_CAN_NOT_RECALCULATED, null).getMsg());
            }
            if (startDate < waSob.getStartDate() || (send > waSob.getEndDate())) {
                String normalStart = DateUtil.getDateStrByTimesamp(waSob.getStartDate());
                String normalEnd = DateUtil.getDateStrByTimesamp(waSob.getEndDate());
                throw new CDException(String.format(ResponseWrap.wrapResult(AttendanceCodes.ACCOUNT_RANGE_BETWEEN, null).getMsg(), normalStart, normalEnd));
            }
            // 2.判断当前的考勤分组是否是默认的
            WaGroup waGroup = waGroupMapper.selectByPrimaryKey(waSob.getWaGroupId());
            waGroupId = waGroup.getWaGroupId();
            if (waGroup.getIsDefault() != null) {
                isDefault = waGroup.getIsDefault();
            }
            boolean includeInProgress = waGroup.getLeaveStatus() != null && waGroup.getLeaveStatus() == 2;
            sobEndDate = waSob.getSobEndDate();
            WaParseGroup parseGroup = waParseGroupMapper.selectByPrimaryKey(waGroup.getParseGroupId());
            // 1 按人分批分析 2 按门店和非门店分类
            Integer tmType2 = 1;
            List<Long> empGroupEmpIds = waSobDo.getEmpIdListByGroup(belongId, currentDate, isDefault, waGroupId, startDate, endDate, dataScope);
            if (empIds != null && empIds.length > 0) {
                List<Long> selectEmpIds = new ArrayList<>();
                for (Long id : empIds) {
                    if (empGroupEmpIds.contains(id)) {
                        selectEmpIds.add(id);
                    }
                }
                empGroupEmpIds.clear();
                empGroupEmpIds.addAll(selectEmpIds);
            }
            result.setEmpList(empGroupEmpIds);
            // 如果启用了按帐套区间来控制加班，请假时间是否落再当天，默认是落在审批日期上
            waCommonService.waGroupAnaly(waGroupId, startDate, endDate, sobEndDate);
            row = analyzeResultCalculateService.analyzeStep1(belongId, startDate, endDate, tmType2, empGroupEmpIds, parseGroup, waSob, userId, isJob, includeInProgress);
        } else {
            AnalyzeResultCalculateDto calculateDto = new AnalyzeResultCalculateDto();
            calculateDto.setBelongid(belongId);
            calculateDto.setUserId(userId);
            calculateDto.setEmpids(empIds == null ? null : Arrays.stream(empIds).collect(Collectors.toList()));
            calculateDto.setStartDate(startDate);
            calculateDto.setEndDate(endDate);
            row = analyzeResultCalculateService.analysisWaRegisterStep(calculateDto);
        }
        log.info("=====================核算完成=============" + row);
        result.setRow(row);
        return result;
    }

    @Override
    public List<AnalyzeCalendarDto> getAnalyzeCalendarList(Long empId, Integer searchMonth, String dataScope) {
        UserInfo userInfo = getUserInfo();
        searchMonth = null == searchMonth ? DateUtilExt.getCurrentYearMonth() : searchMonth;
        empId = null == empId ? userInfo.getStaffId() : empId;
        Map<String, String> map = DateUtilExt.getMothDay(searchMonth);
        String start = map.get("start");
        String end = map.get("end");

        // 员工排班
        List<CalendarEventDto> calendarList = waCommonService.getEmpCalendarShiftListByYm(userInfo.getTenantId(),
                empId, start, end, dataScope);
        if (CollectionUtils.isEmpty(calendarList)) {
            return null;
        }
        List<Map> shiftList = calendarList.stream().map(e -> {
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("dateType", e.getDateType());
            shiftMap.put("workDate", DateUtilExt.getDateByStr(e.getStart()));
            shiftMap.put("shiftDefName", DateTypeEnum.getName(e.getDateType()));
            return shiftMap;
        }).collect(Collectors.toList());

        Map<String, Long> monthDate = DateUtilExt.getMonthDateByYm(searchMonth);
        Long startDate = monthDate.get("start");
        Long endDate = monthDate.get("end");

        //休假记录
        List<WaLeaveDaytimeDo> empLeaveDayList = waLeaveDaytimeDo.getEmpLeaveDay(empId, startDate, endDate,
                new ArrayList<>(Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(),
                        ApprovalStatusEnum.PASSED.getIndex())));
        //出差记录
        List<WaEmpTravelDaytimeDo> empTravelDayList = empTravelDaytimeDo.getTravelDayTimeList(userInfo.getTenantId(), empId, startDate, endDate);
        //加班记录
        List<WaEmpOvertimeDo> empOvertimeList = waEmpOvertimeDo.getEmpOvertimeListByYmdDate(empId, startDate, endDate);
        //补卡
        List<WaRegisterRecordDo> recordList = waRegisterRecordDo.getEmpRegisterRecordList(empId, startDate, endDate,
                new ArrayList<>(Collections.singletonList(ClockWayEnum.FILLCLOCK.getIndex())), new ArrayList<>(Arrays.asList(ApprovalStatusEnum.IN_APPROVAL.getIndex(),
                        ApprovalStatusEnum.PASSED.getIndex())));
        //考勤统计
        List<WaAnalyzeDo> list = waAnalyzeDo.getWaAnaLyzeList(userInfo.getTenantId(), empId, startDate, endDate);
        List<AnalyzeCalendarDto> dateDtoList = BeanMapUtils.mapToBean(shiftList, AnalyzeCalendarDto.class);
        for (AnalyzeCalendarDto analyzeCalendarDto : dateDtoList) {
            analyzeCalendarDto.setEmpId(empId);
            List<WaResultDto> walist = new ArrayList<>();
            //迟到、早退、旷工
            for (WaAnalyzeDo analyzeDo : list) {
                WaResultDto dto;
                String belongDate = DateUtil.convertDateTimeToStr(analyzeDo.getBelongDate(), "yyyyMMdd", true);
                if (belongDate.equals(analyzeCalendarDto.getWorkDate())) {
                    if (analyzeDo.getLateTime() > 0) {
                        dto = new WaResultDto();
                        dto.setId(Long.valueOf(CalendarStatusEnum.LATE.getIndex()));
                        dto.setText(CalendarStatusEnum.getName(CalendarStatusEnum.LATE.getIndex()));
                        walist.add(dto);
                    }
                    if (analyzeDo.getEarlyTime() > 0) {
                        dto = new WaResultDto();
                        dto.setId(Long.valueOf(CalendarStatusEnum.EARLY.getIndex()));
                        dto.setText(CalendarStatusEnum.getName(CalendarStatusEnum.EARLY.getIndex()));
                        walist.add(dto);
                    }
                    if (analyzeDo.getKgWorkTime() > 0) {
                        dto = new WaResultDto();
                        dto.setId(Long.valueOf(CalendarStatusEnum.ABSENT.getIndex()));
                        dto.setText(CalendarStatusEnum.getName(CalendarStatusEnum.ABSENT.getIndex()));
                        walist.add(dto);
                    }
                }
            }
            //休假
            for (WaLeaveDaytimeDo daytimeDo : empLeaveDayList) {
                WaResultDto leave = new WaResultDto();
                String belongDate = DateUtil.convertDateTimeToStr(daytimeDo.getLeaveDate(), "yyyyMMdd", true);
                if (belongDate.equals(analyzeCalendarDto.getWorkDate())) {
                    leave.setId(Long.valueOf(daytimeDo.getLeaveId()));
                    leave.setText(daytimeDo.getLeaveName());
                    walist.add(leave);
                }
            }
            //出差
            for (WaEmpTravelDaytimeDo empTravelDayTime : empTravelDayList) {
                WaResultDto travel = new WaResultDto();
                String belongDate = DateUtil.convertDateTimeToStr(empTravelDayTime.getTravelDate(), "yyyyMMdd", true);
                if (belongDate.equals(analyzeCalendarDto.getWorkDate())) {
                    travel.setId(empTravelDayTime.getTravelId());
                    travel.setText(empTravelDayTime.getTravelType());
                    walist.add(travel);
                }
            }
            //加班
            Map<String, String> overTimeMap = new HashMap<>();
            for (WaEmpOvertimeDo overtimeDo : empOvertimeList) {
                WaResultDto overtime = new WaResultDto();
                String belongDate = DateUtil.convertDateTimeToStr(overtimeDo.getStartTime(), "yyyyMMdd", true);
                if (belongDate.equals(analyzeCalendarDto.getWorkDate())) {
                    if (overTimeMap.get(belongDate) == null) {
                        overTimeMap.put(belongDate, CalendarStatusEnum.getName(CalendarStatusEnum.OVERTIME.getIndex()));
                    } else {
                        continue;
                    }
                    overtime.setId(Long.valueOf(CalendarStatusEnum.OVERTIME.getIndex()));
                    overtime.setText(CalendarStatusEnum.getName(CalendarStatusEnum.OVERTIME.getIndex()));
                    walist.add(overtime);
                }
            }
            //补卡
            Map<String, String> patchMap = new HashMap<>();
            for (WaRegisterRecordDo waRegisterRecordDo : recordList) {
                WaResultDto patch = new WaResultDto();
                String belongDate = DateUtil.convertDateTimeToStr(waRegisterRecordDo.getBelongDate(), "yyyyMMdd", true);
                if (belongDate.equals(analyzeCalendarDto.getWorkDate())) {
                    if (patchMap.get(belongDate) == null) {
                        patchMap.put(belongDate, CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
                    } else {
                        continue;
                    }
                    patch.setId(Long.valueOf(CalendarStatusEnum.PATCH.getIndex()));
                    patch.setText(CalendarStatusEnum.getName(CalendarStatusEnum.PATCH.getIndex()));
                    walist.add(patch);
                }
            }
            analyzeCalendarDto.setWaResult(walist);
        }

        return dateDtoList;
    }

    /**
     * 考勤结果提醒
     *
     * @param dto
     * @param userInfo
     */
    private void sendMsg(MonthAnalysePageDto dto, UserInfo userInfo) {
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        List<Map> list = this.searchRegisterStatistics(dto, pageBean, userInfo);
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        WaSobDo sob = waSobDo.getSobByGroupIdAndPeriod(userInfo.getTenantId(), dto.getGroupId(), dto.getYm());
        Long startDate;
        Long endDate;
        if (dto.getStartDate() != null && dto.getEndDate() != null) {
            startDate = dto.getStartDate();
            endDate = dto.getEndDate();
        } else {
            if (null == sob) {
                return;
            }
            startDate = sob.getStartDate();
            endDate = sob.getEndDate();
        }
        // 查询公司出差规则
        List<String> travelTypeKeyList = null;
        List<WaTravelTypeDo> travelTypeList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(travelTypeList)) {
            travelTypeKeyList = travelTypeList.stream().map(o -> "lt_" + o.getTravelTypeId() + "_key").collect(Collectors.toList());
        }
        for (Map map : list) {
            List<Long> receiver = new ArrayList<>();
            receiver.add((Long) map.get("empid"));
            MessageParams.MessageParamsBuilder builder = MessageParams.builder()
                    .type("msg")
                    .subType("attSummary")
                    .msgNotice(Boolean.TRUE)
                    .emailNotice(Boolean.TRUE)
                    .title("考勤汇总提醒")
                    .funcType("-1")
                    .handlers(receiver)
                    .customForms(getStatisticResult(startDate, endDate, map, travelTypeKeyList))
                    .nType(NoticeType.ATTENDANCE_MONTH_REPORT_MSG)
                    .tenantId(userInfo.getTenantId());
            MessageParams messageParams = builder.build();
            String jsonStr = JSON.toJSONString(messageParams);
            attendanceWorkflowMsgPublish.publish(jsonStr, Long.valueOf(userInfo.getTenantId()));
        }
        if (list.size() < PAGE_SIZE) {
            return;
        }
        dto.setPageNo(dto.getPageNo() + 1);
        sendMsg(dto, userInfo);
    }

    private MonthAnalysePageDto getMonthAnalysePageDto(AttendanceSummaryNotify dto) {
        MonthAnalysePageDto monthAnalysePageDto = new MonthAnalysePageDto();
        monthAnalysePageDto.setPageSize(PAGE_SIZE);
        monthAnalysePageDto.setGroupId(dto.getGroupId());
        monthAnalysePageDto.setYm(dto.getYm());
        monthAnalysePageDto.setEmpIds(dto.getEmpIds());
        monthAnalysePageDto.setStartDate(dto.getStartDate());
        monthAnalysePageDto.setEndDate(dto.getEndDate());
        return monthAnalysePageDto;
    }

    private List<KeyValue> getStatisticResult(Long startDate, Long endDate, Map map, List<String> travelTypeKeyList) {
        List<KeyValue> list = new ArrayList<>();
        list.add(new KeyValue("考勤开始时段", DateUtil.getDateStrByTimesamp(startDate)));
        list.add(new KeyValue("考勤结束时段", DateUtil.getDateStrByTimesamp(endDate)));
        list.add(new KeyValue("汇总内容", getContent(map, travelTypeKeyList)));
        return list;
    }

    private String getContent(Map map, List<String> travelTypeKeyList) {
        String lateCount = map.get("latecount") == null ? "0" : map.get("latecount").toString();
        String absenteeismCount = map.get("is_kg") == null ? "0" : map.get("is_kg").toString();
        String earlyCount = map.get("earlycount") == null ? "0" : map.get("earlycount").toString();
        BigDecimal leaveDayTime = BigDecimal.ZERO;
        BigDecimal leaveHourTime = BigDecimal.ZERO;
        BigDecimal travelDayTime = BigDecimal.ZERO;
        BigDecimal travelHourTime = BigDecimal.ZERO;

        for (Object mapKey : map.keySet()) {
            String key = mapKey.toString();
            if (key.startsWith("lt_") && map.get(mapKey) != null && map.containsKey(key + "_unit") && map.get(key + "_unit") != null) {
                if (travelTypeKeyList.contains(key)) {
                    // 出差
                    if ((Integer) map.get(key + "_unit") == 2) {
                        // 小时
                        travelHourTime = travelHourTime.add(new BigDecimal(map.get(mapKey).toString()));
                    } else {
                        // 天
                        travelDayTime = travelDayTime.add(new BigDecimal(map.get(mapKey).toString()));
                    }
                } else {
                    // 休假
                    if ((Integer) map.get(key + "_unit") == 2) {
                        leaveHourTime = leaveHourTime.add(new BigDecimal(map.get(mapKey).toString()));
                    } else {
                        leaveDayTime = leaveDayTime.add(new BigDecimal(map.get(mapKey).toString()));
                    }
                }
            }
        }
        return String.format("迟到%s次，休假%s天%s小时，出差%s天%s小时，旷工%s次，早退%s次", lateCount, leaveDayTime.toPlainString(), leaveHourTime.toPlainString(), travelDayTime.toPlainString(), travelHourTime.toPlainString(), absenteeismCount, earlyCount);
    }

    @Override
    public PageResult<DayAnalyseAbnormalDto> getDayAnalyseAbnormalList(DayAnalyseAbnormalPageDto dto, UserInfo userInfo) {
        Map<String, Object> paramsMap = new HashMap<>();
        String tenantId = userInfo.getTenantId();
        paramsMap.put("tenantId", tenantId);
        paramsMap.put("startDate", dto.getStartDate());
        paramsMap.put("endDate", dto.getEndDate());
        if (StringUtils.isNotBlank(dto.getKeywords())) {
            paramsMap.put("keywords", dto.getKeywords());
        }
        if (CollectionUtils.isNotEmpty(dto.getWaGroupIds())) {
            paramsMap.put("waGroupIds", dto.getWaGroupIds());
        }
        PageBean pageBean = PageUtil.getNewPageBean(dto);
        String filter = pageBean.getFilter();
        if (filter != null && filter.contains("orgid")) {
            filter = filter.replaceAll("\"orgid\"\\s+=\\s+'(\\d+)'", "orgid IN (SELECT * FROM getsuborgstr('{$1}'))");
        }
        if (StringUtils.isNotBlank(filter)) {
            filter += " and \"analyzeResult\" = 1";
        } else {
            filter = " and \"analyzeResult\" = 1";
        }
        paramsMap.put("filter", filter);
        paramsMap.put("datafilter", dto.getDataScope());
        String sortString = pageBean.getOrder();
        MyPageBounds pageBounds = new MyPageBounds(pageBean.getPosStart(), pageBean.getCount(), Order.formString(sortString));
        PageList<Map> pageList = waAnalyzeDo.selectWaAnalyseList(pageBounds, paramsMap);
        if (CollectionUtils.isEmpty(pageList)) {
            return new PageResult<>(new ArrayList<>(), pageBean.getPageNo(), pageBean.getPageSize(), 0);
        }
        Map<Object, String> employTypeMap = new HashMap<>();
        Map<Object, String> empStatusMap = new HashMap<>();
        List<DayAnalyseAbnormalDto> list = new ArrayList<>();
        for (Map map : pageList) {
            DayAnalyseAbnormalDto abnormalDto = new DayAnalyseAbnormalDto();
            abnormalDto.setAnalyzeId((Integer) map.get("analyzeId"));
            abnormalDto.setAnalyzeResult((Integer) map.get("analyzeResult"));
            Long belongDate = (Long) map.get("belongDate");
            map.put("belong_date", belongDate);
            abnormalDto.setBelongDate(belongDate);
            if (belongDate != null) {
                String week = DateUtil.getWeekLangCode(new Date(belongDate * 1000));
                if (StringUtils.isNotBlank(week)) {
                    String lang = StringUtils.isBlank(SessionHolder.getLang()) ? "zh_CN" : SessionHolder.getLang();
                    abnormalDto.setBelongDateWeek(messageResource.getMessage(week, new Object[]{}, new Locale(lang)));
                }
            }
            abnormalDto.setEmpName(map.get("workno") + "(" + map.get("empName") + ")");
            String orgName = (String) map.get("orgName");
            if (com.caidaocloud.util.StringUtil.isNotBlank(orgName)) {
                abnormalDto.setOrgName(orgName);
            }
            String fullPath = (String) map.get("fullPath");
            if (com.caidaocloud.util.StringUtil.isNotBlank(fullPath)) {
                abnormalDto.setFullPath(fullPath);
            }
            if (map.containsKey("employType")) {
                if (employTypeMap.containsKey(map.get("employType"))) {
                    abnormalDto.setEmployTypeName(employTypeMap.get(map.get("employType")));
                } else {
                    if (null != map.get("employType")) {
                        String employTypeName = textAspect.getDictCache(map.get("employType").toString(), ResponseWrap.getLocale());
                        employTypeMap.put(map.get("employType"), employTypeName);
                        abnormalDto.setEmployTypeName(employTypeName);
                    }
                }
            }
            abnormalDto.setHireDate((Long) map.get("hireDate"));
            if (map.containsKey("empStatus")) {
                abnormalDto.setEmpStatus((Integer) map.get("empStatus"));
                if (empStatusMap.containsKey(map.get("empStatus"))) {
                    abnormalDto.setEmpStatusName(empStatusMap.get(map.get("empStatus")));
                } else {
                    if (null != map.get("empStatus")) {
                        String empStatusName = textAspect.getEmpStatusEnumText(map.get("empStatus").toString(), tenantId);
                        empStatusMap.put(map.get("empStatus"), empStatusName);
                        abnormalDto.setEmpStatusName(empStatusName);
                    }
                }
            }
            abnormalDto.setShiftDefCode((String) map.get("shiftDefCode"));
            abnormalDto.setShiftDefName((String) map.get("shiftDefName"));
            if (map.containsKey("clock_type")) {
                setClockType(map, tenantId);
                Integer clockType = (Integer) map.get("clock_type");
                if (null != clockType) {
                    abnormalDto.setClockType(ParseGroupClockTypeEnum.getName(clockType));
                }
            }
            abnormalDto.setLeaveName((String) map.get("leaveName"));
            abnormalDto.setApplyDate((Long) map.get("applyDate"));
            abnormalDto.setApprovalStatus((Integer) map.get("approvalStatus"));
            list.add(abnormalDto);
        }
        Paginator paginator = pageList.getPaginator();
        return new PageResult<>(list, pageBean.getPageNo(), pageBean.getPageSize(), paginator.getTotalCount());
    }

    @Override
    public List<KeyValue> getMonthHeadersKeyValue(Integer waGroupId) {
        List<KeyValue> result = new ArrayList<>();
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        /*考勤分析字段*/
        result.add(new KeyValue("考勤方案", "wa_group_name"));
        result.add(new KeyValue("应出勤时长(小时)", "work_time"));
        result.add(new KeyValue("应出勤时长(天)", "work_time_day"));
        result.add(new KeyValue("实际出勤时长(小时)", "register_time"));
        result.add(new KeyValue("实际出勤时长(天)", "register_time_day"));
        result.add(new KeyValue("实际工作时长(小时)", "actual_work_time"));
        result.add(new KeyValue("请休假总时长（小时）", "leave_duration"));
        result.add(new KeyValue("请休假总时长（天）", "leave_duration_day"));
        result.add(new KeyValue("全薪假期总时长（小时）", "full_paid_leave_duration"));
        result.add(new KeyValue("全薪假期总时长（天）", "full_paid_leave_duration_day"));
        result.add(new KeyValue("扣薪假期总时长（小时）", "deduction_leave_duration"));
        result.add(new KeyValue("扣薪假期总时长（天）", "deduction_leave_duration_day"));
        result.add(new KeyValue("出差总时长（小时）", "travel_duration"));
        result.add(new KeyValue("出差总时长（天）", "travel_duration_day"));
        result.add(new KeyValue("迟到次数", "latecount"));
        result.add(new KeyValue("迟到时长(分钟)", "late_time_minute"));
        result.add(new KeyValue("早退次数", "earlycount"));
        result.add(new KeyValue("早退时长(分钟)", "early_time_minute"));
        result.add(new KeyValue("旷工次数", "is_kg"));
        result.add(new KeyValue("旷工时长(分钟)", "kg_work_time_minute"));
        result.add(new KeyValue("旷工天数", "kg_work_time_day"));
        result.add(new KeyValue("补卡次数", "bdk_count"));
        result.add(new KeyValue("调休转付现", "compensatory"));

        // 请假类型、关联当前考勤方案
        List<Map> mapList = waConfigService.listLeaveTypeDef(userInfo.getTenantId(), SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(it -> result.add(new KeyValue(ConvertHelper.stringConvert(it.get("text")), "lt_" + ConvertHelper.stringConvert(it.get("value")) + "_key")));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> result.add(new KeyValue(LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()), "tr_" + lt.getTravelTypeId() + "_key")));
        }
        //加班类型
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            result.add(new KeyValue(LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()), columnName));
        }
        return result;
    }

    @Override
    public List<KeyValue> getDayAnalyseHeaderKeyValue() {
        List<KeyValue> result = new ArrayList<>();
        SecurityUserInfo userInfo = SecurityUserUtil.getSecurityUserInfo();
        /*考勤分析字段*/
        result.add(new KeyValue("考勤日期", "belong_date"));
        result.add(new KeyValue("班次名称", "shift_def_name"));
        result.add(new KeyValue("打卡时间1", "signInTime"));
        result.add(new KeyValue("打卡时间2", "signOffTime"));
        result.add(new KeyValue("应出勤时长(小时)", "work_time"));
        result.add(new KeyValue("应出勤时长(天)", "work_time_day"));
        result.add(new KeyValue("实际出勤时长(小时)", "register_time"));
        result.add(new KeyValue("实际出勤时长(天)", "register_time_day"));
        result.add(new KeyValue("实际工作时长(小时)", "actual_work_time"));
        result.add(new KeyValue("请休假总时长（小时）", "leave_duration"));
        result.add(new KeyValue("请休假总时长（天）", "leave_duration_day"));
        result.add(new KeyValue("全薪假期总时长（小时）", "full_paid_leave_duration"));
        result.add(new KeyValue("全薪假期总时长（天）", "full_paid_leave_duration_day"));
        result.add(new KeyValue("扣薪假期总时长（小时）", "deduction_leave_duration"));
        result.add(new KeyValue("扣薪假期总时长（天）", "deduction_leave_duration_day"));
        result.add(new KeyValue("出差总时长（小时）", "travel_duration"));
        result.add(new KeyValue("出差总时长（天）", "travel_duration_day"));
        result.add(new KeyValue("迟到时长(分钟)", "late_time_minute"));
        result.add(new KeyValue("早退时长(分钟)", "early_time_minute"));
        result.add(new KeyValue("旷工时长(分钟)", "kg_work_time_minute"));
        // 请假类型
        List<Map> mapList = waConfigService.listLeaveTypeDef(userInfo.getTenantId(), SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> result.add(new KeyValue(ConvertHelper.stringConvert(lt.get("text")), "lt_" + lt.get("value") + "_key")));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(userInfo.getTenantId());
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> result.add(new KeyValue(LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()), "tr_" + lt.getTravelTypeId() + "_key")));
        }
        //加班类型
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(userInfo.getTenantId(), null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            result.add(new KeyValue(LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()), columnName));
        }
        return result;
    }

    /**
     * 动态列的 考勤统计月度汇总列表
     */
    @Override
    public DynamicPageDto getMouthAnalyseListForDynamic(MonthAnalysePageDto dto, PageBean pageBean) {
        DynamicPageDto result = new DynamicPageDto();
        PageList<Map<String, Object>> page = (PageList<Map<String, Object>>) searchMonthRegisterStatistics(dto, pageBean, getUserInfo());
        Paginator paginator = page.getPaginator();
        val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("MOUTHLYSUM").getData();
        val dynamicSet = dynamicFeignClient.dynamicTableLoad("MOUTHLYSUM").getData();
        Map<String, Map<String, Object>> collect = page.stream()
                .collect(Collectors.toMap(
                        map -> (String) map.get("bid"),
                        map -> {
                            Map<String, Object> subMap = new HashMap<>();
                            for (Map.Entry<String, Object> entry : map.entrySet()) {
                                if (entry.getKey().startsWith("lt") || entry.getKey().startsWith("tr") || entry.getKey().startsWith("ot")) {
                                    subMap.put(entry.getKey() + "@entity.attendance.MouthlySummary", entry.getValue());
                                } else {
                                    subMap.put(entry.getKey(), entry.getValue());
                                }
                            }
                            return subMap;
                        }
                ));
        if (!page.isEmpty()) {
            List<MetadataPropertyDto> properties = dynamicSet;
            Map<String, List<MetadataPropertyDto>> mappedProperties = com.googlecode.totallylazy.Maps.map();
            for (MetadataPropertyDto property : properties) {
                val split = property.getProperty().split("@");
                val identifier = split[1];
                property.setProperty(split[0]);
                if (mappedProperties.containsKey(identifier)) {
                    mappedProperties.get(identifier).add(property);
                } else {
                    mappedProperties.put(identifier, com.googlecode.totallylazy.Lists.list(property));
                }
            }
            val dataList = page.stream().map(dt -> {
                Map<String, Object> data = BeanMapUtils.beanToMap(dt);
                String empId = dt.get("empid").toString();
                Long time = System.currentTimeMillis();
                addDynamicColumnValue(data, "entity.hr.EmpPrivateInfo",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.EmpWorkInfo",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.EmpWorkOverview",
                        mappedProperties, time, empId, "empId");
                addDynamicColumnValue(data, "entity.hr.LastContract",
                        mappedProperties, time, empId, "owner$empId");
                data.put("bid", dt.get("bid"));
                data.putAll(collect.get(dt.get("bid")));
                return data;
            }).collect(Collectors.toList());
            List<DataSimple> dataSimples = new ArrayList<>();
            for (Map map : page) {
                DataSimple dataSimple = of(map);
                dataSimple.setBid(map.get("bid").toString());
                dataSimples.add(dataSimple);
            }
            for (Map<String, Object> map : dataList) {
                String flagBid = map.get("bid").toString();
                DataSimple dataObj = new DataSimple();
                dataObj.setBid(flagBid);
                for (DataSimple dataSimple : dataSimples) {
                    if ((dataSimple.getBid()).equals(dataObj.getBid())) {
                        mappedProperties.get("entity.attendance.MouthlySummary").forEach(property -> {
                            Object value = dataSimple.getProperties().get(property.getProperty());
                            if (property.getDataType() != null && property.getDataType().equals("Timestamp")) {
                                SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
                                value = simple.format(value);
                            }
                            map.put(property.getProperty() + "@entity.attendance.MouthlySummary", value);
                        });
                    }
                }
            }
            dataList.forEach(this::replaceDisplay);
            result.setPageData(new PageResult(dataList, pageBean.getPageNo(), pageBean.getPageSize(), page.size()));
        }
        result.setUserDynamicConfig(userDynamicSet);
        result.setDynamicConfig(dynamicSet);
        List<Map> items;
        if (result.getPageData() != null) {
            if (result.getPageData().getItems() != null) {
                items = result.getPageData().getItems();
                for (Map map : items) {
                    if (!"enabled".equals(postTxtShowCode)) {
                        String postTxt = (String) map.get("<EMAIL>");
                        if (StringUtils.isNotEmpty(postTxt) && postTxt.contains("(")) {
                            postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                            map.put("<EMAIL>", postTxt);
                        }
                    }
                }
                result.setPageData(new PageResult<>(items, paginator.getPage(), paginator.getLimit(), paginator.getTotalCount()));
                return result;
            }
        }
        if (paginator == null) {
            result.setPageData(new PageResult<>(null, dto.getPageNo(), dto.getPageSize(), 0));
        } else {
            result.setPageData(new PageResult<>(null, paginator.getPage(), paginator.getLimit(), 0));
        }
        return result;
    }

    @Override
    public DynamicPageDto getMouthAnalyseListForDynamic(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo) {
        DynamicPageDto result = new DynamicPageDto();
        PageList<Map<String, Object>> page = (PageList<Map<String, Object>>) searchMonthRegisterStatistics(dto, pageBean, userInfo);
        Paginator paginator = page.getPaginator();
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            if (userInfo != null) {
                securityUserInfo.setUserId(userInfo.getUserId());
                securityUserInfo.setTenantId(userInfo.getTenantId());
                securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            }
            val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("MOUTHLYSUM").getData();
            val dynamicSet = dynamicFeignClient.dynamicTableLoad("MOUTHLYSUM").getData();
            Map<String, Map<String, Object>> collect = page.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("bid"),
                            map -> {
                                Map<String, Object> subMap = new HashMap<>();
                                for (Map.Entry<String, Object> entry : map.entrySet()) {
                                    if (entry.getKey().startsWith("lt") || entry.getKey().startsWith("tr") || entry.getKey().startsWith("ot")) {
                                        subMap.put(entry.getKey() + "@entity.attendance.MouthlySummary", entry.getValue());
                                    } else {
                                        subMap.put(entry.getKey(), entry.getValue());
                                    }
                                }
                                return subMap;
                            }
                    ));
            if (!page.isEmpty()) {
                List<MetadataPropertyDto> properties = dynamicSet;
                Map<String, List<MetadataPropertyDto>> mappedProperties = com.googlecode.totallylazy.Maps.map();
                for (MetadataPropertyDto property : properties) {
                    val split = property.getProperty().split("@");
                    val identifier = split[1];
                    property.setProperty(split[0]);
                    if (mappedProperties.containsKey(identifier)) {
                        mappedProperties.get(identifier).add(property);
                    } else {
                        mappedProperties.put(identifier, com.googlecode.totallylazy.Lists.list(property));
                    }
                }
                val dataList = page.stream().map(dt -> {
                    Map<String, Object> data = BeanMapUtils.beanToMap(dt);
                    String empId = dt.get("empid").toString();
                    Long time = System.currentTimeMillis();
                    addDynamicColumnValue(data, "entity.hr.EmpPrivateInfo",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkInfo",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkOverview",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.LastContract",
                            mappedProperties, time, empId, "owner$empId");
                    data.put("bid", dt.get("bid"));
                    data.putAll(collect.get(dt.get("bid")));
                    return data;
                }).collect(Collectors.toList());
                List<DataSimple> dataSimples = new ArrayList<>();
                for (Map map : page) {
                    DataSimple dataSimple = of(map);
                    dataSimple.setBid(map.get("bid").toString());
                    dataSimples.add(dataSimple);
                }
                for (Map<String, Object> map : dataList) {
                    String flagBid = map.get("bid").toString();
                    DataSimple dataObj = new DataSimple();
                    dataObj.setBid(flagBid);
                    for (DataSimple dataSimple : dataSimples) {
                        if ((dataSimple.getBid()).equals(dataObj.getBid())) {
                            mappedProperties.get("entity.attendance.MouthlySummary").forEach(property -> {
                                Object value = dataSimple.getProperties().get(property.getProperty());
                                map.put(property.getProperty() + "@entity.attendance.MouthlySummary", value);
                            });
                        }
                    }
                }
                dataList.forEach(this::replaceDisplay);
                result.setPageData(new PageResult(dataList, pageBean.getPageNo(), pageBean.getPageSize(), page.size()));
            }
            result.setUserDynamicConfig(userDynamicSet);
            result.setDynamicConfig(dynamicSet);
        } catch (Exception e) {
            log.error("dynamicFeignClient execute fail,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        List<Map> items;
        if (result.getPageData() != null) {
            if (result.getPageData().getItems() != null) {
                items = result.getPageData().getItems();
                result.setPageData(new PageResult<>(items, paginator.getPage(), paginator.getLimit(), paginator.getTotalCount()));
                return result;
            }
        }
        if (paginator == null) {
            result.setPageData(new PageResult<>(null, dto.getPageNo(), dto.getPageSize(), 0));
        } else {
            result.setPageData(new PageResult<>(null, paginator.getPage(), paginator.getLimit(), 0));
        }
        return result;
    }

    /**
     * 动态列的 考勤统计每日明细列表
     */
    @Override
    public DynamicPageDto getDayAnalyseListForDynamic(DayAnalysePageDto dto, PageBean pageBean) {
        return getDayAnalyseListForDynamic(dto, pageBean, null);
    }

    @Override
    public DynamicPageDto getDayAnalyseListForDynamic(DayAnalysePageDto requestDto, PageBean pageBean, UserInfo userInfo) {
        DynamicPageDto result = new DynamicPageDto();
        PageList<Map<String, Object>> page = (PageList<Map<String, Object>>) getDayAnalyseList(requestDto, pageBean, userInfo);
        Paginator paginator = page.getPaginator();
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            if (userInfo != null) {
                securityUserInfo.setUserId(userInfo.getUserId());
                securityUserInfo.setTenantId(userInfo.getTenantId());
                securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
                SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            }
            val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("DAILYSUM").getData();
            val dynamicSet = dynamicFeignClient.dynamicTableLoad("DAILYSUM").getData();
            //假勤快，出差，加班数据
            Map<String, Map<String, Object>> collect = page.stream().collect(Collectors.toMap(
                    map -> (String) map.get("bid"),
                    map -> {
                        Map<String, Object> subMap = new HashMap<>();
                        for (Map.Entry<String, Object> entry : map.entrySet()) {
                            if (entry.getKey().startsWith("lt") || entry.getKey().startsWith("tr") || entry.getKey().startsWith("ot")) {
                                subMap.put(entry.getKey() + "@entity.attendance.DatalySummary", entry.getValue());
                            } else {
                                subMap.put(entry.getKey(), entry.getValue());
                            }
                        }
                        return subMap;
                    }
            ));
            if (!page.isEmpty()) {
                List<MetadataPropertyDto> properties = dynamicSet;
                Map<String, List<MetadataPropertyDto>> mappedProperties = com.googlecode.totallylazy.Maps.map();
                for (MetadataPropertyDto property : properties) {
                    val split = property.getProperty().split("@");
                    val identifier = split[1];
                    property.setProperty(split[0]);
                    if (mappedProperties.containsKey(identifier)) {
                        mappedProperties.get(identifier).add(property);
                    } else {
                        mappedProperties.put(identifier, com.googlecode.totallylazy.Lists.list(property));
                    }
                }
                val dataList = page.stream().map(dt -> {
                    Map<String, Object> data = BeanMapUtils.beanToMap(dt);
                    String empId = dt.get("empid").toString();
                    Long time = System.currentTimeMillis();
                    addDynamicColumnValue(data, "entity.hr.EmpPrivateInfo", mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkInfo", mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkOverview", mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.LastContract", mappedProperties, time, empId, "owner$empId");
                    data.put("bid", dt.get("bid"));
                    data.putAll(collect.get(dt.get("bid").toString()));
                    return data;
                }).collect(Collectors.toList());
                List<DataSimple> dataSimples = new ArrayList<>();
                for (Map map : page) {
                    DataSimple dataSimple = of(map);
                    dataSimple.setBid(map.get("bid").toString());
                    dataSimples.add(dataSimple);
                }
                for (Map<String, Object> map : dataList) {
                    String flagBid = map.get("bid").toString();
                    DataSimple dataObj = new DataSimple();
                    dataObj.setBid(flagBid);
                    for (DataSimple dataSimple : dataSimples) {
                        if ((dataSimple.getBid()).equals(dataObj.getBid())) {
                            mappedProperties.get("entity.attendance.DatalySummary").forEach(property -> {
                                Object value = dataSimple.getProperties().get(property.getProperty());
                                if (property.getDataType() != null && property.getDataType().equals("Timestamp")) {
                                    SimpleDateFormat simple = new SimpleDateFormat("yyyy-MM-dd");
                                    value = simple.format(value);
                                }
                                map.put(property.getProperty() + "@entity.attendance.DatalySummary", value);
                            });
                        }
                    }
                }
                dataList.forEach(this::replaceDisplay);
                result.setPageData(new PageResult(dataList, pageBean.getPageNo(), pageBean.getPageSize(), page.size()));
            }
            result.setUserDynamicConfig(userDynamicSet);
            result.setDynamicConfig(dynamicSet);
        } catch (Exception e) {
            log.error("dynamicFeignClient execute fail,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        List<Map> items;
        if (result.getPageData() != null) {
            if (result.getPageData().getItems() != null) {
                items = result.getPageData().getItems();
                for (Map map : items) {
                    if (!"enabled".equals(postTxtShowCode)) {
                        String postTxt = (String) map.get("<EMAIL>");
                        if (StringUtils.isNotEmpty(postTxt) && postTxt.contains("(")) {
                            postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                            map.put("<EMAIL>", postTxt);
                        }
                    }
                }
                result.setPageData(new PageResult<>(items, paginator.getPage(), paginator.getLimit(), paginator.getTotalCount()));
                return result;
            }
        }
        result.setPageData(new PageResult<>(new ArrayList<>(), paginator.getPage(), paginator.getLimit(), 0));
        return result;
    }

    public static DataSimple of(Map properties) {
        DataSimple dataSimple = new DataSimple();
        NestPropertyValue nestPropertyValue = new NestPropertyValue();
        nestPropertyValue.putAll(properties);
        dataSimple.setProperties(nestPropertyValue);
        return dataSimple;
    }

    private void addDynamicColumnValue(Map data, String identifier, Map<String, List<MetadataPropertyDto>> mappedProperties, long time, String empId, String empIdProp) {
        if (mappedProperties.containsKey(identifier)) {
            DataQuery.identifier(identifier).limit(1, 1).filter(DataFilter.eq(empIdProp, empId)
                            .andNe("deleted", Boolean.TRUE.toString()),
                    DataSimple.class, time).getItems().stream().findFirst().ifPresent(emp -> mappedProperties.get(identifier).forEach(property -> {
                val value = emp.getProperties().get(property.getProperty());
                if (value instanceof SimplePropertyValue && ((SimplePropertyValue) value).getValue() != null &&
                        ((SimplePropertyValue) value).getType().name().equals("Boolean")) {
                    String InitValue = (((SimplePropertyValue) value).getValue().equals("false") ? BooleanValueEnum.getName(0) : BooleanValueEnum.getName(1));
                    data.put(property.getProperty() + "@" + identifier, InitValue);
                } else {
                    data.put(property.getProperty() + "@" + identifier, dynamicValueFormat(value));
                }
            }));
        }
    }

    private Object dynamicValueFormat(PropertyValue propertyValue) {
        if (propertyValue instanceof SimplePropertyValue) {
            if (((SimplePropertyValue) propertyValue).getType().isArray()) {
                return ((SimplePropertyValue) propertyValue).getArrayValues();
            } else {
                return ((SimplePropertyValue) propertyValue).getValue();
            }
        }
        if (propertyValue instanceof DictSimple) {
            return ((DictSimple) propertyValue).getText();
        }
        if (propertyValue instanceof PhoneSimple) {
            return ((PhoneSimple) propertyValue).getValue();
        }
        if (propertyValue instanceof EnumSimple) {
            return ((EnumSimple) propertyValue).getText();
        }
        if (propertyValue instanceof Address) {
            return ((Address) propertyValue).getText();
        }
        if (propertyValue instanceof JobGradeRange) {
            return ((JobGradeRange) propertyValue).getChannelName();
        }
        if (propertyValue instanceof EmpSimple) {
            return ((EmpSimple) propertyValue).getName();
        }
        return propertyValue;
    }

    private void replaceDisplay(Map data) {
        Map<String, Map<String, String>> replace = com.googlecode.totallylazy.Maps.map(
        );
        for (String key : replace.keySet()) {
            if (data.containsKey(key)) {
                data.put(key, replace.get(key).get(data.get(key)));
            }
        }
    }

    public List<MetadataVo> fetchStatisticsAvailableColumn(String type) {
        AttendanceAvailableColumn attendanceAvailableColumn = new AttendanceAvailableColumn();
        attendanceAvailableColumn.getMetadataList().add(metadataService.load("entity.hr.EmpPrivateInfo"));
        attendanceAvailableColumn.getMetadataList().add(metadataService.load("entity.hr.EmpWorkInfo"));
        attendanceAvailableColumn.getMetadataList().add(metadataService.load("entity.hr.EmpWorkOverview"));
        attendanceAvailableColumn.getMetadataList().add(metadataService.load("entity.hr.LastContract"));
        MetadataVo metadataVo = new MetadataVo();
        List<MetadataPropertyVo> standardProperties = new ArrayList<>();
        switch (type) {
            case "day":
                metadataVo.setIdentifier("entity.attendance.DatalySummary");
                metadataVo.setName("每日统计业务字段");
                List<String> dayHeaders = getDayHeaders();
                List<String> subList = dayHeaders.subList(8, dayHeaders.size());
                List<Map<String, String>> maps = convertList(subList);
                for (Map<String, String> convert : maps) {
                    standardProperties.add(metadataPropertyVoInit(convert.get("id"), convert.get("value")));
                }
                break;
            case "month":
                metadataVo.setIdentifier("entity.attendance.MouthlySummary");
                metadataVo.setName("月度汇总业务字段");
                List<String> monthHeader = getStaticList();
                List<Map<String, String>> monthMaps = convertList(monthHeader);
                for (Map<String, String> convert : monthMaps) {
                    standardProperties.add(metadataPropertyVoInit(convert.get("id"), convert.get("value")));
                }
                break;
            case "advance":
                metadataVo.setIdentifier("entity.attendance.MouthlyAdvancet");
                metadataVo.setName("考勤明细业务字段");
                List<String> advanceHeader = getStaticList();
                List<Map<String, String>> advanceMaps = convertList(advanceHeader);
                for (Map<String, String> convert : advanceMaps) {
                    standardProperties.add(metadataPropertyVoInit(convert.get("id"), convert.get("value")));
                }
                break;
        }
        metadataVo.setStandardProperties(standardProperties);
        attendanceAvailableColumn.getMetadataList().add(metadataVo);


        return attendanceAvailableColumn.getMetadataList();
    }

    @Override
    public UserDynamicConfig resetAvailableColumn(String type) {
        val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("DAILYSUM").getData();
        if (type.equals("1")) {
            String[] strings = new String[]{
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
            };
            List<String> checkList = new ArrayList<>(Arrays.asList(strings));
            List<String> extracted = extracted(userDynamicSet);
            checkList.addAll(extracted);
            userDynamicSet.setCheckedList(checkList);
        } else if (type.equals("2")) {
            String[] strings = new String[]{
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
            };
            // 将字符串数组转换为List<String>
            List<String> checkList = new ArrayList<>(Arrays.asList(strings));
            List<String> extracted = extracted(userDynamicSet);
            checkList.addAll(extracted);
            userDynamicSet.setCheckedList(checkList);
        } else {
            String[] strings = new String[]{
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>",
                    "<EMAIL>"
            };
            // 将字符串数组转换为List<String>
            List<String> checkList = new ArrayList<>(Arrays.asList(strings));
            List<String> extracted = extracted(userDynamicSet);
            checkList.addAll(extracted);
            userDynamicSet.setCheckedList(checkList);
        }
        return userDynamicSet;
    }


    @Override
    public DynamicPageDto getRegisterStatisticsAdvancedForDynamic(MonthAnalysePageDto dto, PageBean pageBean, UserInfo userInfo) {
        DynamicPageDto result = new DynamicPageDto();
        List<Map<String, Object>> list = searchRegisterStatistics(dto, pageBean, true, userInfo);
        int totalCount = getTotalCount(list, pageBean);
        pageBean.setCount(totalCount);
        SecurityUserInfo securityUserInfo = new SecurityUserInfo();
        try {
            securityUserInfo.setUserId(userInfo.getUserId());
            securityUserInfo.setTenantId(userInfo.getTenantId());
            securityUserInfo.setEmpId(userInfo.getEmpid() != null ? userInfo.getEmpid().longValue() : 0L);
            SecurityUserUtil.setSecurityUserInfo(securityUserInfo);
            val userDynamicSet = dynamicFeignClient.userDynamicTableLoad("MOUTHLYADVANCET").getData();
            val dynamicSet = dynamicFeignClient.dynamicTableLoad("MOUTHLYADVANCET").getData();
            //假勤快，出差，加班数据
            Map<String, Map<String, Object>> collect = list.stream()
                    .collect(Collectors.toMap(
                            map -> (String) map.get("bid"),
                            map -> {
                                Map<String, Object> subMap = new HashMap<>();
                                for (Map.Entry<String, Object> entry : map.entrySet()) {
                                    if (entry.getKey().startsWith("lt") || entry.getKey().startsWith("tr") || entry.getKey().startsWith("ot")) {
                                        subMap.put(entry.getKey() + "@entity.attendance.MouthlyAdvancet", entry.getValue());
                                    } else {
                                        subMap.put(entry.getKey(), entry.getValue());
                                    }
                                }
                                return subMap;
                            }
                    ));
            if (!list.isEmpty()) {
                Map<String, List<MetadataPropertyDto>> mappedProperties = com.googlecode.totallylazy.Maps.map();
                for (MetadataPropertyDto property : dynamicSet) {
                    val split = property.getProperty().split("@");
                    val identifier = split[1];
                    property.setProperty(split[0]);
                    if (mappedProperties.containsKey(identifier)) {
                        mappedProperties.get(identifier).add(property);
                    } else {
                        mappedProperties.put(identifier, com.googlecode.totallylazy.Lists.list(property));
                    }
                }
                val dataList = list.stream().map(dt -> {
                    Map<String, Object> data = BeanMapUtils.beanToMap(dt);
                    String empId = dt.get("empid").toString();
                    Long time = System.currentTimeMillis();
                    addDynamicColumnValue(data, "entity.hr.EmpPrivateInfo",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkInfo",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.EmpWorkOverview",
                            mappedProperties, time, empId, "empId");
                    addDynamicColumnValue(data, "entity.hr.LastContract",
                            mappedProperties, time, empId, "owner$empId");
                    data.put("bid", dt.get("bid"));
                    data.putAll(collect.get(dt.get("bid").toString()));
                    return data;
                }).collect(Collectors.toList());
                List<DataSimple> dataSimples = new ArrayList<>();
                for (Map<String, Object> map : list) {
                    DataSimple dataSimple = of(map);
                    dataSimple.setBid(map.get("bid").toString());
                    dataSimples.add(dataSimple);
                }
                for (Map<String, Object> map : dataList) {
                    String flagBid = map.get("bid").toString();
                    DataSimple dataObj = new DataSimple();
                    dataObj.setBid(flagBid);
                    for (DataSimple dataSimple : dataSimples) {
                        if ((dataSimple.getBid()).equals(dataObj.getBid())) {
                            mappedProperties.get("entity.attendance.MouthlyAdvancet").forEach(property -> {
                                Object value = dataSimple.getProperties().get(property.getProperty());
                                map.put(property.getProperty() + "@entity.attendance.MouthlyAdvancet", value);
                            });
                        }
                    }
                }
                dataList.forEach(this::replaceDisplay);
                for (Map map : dataList) {
                    if (!"enabled".equals(postTxtShowCode)) {
                        String postTxt = (String) map.get("<EMAIL>");
                        if (StringUtils.isNotEmpty(postTxt) && postTxt.contains("(")) {
                            postTxt = postTxt.substring(0, postTxt.lastIndexOf("("));
                            map.put("<EMAIL>", postTxt);
                        }
                    }
                }
                result.setPageData(new PageResult(dataList, pageBean.getPageNo(), pageBean.getPageSize(), totalCount));
            }
            result.setUserDynamicConfig(userDynamicSet);
            result.setDynamicConfig(dynamicSet);
        } catch (Exception e) {
            log.error("dynamicFeignClient execute fail,{}", e.getMessage(), e);
        } finally {
            SecurityUserUtil.removeSecurityUserInfo();
        }
        return result;
    }

    private int getTotalCount(List<Map<String, Object>> list, PageBean pageBean) {
        if (CollectionUtils.isNotEmpty(list)) {
            if (list instanceof PageList) {
                PageList<Map> pageList = (PageList) list;
                if (pageList.getPaginator() == null) {
                    return pageBean.getCount();
                } else {
                    return pageList.getPaginator().getTotalCount();
                }
            } else {
                return list.size();
            }
        } else {
            return 0;
        }
    }

    /**
     * 单独处理四种假期类型
     */
    private List<String> extracted(UserDynamicConfig userDynamicSet) {
        List<Map> columns = userDynamicSet.getColumns();
        String jsonString = FastjsonUtil.toJson(columns);
        JSONObject outerJsonObject = new JSONObject();
        outerJsonObject.put("columns", new JSONArray(jsonString));
        // 获取 "columns" 数组
        JSONArray columnsArray = outerJsonObject.getJSONArray("columns");
        // 遍历 "columns" 数组
        List<String> fileList = new ArrayList<>();
        for (int i = 0; i < columnsArray.length(); i++) {
            JSONObject column = columnsArray.getJSONObject(i);
            // 获取当前项的 "props" 对象
            JSONObject propsObject = column.getJSONObject("props");
            // 获取 "field" 对象
            JSONObject fieldObject = propsObject.getJSONObject("field");
            // 检查 "label" 是否为 "婚假期，"
            if ("哺乳假".equals(fieldObject.getString("label")) || "产假".equals(fieldObject.getString("label"))
                    || "调休".equals(fieldObject.getString("label")) || "探亲假".equals(fieldObject.getString("label"))) {
                // 如果是，获取 "value" 值
                String value = fieldObject.getString("key");
                if (value != null) {
                    fileList.add(value);
                }
            }
        }
        return fileList;
    }

    private List<String> getStaticList() {
        List<String> staticList = Lists.newArrayList(
                "{\"id\": \"wa_group_name\", \"width\": \"200px\", \"type\": \"ro\", \"value\": \"" + "考勤方案" + "\"}",
                "{\"id\": \"work_time\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(小时)" + "\"}",
                "{\"id\": \"work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "应出勤时长(天)" + "\"}",
                "{\"id\": \"register_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(小时)" + "\"}",
                "{\"id\": \"register_time_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤时长(天)" + "\"}",
                "{\"id\": \"registerTimeTotal\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际出勤总时长" + "\"}",
                "{\"id\": \"actual_work_time\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "实际工作时长(小时)" + "\"}",
                "{\"id\": \"leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（小时）" + "\"}",
                "{\"id\": \"leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "请休假总时长（天）" + "\"}",
                "{\"id\": \"full_paid_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（小时）" + "\"}",
                "{\"id\": \"full_paid_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "全薪假期总时长（天）" + "\"}",
                "{\"id\": \"deduction_leave_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（小时）" + "\"}",
                "{\"id\": \"deduction_leave_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "扣薪假期总时长（天）" + "\"}",
                "{\"id\": \"travel_duration\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（小时）" + "\"}",
                "{\"id\": \"travel_duration_day\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + "出差总时长（天）" + "\"}",
                "{\"id\": \"latecount\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "迟到次数" + "\"}",
                "{\"id\": \"late_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "迟到时长(分钟)" + "\"}",
                "{\"id\": \"earlycount\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "早退次数" + "\"}",
                "{\"id\": \"early_time_minute\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "早退时长(分钟)" + "\"}",
                "{\"id\": \"is_kg\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "旷工次数" + "\"}",
                "{\"id\": \"kg_work_time_minute\", \"width\": \"50px\", \"type\": \"ro\", \"value\": \"" + "旷工时长(分钟)" + "\"}",
                "{\"id\": \"kg_work_time_day\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "旷工天数" + "\"}",
                "{\"id\": \"bdk_count\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "补卡次数" + "\"}",
                "{\"id\": \"compensatory\", \"width\": \"60px\", \"type\": \"ro\", \"value\": \"" + "调休转付现" + "\"}");

        UserInfo userInfo = getUserInfo();
        String tenantId;
        if (userInfo != null) {
            tenantId = userInfo.getTenantId();
        } else {
            tenantId = SecurityUserUtil.getSecurityUserInfo().getTenantId();
        }
        // 请假类型
        List<Map> mapList = waConfigService.listLeaveTypeDef(tenantId, SessionHolder.getLang());
        if (CollectionUtils.isNotEmpty(mapList)) {
            mapList.forEach(lt -> staticList.add("{\"id\": \"lt_" + lt.get("value") + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + lt.get("text") + "\"}"));
        }
        //出差类型
        List<WaTravelTypeDo> travelTypeDoList = waTravelTypeDo.getWaTravelTypeList(tenantId);
        if (CollectionUtils.isNotEmpty(travelTypeDoList)) {
            travelTypeDoList.forEach(lt -> staticList.add("{\"id\": \"tr_" + lt.getTravelTypeId() + "_key\", \"width\": \"80px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(lt.getI18nTravelTypeName(), lt.getTravelTypeName()) + "\"}"));
        }
        //加班
        List<OverTimeTypeDo> overtimeTypes = overTimeTypeDo.getAllOtTypes(tenantId, null, DateUtil.getCurrentTime(true));
        for (OverTimeTypeDo overtimeType : overtimeTypes) {
            String columnName = "ot_" + overtimeType.getOvertimeTypeId() + "_key";
            staticList.add("{\"id\": \"" + columnName + "\", \"width\": \"160px\", \"type\": \"ro\", \"value\": \"" + LangParseUtil.getI18nLanguage(overtimeType.getI18nTypeName(), overtimeType.getTypeName()) + "\"}");
        }
        return staticList;
    }

    private MetadataPropertyVo metadataPropertyVoInit(String property, String name) {
        MetadataPropertyVo metadataPropertyVo = new MetadataPropertyVo();
        metadataPropertyVo.setName(name);
        metadataPropertyVo.setProperty(property);
        return metadataPropertyVo;
    }

    public static List<Map<String, String>> convertList(List<String> inputList) {
        List<Map<String, String>> result = new ArrayList<>();
        for (String str : inputList) {
            Map<String, String> map = new HashMap<>();
            str = str.replaceAll("\\{", "").replaceAll("}", "");
            String[] keyValuePairs = str.split(",");
            for (String pair : keyValuePairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].trim().replaceAll("\"", "");
                    String value = keyValue[1].trim().replaceAll("\"", "");
                    map.put(key, value);
                }
            }
            result.add(map);
        }
        return result;
    }
}