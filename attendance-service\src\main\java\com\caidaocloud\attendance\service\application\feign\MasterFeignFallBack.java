package com.caidaocloud.attendance.service.application.feign;

import com.caidaocloud.attendance.service.application.dto.EmployeeGroupDto;
import com.caidaocloud.dto.EmpGroup;
import com.caidaocloud.web.Result;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
public class MasterFeignFallBack implements MasterFeignClient{
    @Override
    public Result<EmployeeGroupDto> getEmployeeGroup(String businessKey, String groupType) {
        return Result.fail();
    }

    @Override
    public Result<EmployeeGroupDto> saveEmployeeGroup(EmployeeGroupDto empGroupDto) {
        return Result.fail();
    }

    @Override
    public Result<EmployeeGroupDto> saveOrUpdate(EmployeeGroupDto empGroupDto) {
        return Result.fail();
    }

    @Override
    public Result<EmployeeGroupDto> getEmployeeGroupDetail(Long empGroupId) {
        return Result.fail();
    }

    @Override
    public Result removeBusKey(String businessKey, String groupType) {
        return Result.fail();
    }

    @Override
    public Result<List<EmployeeGroupDto>> detailByKeysAndType(EmpGroup empGroup) {
        return Result.fail();
    }

    @Override
    public Result fetchModelPropertyEnum(String identifier, String property) {
        return Result.fail();
    }
}
