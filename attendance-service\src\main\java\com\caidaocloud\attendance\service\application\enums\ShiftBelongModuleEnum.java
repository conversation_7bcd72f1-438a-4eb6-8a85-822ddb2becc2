package com.caidaocloud.attendance.service.application.enums;

/**
 * 班次设置：班次所属模块
 */
public enum ShiftBelongModuleEnum {
    ATTENDANCE("attendance", "假勤"),
    WFM("wfm", "工时");

    private String code;
    private String name;

    ShiftBelongModuleEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static String getName(String code) {
        for (ShiftBelongModuleEnum c : ShiftBelongModuleEnum.values()) {
            if (c.getCode().equals(code)) {
                return c.name;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
}
