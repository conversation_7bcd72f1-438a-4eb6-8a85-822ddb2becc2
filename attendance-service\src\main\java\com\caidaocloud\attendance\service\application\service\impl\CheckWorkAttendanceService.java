package com.caidaocloud.attendance.service.application.service.impl;

import com.caidao1.commons.utils.DateUtil;
import com.caidao1.commons.utils.JSONUtils;
import com.caidaocloud.attendance.service.application.dto.WaStatisticsDto;
import com.caidaocloud.attendance.service.application.event.publish.PaySyncWaPublish;
import com.caidaocloud.attendance.service.application.service.ISobService;
import com.caidaocloud.attendance.service.application.service.IStatisticsService;
import com.caidaocloud.attendance.service.domain.entity.WaSobDo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class CheckWorkAttendanceService {
    @Resource
    private ISobService sobService;
    @Resource
    private PaySyncWaPublish paySyncWaPublish;
    @Resource
    @Lazy
    private StatisticsReportService statisticsReportService;
    @Autowired
    private IStatisticsService statisticsService;

    public void analyzeRegister() throws Exception {
        Long today = DateUtil.getOnlyDate();
        log.info("Start to execute analyzeRegister time:{}", DateUtil.getTimeStrByTimesamp(today));
        List<WaSobDo> waSobs = sobService.getWaSobIdByDateRangeAndPeriodMonth(null, today, null, null);
        log.info("Select waSobs:{}", JSONUtils.ObjectToJson(waSobs));
        if (CollectionUtils.isNotEmpty(waSobs)) {
            Map<String, List<WaSobDo>> sobMap = waSobs.stream().collect(Collectors.groupingBy(WaSobDo::getBelongOrgId));
            for (Map.Entry<String, List<WaSobDo>> m : sobMap.entrySet()) {
                if (CollectionUtils.isEmpty(m.getValue())) {
                    continue;
                }
                String belongOrgId = m.getKey();
                List<WaSobDo> sobs = Lists.newArrayList();
                for (WaSobDo sob : m.getValue()) {
                    List<Integer> sysPeriodMonths = Lists.newArrayList();
                    Integer todaySysPeriodMonth = sob.getSysPeriodMonth();
                    sysPeriodMonths.add(todaySysPeriodMonth);
                    Long lastMonth = DateUtil.addMonth(DateUtil.getOnlyDate(new Date(DateUtil.convertStringToDateTime(String.valueOf(todaySysPeriodMonth), "yyyyMM", Boolean.FALSE))), -1);
                    Integer lastSysPeriodMonth = this.getPeriodMonth(lastMonth);
                    sysPeriodMonths.add(lastSysPeriodMonth);
                    sobs.addAll(sobService.getWaSobIdByDateRangeAndPeriodMonth(belongOrgId, null, sysPeriodMonths, sob.getWaGroupId()));
                }
                sobs = sobs.stream().sorted(Comparator.comparing(WaSobDo::getSysPeriodMonth)).collect(Collectors.toList());
                log.info("select sobs:{}", JSONUtils.ObjectToJson(sobs));
                for (WaSobDo waSob : sobs) {
                    Long startDate = waSob.getStartDate();
                    Long endDate = waSob.getEndDate();
                    try {
                        log.info("waSob:{}", JSONUtils.ObjectToJson(waSob));
                        // 考勤分析
                        WaStatisticsDto waStatisticsDto = statisticsService.asyncRegister(belongOrgId, startDate, endDate, null, waSob.getWaSobId(), "", 0L, true);
                        if (null != waStatisticsDto.getEmpList() && !waStatisticsDto.getEmpList().isEmpty()) {
                            // 同步数据到薪资
                            paySyncWaPublish.syncWaAnalyze(waStatisticsDto.getEmpList(), belongOrgId, startDate, endDate);
                            // 考勤统计 周报、月报数据计算同步
                            statisticsReportService.syncWaAnalyzeStatisticsReport(waStatisticsDto.getEmpList(), waSob.getWaSobId(), belongOrgId, 0L);
                        }
                    } catch (Exception e) {
                        log.error("[belongid={},starDate={},endDate={},wasobid={}]analyzeRegister err,{}", belongOrgId, startDate, endDate, waSob.getWaSobId(), e.getMessage(), e);
                    }
                    log.info("Automatic accounting sobId={},的考勤记录belongid={} Attendance record", waSob.getWaSobId(), belongOrgId);
                }
            }
        }
        log.info("Execute analyzeRegister end time:{}", DateUtil.getTimeStrByTimesamp(System.currentTimeMillis() / 1000));
    }

    private Integer getPeriodMonth(long time) {
        return Integer.valueOf(DateUtil.parseDateToPattern(new Date(time * 1000), "yyyyMM"));
    }
}
