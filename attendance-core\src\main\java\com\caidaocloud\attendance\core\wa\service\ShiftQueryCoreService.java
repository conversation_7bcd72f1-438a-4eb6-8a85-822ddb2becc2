package com.caidaocloud.attendance.core.wa.service;

import com.caidao1.auth.mybatis.mapper.SysEmpInfoMapper;
import com.caidao1.auth.mybatis.model.SysEmpInfo;
import com.caidao1.commons.BaseConst;
import com.caidao1.commons.cache.RedisCache;
import com.caidao1.commons.cache.util.CDCacheUtil;
import com.caidao1.commons.cache.util.RedisKeyDefine;
import com.caidao1.commons.utils.DateUtil;
import com.caidao1.system.mybatis.mapper.SysCorpOrgMapper;
import com.caidao1.system.mybatis.model.SysCorpOrg;
import com.caidao1.wa.mybatis.mapper.*;
import com.caidao1.wa.mybatis.model.*;
import com.caidaocloud.attendance.core.wa.dto.CalendarEventDto;
import com.caidaocloud.attendance.core.wa.dto.EmpShiftInfo;
import com.caidaocloud.attendance.core.wa.dto.ShiftRestPeriods;
import com.caidaocloud.attendance.core.wa.dto.WorkTimeCalendar;
import com.caidaocloud.attendance.core.wa.dto.shift.*;
import com.caidaocloud.attendance.core.wa.enums.DateTypeEnum;
import com.caidaocloud.attendance.core.wa.utils.LangParseUtil;
import com.caidaocloud.util.FastjsonUtil;
import com.caidaocloud.util.StringUtil;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.postgresql.util.PGobject;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 员工排班查询服务（假勤模块）
 *
 * <AUTHOR>
 * @Date 2025/2/14
 */
@Slf4j
@Service
public class ShiftQueryCoreService {
    @Autowired
    private WaConfigMapper waConfigMapper;
    @Autowired
    private WaStoreTimeMapper waStoreTimeMapper;
    @Autowired
    private SysCorpOrgMapper sysCorpOrgMapper;
    @Autowired
    private SysEmpInfoMapper sysEmpInfoMapper;
    @Autowired
    private WaRegisterRecordMapper waRegisterRecordMapper;
    @Autowired
    private WaEmpShiftChangeMapper waEmpShiftChangeMapper;
    @Autowired
    private WaWorktimeDetailMapper waWorktimeDetailMapper;
    @Autowired
    private WaShiftDefMapper waShiftDefMapper;
    @Autowired
    private RemoteSmartWorkTimeService remoteSmartWorkTimeService;
    @Autowired
    private WorkCalendarFeignService workCalendarFeignService;

    private static ObjectMapper objectMapper;

    static {
        objectMapper = new ObjectMapper();
        //允许没有定义的属性
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        //允许使用未带引号的字段名
        objectMapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_FIELD_NAMES, true);
        //允许使用单引号
        objectMapper.configure(JsonParser.Feature.ALLOW_SINGLE_QUOTES, true);
    }

    /**
     * 查询单个员工排班
     *
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, WaShiftDef> getWaWorkShiftDef(String belongOrgid, Long empid, Integer tmType, Long startDate, Long endDate, Map<String, Integer> empChangeShiftMap) {
        Map<Long, WaShiftDef> worktimeShiftMap = new HashMap<>();
        if (tmType == null) {
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
            if (empInfo != null) {
                tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
            } else {
                return worktimeShiftMap;
            }
        }

        Map params = new HashMap() {{
            put("empId", empid);
            put("startDate", startDate);
            put("endDate", endDate);
        }};

        List<WaWorktimeDetail> worktimeDetails = null;
        if (tmType == 1) {
            worktimeDetails = waWorktimeDetailMapper.getWaShiftDefByDates(params);
        } else {
            worktimeDetails = waWorktimeDetailMapper.getStoreShiftDefByDates(params);
        }

        if (CollectionUtils.isNotEmpty(worktimeDetails)) {
            Map<Integer, WaShiftDef> allShiftDef = getCorpAllShiftDef(belongOrgid);

            worktimeDetails.forEach(row -> {
                //班次替换
                Long workDate = row.getWorkDate();
                String shiftKey = String.format("%s_%s", empid, workDate);
                if (empChangeShiftMap.containsKey(shiftKey)) {
                    Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                    row.setShiftDefId(changeShiftId);
                    empChangeShiftMap.remove(shiftKey);
                }
                worktimeShiftMap.put(row.getWorkDate(), allShiftDef.get(row.getShiftDefId()));
            });
        }

        return worktimeShiftMap;
    }

    /**
     * 查询员工排班
     *
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, WaWorktimeDetail> getWaWorktimeDetail(Long empid, Integer tmType, Long startDate, Long endDate) {
        Map<Long, WaWorktimeDetail> worktimeDetailMap = new HashMap();
        if (tmType == null) {
            SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empid);
            if (empInfo != null) {
                tmType = empInfo.getTmType() == null ? 1 : empInfo.getTmType();
            } else {
                return worktimeDetailMap;
            }
        }

        Map params = new HashMap() {{
            put("empId", empid);
            put("startDate", startDate);
            put("endDate", endDate);
        }};

        List<WaWorktimeDetail> worktimeDetails = null;
        if (tmType == 1) {
            worktimeDetails = waWorktimeDetailMapper.getWaShiftDefByDates(params);
        } else {
            worktimeDetails = waWorktimeDetailMapper.getStoreShiftDefByDates(params);
        }
        if (CollectionUtils.isNotEmpty(worktimeDetails)) {
            worktimeDetails.forEach(row -> worktimeDetailMap.put(row.getWorkDate(), row));
        }

        return worktimeDetailMap;
    }

    /**
     * 查询公司全部班次（标准班次）
     *
     * @param belongid
     * @return
     */
    public Map<Integer, WaShiftDef> getCorpAllShiftDef(String belongid) {
        // TODO 后续如有必要可优化至缓存中
        WaShiftDefExample shiftDefExample = new WaShiftDefExample();
        shiftDefExample.createCriteria().andBelongOrgidEqualTo(belongid);
        List<WaShiftDef> shiftDefList = waShiftDefMapper.selectByExample(shiftDefExample);
        if (CollectionUtils.isEmpty(shiftDefList)) {
            return new HashMap<>();
        }
        return shiftDefList.stream().collect(Collectors.toMap(WaShiftDef::getShiftDefId, Function.identity()));
    }

    /**
     * 查询公司全部班次（标准班次）
     *
     * @param belongid
     * @param shiftDefIds
     * @return
     */
    public Map<Integer, WaShiftDef> getCorpAllShiftDef(String belongid, List<Integer> shiftDefIds) {
        if (CollectionUtils.isEmpty(shiftDefIds)) {
            return getCorpAllShiftDef(belongid);
        }
        WaShiftDefExample shiftDefExample = new WaShiftDefExample();
        shiftDefExample.createCriteria().andBelongOrgidEqualTo(belongid).andShiftDefIdIn(shiftDefIds);
        List<WaShiftDef> shiftDefList = waShiftDefMapper.selectByExample(shiftDefExample);
        if (CollectionUtils.isEmpty(shiftDefList)) {
            return new HashMap<>();
        }
        return shiftDefList.stream().collect(Collectors.toMap(WaShiftDef::getShiftDefId, Function.identity()));
    }

    /**
     * 查询公司默认班次（标准班次）
     *
     * @param belongid
     * @return
     */
    public WaShiftDef getCorpDefaultShift(String belongid) {
        // TODO 后续如有必要可优化至缓存中
        WaShiftDefExample shiftDefExample = new WaShiftDefExample();
        shiftDefExample.createCriteria().andBelongOrgidEqualTo(belongid).andIsDefaultEqualTo(true)
                .andShiftDefCodeEqualTo(BaseConst.SYSTEM_DEFAULT_SHIFT);
        List<WaShiftDef> shiftDefList = waShiftDefMapper.selectByExample(shiftDefExample);
        if (CollectionUtils.isEmpty(shiftDefList)) {
            return null;
        }
        return shiftDefList.get(0);
    }

    public WaShiftDef getDefaultShiftDef(Map<Integer, WaShiftDef> shiftMap) {
        // TODO 优化，不需要每次都遍历查找
        if (MapUtils.isNotEmpty(shiftMap)) {
            List<WaShiftDef> defaultShiftList = shiftMap.values().stream()
                    .filter(it -> it.getIsDefault() != null
                            && it.getIsDefault()
                            && BaseConst.SYSTEM_DEFAULT_SHIFT.equals(it.getShiftDefCode()))
                    .collect(Collectors.toList());
            return CollectionUtils.isNotEmpty(defaultShiftList) ? defaultShiftList.get(0) : null;
        }
        return null;
    }

    /**
     * 查询单个员工班次数据
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, WaShiftDef> getEmpWorkShift(String belongid, Long empid, Integer tmType, Long startDate, Long endDate) {
        //班次调整查询
        Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(belongid, new ArrayList<>(Arrays.asList(empid)), startDate, endDate);

        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);

        Map<Long, WaShiftDef> empShiftMap = new HashMap<>();

        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, new ArrayList<>(Arrays.asList(new Long[]{empid})), startDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                calendarList.forEach(ec -> {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    //班次替换
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey)) {
                        Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                        ec.setId(changeShiftId);
                        empChangeShiftMap.remove(shiftKey);
                    }
                    empShiftMap.put(workDate, corpAllShiftDef.get(ec.getId()));
                });
            }
        } else {
            Map<Long, WaShiftDef> workShiftDef = this.getWaWorkShiftDef(belongid, empid, tmType, startDate, endDate, empChangeShiftMap);
            if (MapUtils.isNotEmpty(workShiftDef)) {
                empShiftMap.putAll(workShiftDef);
            }
        }
        // 未排班直接替换班次的数据
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            empChangeShiftMap.forEach((empIdAndWorkDateKey, changeShiftId) -> {
                if (corpAllShiftDef.containsKey(changeShiftId) && corpAllShiftDef.get(changeShiftId) != null) {
                    String[] keyArray = empIdAndWorkDateKey.split("_");
                    Long workDate = Long.valueOf(keyArray[1]);
                    empShiftMap.put(workDate, corpAllShiftDef.get(changeShiftId));
                }
            });
        }
        return empShiftMap;
    }

    public List<WaEmpShiftChange> getEmpShiftChangeList(String belongid, List<Long> empids, Long startDate, Long endDate) {
        WaEmpShiftChangeExample shiftChangeExample = new WaEmpShiftChangeExample();
        shiftChangeExample.createCriteria().andBelongOrgIdEqualTo(belongid).andEmpidIn(empids).
                andStatusEqualTo(2).andWorkDateBetween(startDate, endDate);
        return waEmpShiftChangeMapper.selectByExample(shiftChangeExample);
    }

    /**
     * 替换班次查询
     *
     * @param belongid
     * @param empids
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<String, Integer> getEmpChangeShiftMap(String belongid, List<Long> empids, Long startDate, Long endDate) {
        Map<String, Integer> empChangeShiftMap = new HashMap<>();
        List<WaEmpShiftChange> empShiftChangeList = getEmpShiftChangeList(belongid, empids, startDate, endDate);
        if (CollectionUtils.isNotEmpty(empShiftChangeList)) {
            SimpleDateFormat ymdDf = new SimpleDateFormat("yyyyMMdd");
            empShiftChangeList.forEach(row -> {
                String ymdStr = ymdDf.format(new Date(row.getWorkDate() * 1000));
                empChangeShiftMap.put(row.getEmpid() + "_" + ymdStr, row.getNewShiftDefId());
            });
        }
        return empChangeShiftMap;
    }

    /**
     * 替换班次查询
     *
     * @param belongid
     * @param empids
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<String, Integer> getEmpChangeShiftMapByTimeStamp(String belongid, List<Long> empids, Long startDate, Long endDate) {
        Map<String, Integer> empChangeShiftMap = new HashMap<>();
        List<WaEmpShiftChange> empShiftChangeList = getEmpShiftChangeList(belongid, empids, startDate, endDate);
        if (CollectionUtils.isNotEmpty(empShiftChangeList)) {
            empShiftChangeList.forEach(row -> empChangeShiftMap.put(row.getEmpid() + "_" + row.getWorkDate(), row.getNewShiftDefId()));
        }
        return empChangeShiftMap;
    }

    /**
     * 查询员工排班
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @param worktimeType
     * @param isAutoScheduling
     * @return
     */
    public Map<Long, WaWorktimeDetail> getEmpWaWorktimeDetail(String belongid, Long empid, Integer tmType,
                                                              Long startDate, Long endDate, Integer worktimeType,
                                                              Boolean isAutoScheduling) {
        //班次调整查询
        Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(belongid, new ArrayList<>(Arrays.asList(empid)), startDate, endDate);
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);
        Map<Long, WaWorktimeDetail> empShiftMap = new HashMap<>();
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, new ArrayList<>(Arrays.asList(new Long[]{empid})), startDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                calendarList.forEach(ec -> {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey)) {
                        Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                        ec.setId(changeShiftId);
                        empChangeShiftMap.remove(shiftKey);
                    }
                    if (corpAllShiftDef.get(ec.getId()) != null) {
                        WaWorktimeDetail detail = new WaWorktimeDetail();
                        BeanUtils.copyProperties(corpAllShiftDef.get(ec.getId()), detail);
                        if (null == detail.getWorkDate()) {
                            detail.setWorkDate(workDate);
                        }
                        empShiftMap.put(workDate, detail);
                    }
                });
            }
        } else {
            Map<Long, WaWorktimeDetail> worktimeDetailMap = this.getWaWorktimeDetail(empid, tmType, startDate, endDate);
            if (BooleanUtils.isTrue(isAutoScheduling)) {
                // 门店人员&综合工时制 请假自动补班
                String isOpen = CDCacheUtil.getValue(RedisKeyDefine.SYS_CONFIG_ENABLED_KEY_ + belongid + RedisKeyDefine.IS_OPEN_STORE_AUTO_SCHEDULING);
                if ("1".equals(isOpen) && ((tmType != null && tmType == 2) || (worktimeType != null && worktimeType == 2))) {
                    WaShiftDef defaultShift = this.getCorpDefaultShift(belongid);//默认班次
                    if (defaultShift != null) {
                        //检查门店考勤员工是否排班，如未排班则检查是否开启了自动排班功能并且系统有默认班次，如果满足以上条件，则自动排班
                        if (worktimeDetailMap == null) {
                            worktimeDetailMap = new HashMap<>();
                        }
                        long tmpDate = startDate;
                        while (tmpDate <= endDate) {
                            if (worktimeDetailMap.get(tmpDate) == null) {
                                WaWorktimeDetail detail = new WaWorktimeDetail();
                                detail.setDateType(defaultShift.getDateType());
                                detail.setStartTime(defaultShift.getStartTime());
                                detail.setEndTime(defaultShift.getEndTime());
                                detail.setWorkDate(tmpDate);
                                detail.setWorkTotalTime(defaultShift.getWorkTotalTime());
                                detail.setRestPeriods(defaultShift.getRestPeriods());
                                detail.setRestTotalTime(defaultShift.getRestTotalTime());
                                detail.setIsNoonRest(defaultShift.getIsNoonRest());
                                detail.setNoonRestStart(defaultShift.getNoonRestStart());
                                detail.setNoonRestEnd(defaultShift.getNoonRestEnd());
                                detail.setShiftDefId(defaultShift.getShiftDefId());
                                worktimeDetailMap.put(tmpDate, detail);
                            }
                            tmpDate = tmpDate + 24 * 60 * 60;
                        }
                    }
                }
            }
            if (MapUtils.isNotEmpty(worktimeDetailMap)) {
                worktimeDetailMap.forEach((workDate, waWorktimeDetail) -> {
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey)) {
                        Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                        if (corpAllShiftDef.containsKey(changeShiftId)) {
                            BeanUtils.copyProperties(corpAllShiftDef.get(changeShiftId), waWorktimeDetail);
                            if (null == waWorktimeDetail.getWorkDate()) {
                                waWorktimeDetail.setWorkDate(workDate);
                            }
                        }
                        empChangeShiftMap.remove(shiftKey);
                    }
                });
                empShiftMap.putAll(worktimeDetailMap);
            }
        }

        // 未排班直接替换班次的数据
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            empChangeShiftMap.forEach((empIdAndWorkDateKey, changeShiftId) -> {
                if (corpAllShiftDef.containsKey(changeShiftId) && corpAllShiftDef.get(changeShiftId) != null) {
                    String[] keyArray = empIdAndWorkDateKey.split("_");
                    Long workDate = Long.valueOf(keyArray[1]);
                    WaWorktimeDetail detail = new WaWorktimeDetail();
                    BeanUtils.copyProperties(corpAllShiftDef.get(changeShiftId), detail);
                    detail.setWorkDate(workDate);
                    empShiftMap.put(workDate, detail);
                }
            });
        }
        return empShiftMap;
    }

    public EmpShiftInfo convertWaShiftDef(Long empId, Long workDate, WaShiftDef shiftDef) {
        EmpShiftInfo shift = new EmpShiftInfo();
        BeanUtils.copyProperties(shiftDef, shift);
        shift.setWorkDate(workDate);
        shift.setEmpid(empId);
        try {
            if (shiftDef.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getRestPeriods();
                List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                shift.setRestPeriods(restPeriods);
            }
            if (shiftDef.getOvertimeRestPeriods() != null) {
                PGobject pGobject = (PGobject) shiftDef.getOvertimeRestPeriods();
                List<ShiftRestPeriods> overRest = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                shift.setOvertimeRestPeriods(overRest);
            }
        } catch (Exception e) {
            log.error("WaCommonService.convertWaShiftDef error msg {}", e.getMessage(), e);
        }
        return shift;
    }

    /**
     * 查询员工排班
     *
     * @param belongid
     * @param empIdList
     * @param startdate
     * @param endDate
     * @return
     */
    public Map<String, EmpShiftInfo> getEmpShiftInfoListMaps(String belongid, List<Long> empIdList, Long startdate, Long endDate) {
        Long preStartDate = startdate - (24 * 60 * 60);
        Map<String, EmpShiftInfo> empShift = new HashMap<>();

        //公司下面所有的排班计划
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);

        //班次调整查询
        Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(belongid, empIdList, preStartDate, endDate);

        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            // 智能排班
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, empIdList, preStartDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                calendarList.forEach(ec -> {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    Long empId = ec.getEmpid();
                    String shiftKey = String.format("%s_%s", empId, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey) && empChangeShiftMap.get(shiftKey) != null) {
                        ec.setId(empChangeShiftMap.get(shiftKey));
                        empChangeShiftMap.remove(shiftKey);
                    }
                    if (corpAllShiftDef.containsKey(ec.getId()) && corpAllShiftDef.get(ec.getId()) != null) {
                        EmpShiftInfo shiftInfo = convertWaShiftDef(empId, workDate, corpAllShiftDef.get(ec.getId()));
                        empShift.put(shiftKey, shiftInfo);
                    }
                });
            }
        } else {
            // 日历排班和门店排班
            Map params = new HashMap();
            params.put("belongid", belongid);
            params.put("startdate", preStartDate);
            params.put("endDate", endDate);
            Integer ymstart = Integer.valueOf(DateUtil.parseDateToPattern(new Date(startdate * 1000), "yyyyMM"));
            Integer ymend = Integer.valueOf(DateUtil.parseDateToPattern(new Date(endDate * 1000), "yyyyMM"));
            params.put("ymstart", ymstart);
            params.put("ymend", ymend);
            params.put("anyEmpids", "'{" + StringUtils.join(empIdList, ",") + "}'");

            // 查询日历排班和门店排班数据
            List<Map> empShiftMapList = waRegisterRecordMapper.getEmpShiftRecords2(params);
            if (CollectionUtils.isNotEmpty(empShiftMapList)) {
                ObjectMapper objectMapper = new ObjectMapper();
                for (Map shiftObj : empShiftMapList) {
                    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    String json;
                    EmpShiftInfo shift = null;
                    try {
                        json = objectMapper.writeValueAsString(shiftObj);
                        shift = objectMapper.readValue(json, new TypeReference<EmpShiftInfo>() {
                        });
                        if (shiftObj.get("rest_periods") != null) {
                            PGobject pGobject = (PGobject) shiftObj.get("rest_periods");
                            List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                            });
                            shift.setRestPeriods(restPeriods);
                        }
                        if (shiftObj.get("overtime_rest_periods") != null) {
                            PGobject pGobject = (PGobject) shiftObj.get("overtime_rest_periods");
                            List<ShiftRestPeriods> overRest = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                            });
                            shift.setOvertimeRestPeriods(overRest);
                        }
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    if (shift != null) {
                        String shiftKey = String.format("%s_%s", shift.getEmpid(), shift.getWorkDate());
                        if (empChangeShiftMap.containsKey(shiftKey) && empChangeShiftMap.get(shiftKey) != null) {
                            Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                            if (corpAllShiftDef.containsKey(changeShiftId) && corpAllShiftDef.get(changeShiftId) != null) {
                                WaShiftDef shiftDef = corpAllShiftDef.get(changeShiftId);
                                BeanUtils.copyProperties(shiftDef, shift);
                            }
                            empChangeShiftMap.remove(shiftKey);
                        }

                        empShift.put(shiftKey, shift);
                    }
                }
            }
        }

        // 未排班直接替换班次的数据
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            empChangeShiftMap.forEach((empIdAndWorkDateKey, changeShiftId) -> {
                if (corpAllShiftDef.containsKey(changeShiftId) && corpAllShiftDef.get(changeShiftId) != null) {
                    String[] keyArray = empIdAndWorkDateKey.split("_");
                    Long empId = Long.valueOf(keyArray[0]);
                    Long workDate = Long.valueOf(keyArray[1]);

                    EmpShiftInfo shiftInfo = convertWaShiftDef(empId, workDate, corpAllShiftDef.get(changeShiftId));
                    empShift.put(String.format("%s_%s", empId, workDate), shiftInfo);
                }
            });
        }
        return empShift;
    }

    /**
     * 查询员工排班
     *
     * @param belongid
     * @param empid
     * @param tmType
     * @param startDate
     * @param endDate
     * @return
     */
    public Map<Long, WaWorktimeDetail> getEmpWaWorktimeDetail(String belongid, Long empid, Integer tmType,
                                                              Long startDate, Long endDate) {
        //班次调整查询
        Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(belongid,
                new ArrayList<>(Arrays.asList(empid)), startDate, endDate);
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(belongid);
        Map<Long, WaWorktimeDetail> empShift = new HashMap<>();

        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, new ArrayList<>(Arrays.asList(new Long[]{empid})), startDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                calendarList.forEach(ec -> {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey)) {
                        Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                        ec.setId(changeShiftId);
                        empChangeShiftMap.remove(shiftKey);
                    }
                    if (corpAllShiftDef.get(ec.getId()) != null) {
                        WaWorktimeDetail detail = new WaWorktimeDetail();
                        BeanUtils.copyProperties(corpAllShiftDef.get(ec.getId()), detail);
                        if (null == detail.getWorkDate()) {
                            detail.setWorkDate(workDate);
                        }
                        empShift.put(workDate, detail);
                    }
                });
            }
        } else {
            Map<Long, WaWorktimeDetail> worktimeDetailMap = this.getWaWorktimeDetail(empid, tmType, startDate, endDate);
            if (MapUtils.isNotEmpty(worktimeDetailMap)) {
                worktimeDetailMap.forEach((workDate, waWorktimeDetail) -> {
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey)) {
                        Integer changeShiftId = empChangeShiftMap.get(shiftKey);
                        BeanUtils.copyProperties(corpAllShiftDef.get(changeShiftId), waWorktimeDetail);
                        if (null == waWorktimeDetail.getWorkDate()) {
                            waWorktimeDetail.setWorkDate(workDate);
                        }
                        empChangeShiftMap.remove(shiftKey);
                    }
                });
                empShift.putAll(worktimeDetailMap);
            }
        }

        // 未排班直接替换班次的数据
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            empChangeShiftMap.forEach((empIdAndWorkDateKey, changeShiftId) -> {
                if (corpAllShiftDef.containsKey(changeShiftId) && corpAllShiftDef.get(changeShiftId) != null) {
                    String[] keyArray = empIdAndWorkDateKey.split("_");
                    Long workDate = Long.valueOf(keyArray[1]);
                    WaWorktimeDetail detail = new WaWorktimeDetail();
                    BeanUtils.copyProperties(corpAllShiftDef.get(changeShiftId), detail);
                    if (null == detail.getWorkDate()) {
                        detail.setWorkDate(workDate);
                    }
                    empShift.put(workDate, detail);
                }
            });
        }
        return empShift;
    }

    /**
     * 查询员工排班（考勤分析使用）
     *
     * @param paramsMap
     * @param empShiftInfoByDateMap
     * @param empIdList
     * @param corpShiftDefMap
     * @return
     */
    public Map<String, EmpShiftInfo> getEmpShiftInfoListMaps(Map<String, Object> paramsMap,
                                                             Map<String, EmpShiftInfo> empShiftInfoByDateMap,
                                                             List<Long> empIdList,
                                                             Map<Integer, WaShiftDef> corpShiftDefMap) {
        String belongid = (String) paramsMap.get("belongid");
        Long startdate = (Long) paramsMap.get("startDate");
        Long preStartDate = startdate - (24 * 60 * 60);
        Long endDate = (Long) paramsMap.get("endDate");

        Map<String, EmpShiftInfo> empShift = new HashMap<>();

        // 班次调整
        Map<String, Integer> empChangeShiftMap = this.getEmpChangeShiftMapByTimeStamp(belongid, empIdList, preStartDate, endDate);
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(belongid)) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmployeeShiftList(belongid, empIdList, preStartDate, endDate);
            if (CollectionUtils.isNotEmpty(calendarList)) {
                for (WorkTimeCalendar ec : calendarList) {
                    Long workDate = DateUtil.convertStringToDateTime(String.valueOf(ec.getDate()), "yyyyMMdd", true);
                    Long empid = ec.getEmpid();
                    //班次替换
                    String shiftKey = String.format("%s_%s", empid, workDate);
                    if (empChangeShiftMap.containsKey(shiftKey) && empChangeShiftMap.get(shiftKey) != null) {
                        ec.setId(empChangeShiftMap.get(shiftKey));
                        empChangeShiftMap.remove(shiftKey);
                    }
                    if (corpShiftDefMap.containsKey(ec.getId()) && corpShiftDefMap.get(ec.getId()) != null) {
                        EmpShiftInfo shift = doConvertWaShiftDef(empid, workDate, corpShiftDefMap.get(ec.getId()));
                        String key = shift.getEmpid() + "_" + shift.getShiftDefId() + "_" + shift.getWorkDate();
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                }
            }
        } else {
            paramsMap.put("startDate", preStartDate);
            List<Map> shiftinfos = waRegisterRecordMapper.getEmpShiftRecords2(paramsMap);
            paramsMap.put("startDate", startdate);

            if (CollectionUtils.isNotEmpty(shiftinfos)) {
                ObjectMapper objectMapper = new ObjectMapper();
                for (Map<String, Object> shiftObj : shiftinfos) {
                    objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
                    String json = null;
                    EmpShiftInfo shift = null;
                    try {
                        json = objectMapper.writeValueAsString(shiftObj);
                        shift = objectMapper.readValue(json, new TypeReference<EmpShiftInfo>() {
                        });
                        convertJsonValue(shift, shiftObj);
                    } catch (Exception e) {
                        log.error(e.getMessage(), e);
                    }
                    if (shift != null) {
                        Long empid = shift.getEmpid();
                        Long workDate = shift.getWorkDate();
                        //班次替换
                        if (empChangeShiftMap.containsKey(empid + "_" + workDate)) {
                            Integer changeShiftId = empChangeShiftMap.get(empid + "_" + workDate);
                            WaShiftDef shiftDef = corpShiftDefMap.get(changeShiftId);
                            if (shiftDef != null) {
                                BeanUtils.copyProperties(shiftDef, shift);
                                convertJsonValue(shiftDef, shift);
                            }
                            empChangeShiftMap.remove(empid + "_" + workDate);
                        }
                        String key = String.format("%s_%s_%s", shift.getEmpid(), shift.getShiftDefId(), shift.getWorkDate());
                        empShift.put(key, shift);
                        empShiftInfoByDateMap.put(shift.getEmpid() + "_" + shift.getWorkDate(), shift);
                    }
                }
            }
        }

        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            for (Map.Entry<String, Integer> entry : empChangeShiftMap.entrySet()) {
                EmpShiftInfo shift = new EmpShiftInfo();
                WaShiftDef source = corpShiftDefMap.get(entry.getValue());
                if (null == source) {
                    continue;
                }
                BeanUtils.copyProperties(source, shift);
                convertJsonValue(source, shift);
                String empIdAndWorkDateKey = entry.getKey();
                String[] keyArray = empIdAndWorkDateKey.split("_");
                shift.setEmpid(Long.valueOf(keyArray[0]));
                shift.setWorkDate(Long.valueOf(keyArray[1]));
                empShift.put(String.format("%s_%s_%s", shift.getEmpid(), shift.getShiftDefId(), shift.getWorkDate()), shift);
                empShiftInfoByDateMap.put(entry.getKey(), shift);
            }
        }
        return empShift;
    }

    public EmpShiftInfo doConvertWaShiftDef(Long empId, Long workDate, WaShiftDef shiftDef) {
        EmpShiftInfo shift = new EmpShiftInfo();
        BeanUtils.copyProperties(shiftDef, shift);
        shift.setWorkDate(workDate);
        shift.setEmpid(empId);
        Map<String, Object> shiftObj = new HashMap<>();
        shiftObj.put("rest_periods", shiftDef.getRestPeriods());
        shiftObj.put("overtime_rest_periods", shiftDef.getOvertimeRestPeriods());
        shiftObj.put("midwayClockTimes", shiftDef.getMidwayClockTime());
        shiftObj.put("multiWorkTimes", shiftDef.getMultiWorkTimes());
        shiftObj.put("multiCheckinTimes", shiftDef.getMultiCheckinTimes());
        shiftObj.put("multiOvertime", shiftDef.getMultiOvertime());
        try {
            convertJsonValue(shift, shiftObj);
        } catch (Exception e) {
            log.error("ShiftQueryCoreService.doConvertWaShiftDef error msg {}", e.getMessage(), e);
        }
        return shift;
    }

    public void convertJsonValue(WaShiftDef source, EmpShiftInfo target) {
        if (null == source || null == target) {
            return;
        }
        try {
            if (source.getRestPeriods() != null) {
                PGobject pGobject = (PGobject) source.getRestPeriods();
                List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                target.setRestPeriods(restPeriods);
            }
            if (source.getOvertimeRestPeriods() != null) {
                PGobject overtimeRestPeriodsPGobject = (PGobject) source.getOvertimeRestPeriods();
                List<ShiftRestPeriods> overRest = objectMapper.readValue(overtimeRestPeriodsPGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
                });
                target.setOvertimeRestPeriods(overRest);
            }
            if (StringUtils.isNotBlank(source.getMidwayClockTime())) {
                target.setMidwayClockTimeList(FastjsonUtil.toArrayList(source.getMidwayClockTime(), MidwayClockTimeInfo.class));
            }
            if (null != source.getMultiWorkTimes()) {
                PGobject pGobject = (PGobject) source.getMultiWorkTimes();
                List<MultiWorkTimeBaseDto> multiWorkTimeList = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<MultiWorkTimeBaseDto>>() {
                });
                target.setMultiWorkTimeList(multiWorkTimeList);
            }
            if (StringUtils.isNotBlank(source.getMultiOvertime())) {
                target.setMultiOvertimeList(FastjsonUtil.toArrayList(source.getMultiOvertime(), MultiOvertimeDto.class));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void convertJsonValue(EmpShiftInfo shift, Map map) throws Exception {
        String key = "rest_periods";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<ShiftRestPeriods> restPeriods = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
            });
            shift.setRestPeriods(restPeriods);
        }
        key = "overtime_rest_periods";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<ShiftRestPeriods> overRest = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<ShiftRestPeriods>>() {
            });
            shift.setOvertimeRestPeriods(overRest);
        }
        key = "midwayClockTimes";
        if (map.containsKey(key) && map.get(key) != null) {
            shift.setMidwayClockTimeList(FastjsonUtil.toArrayList(map.get(key).toString(), MidwayClockTimeInfo.class));
        }
        key = "multiWorkTimes";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<MultiWorkTimeBaseDto> multiWorkTimeList = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<MultiWorkTimeBaseDto>>() {
            });
            shift.setMultiWorkTimeList(multiWorkTimeList);
        }
        key = "multiCheckinTimes";
        if (map.containsKey(key) && map.get(key) != null) {
            PGobject pGobject = (PGobject) map.get(key);
            List<MultiCheckinTimeInfo> multiCheckinTimeList = objectMapper.readValue(pGobject.getValue(), new TypeReference<List<MultiCheckinTimeInfo>>() {
            });
            shift.setMultiCheckinTimeList(multiCheckinTimeList);
        }
        key = "multiOvertime";
        if (map.containsKey(key) && map.get(key) != null) {
            shift.setMultiOvertimeList(FastjsonUtil.toArrayList(map.get(key).toString(), MultiOvertimeDto.class));
        }
    }

    /**
     * 员工端：指定日期查询员工某天的排班信息
     *
     * @param empId
     * @param date
     * @return
     */
    @RedisCache(expire = 60)
    public List<Map> getShiftListForEmpPortal(Long empId, Long date) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empInfo) {
            return Lists.newArrayList();
        }
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {//开启智能排班
            WaShiftDef shiftDef = remoteSmartWorkTimeService.getEmployeeShiftByDate(empInfo.getBelongOrgId(), empInfo.getEmpid(), date);
            if (null == shiftDef) {
                return Lists.newArrayList();
            }
            Map<String, Object> shiftMap = new HashMap<>();
            shiftMap.put("dateType", shiftDef.getDateType());
            shiftMap.put("workDate", date);
            shiftMap.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(shiftDef.getDateType()));
            shiftMap.put("startTime", shiftDef.getStartTime());
            shiftMap.put("endTime", shiftDef.getEndTime());
            shiftMap.put("restTimeDesc", shiftDef.getRestTimeDesc());
            shiftMap.put("onDutyStartTime", shiftDef.getOnDutyStartTime());
            shiftMap.put("onDutyEndTime", shiftDef.getOnDutyEndTime());
            shiftMap.put("offDutyStartTime", shiftDef.getOffDutyStartTime());
            shiftMap.put("offDutyEndTime", shiftDef.getOffDutyEndTime());
            shiftMap.put("shiftCode", shiftDef.getShiftDefCode());
            shiftMap.put("shiftName", shiftDef.getShiftDefName());
            if (null != shiftDef.getI18nShiftDefName()) {
                shiftMap.put("shiftName", LangParseUtil.getI18nLanguage(shiftDef.getI18nShiftDefName(), shiftDef.getShiftDefName()));
            }
            shiftMap.put("i18nShiftDefName", shiftDef.getI18nShiftDefName());
            shiftMap.put("isNoonRest", shiftDef.getIsNoonRest());
            shiftMap.put("noonRestStart", shiftDef.getNoonRestStart());
            shiftMap.put("noonRestEnd", shiftDef.getNoonRestEnd());
            shiftMap.put("workTotalTime", shiftDef.getWorkTotalTime());
            if (empInfo.getOrgid() != null) {
                SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(empInfo.getOrgid());
                if (org != null) {
                    shiftMap.put("addr", org.getOfficeAddr());//部门办公地点
                }
            }
            return Lists.newArrayList(shiftMap);
        } else {
            Map<String, Object> queryShiftParams = new HashMap<>();
            queryShiftParams.put("empId", empId);
            queryShiftParams.put("searchDate", date);
            if (empInfo.getTmType() != null && empInfo.getTmType() == 2) {//门店考勤
                try {
                    queryShiftParams.put("searchDate", Integer.parseInt(DateUtil.convertDateTimeToStr(date, "yyyyMMdd", true)));
                } catch (Exception e) {
                    queryShiftParams.put("searchDate", 0);
                    log.error(e.getMessage(), e);
                }
                List<Map> shiftList = waStoreTimeMapper.getMobileStorePbByDate(queryShiftParams);
                // 杰尼亚-因允许未排班也可以休假，休假时会默认排个系统默认班次（班次代码为：system-default-shift），系统默认班次为虚拟班次，日历上面不展示
                shiftList = shiftList.stream().filter(row -> {
                    String code = (String) row.get("shiftDefCode");
                    return !StringUtils.isNotBlank(code) || !code.equals(BaseConst.SYSTEM_DEFAULT_SHIFT);
                }).collect(Collectors.toList());
                return shiftList;
            } else {
                //班次调整查询
                Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(empInfo.getBelongOrgId(), Lists.newArrayList(empId), date, date);
                String shiftKey = empId + "_" + date;
                Integer changeShiftId;
                if (empChangeShiftMap.containsKey(shiftKey) && null != (changeShiftId = empChangeShiftMap.get(shiftKey))) {
                    WaShiftDef shiftDef = waShiftDefMapper.selectByPrimaryKey(changeShiftId);
                    if (null == shiftDef) {
                        return Lists.newArrayList();
                    }
                    Map<String, Object> shiftMap = new HashMap<>();
                    shiftMap.put("dateType", shiftDef.getDateType());
                    shiftMap.put("workDate", date);
                    shiftMap.put("shiftDefName", BaseConst.WA_DATE_TYPE.get(shiftDef.getDateType()));
                    shiftMap.put("startTime", shiftDef.getStartTime());
                    shiftMap.put("endTime", shiftDef.getEndTime());
                    shiftMap.put("restTimeDesc", shiftDef.getRestTimeDesc());
                    shiftMap.put("onDutyStartTime", shiftDef.getOnDutyStartTime());
                    shiftMap.put("onDutyEndTime", shiftDef.getOnDutyEndTime());
                    shiftMap.put("offDutyStartTime", shiftDef.getOffDutyStartTime());
                    shiftMap.put("offDutyEndTime", shiftDef.getOffDutyEndTime());
                    shiftMap.put("shiftCode", shiftDef.getShiftDefCode());
                    shiftMap.put("shiftName", shiftDef.getShiftDefName());
                    if (null != shiftDef.getI18nShiftDefName()) {
                        shiftMap.put("shiftName", LangParseUtil.getI18nLanguage(shiftDef.getI18nShiftDefName(), shiftDef.getShiftDefName()));
                    }
                    shiftMap.put("i18nShiftDefName", shiftDef.getI18nShiftDefName());
                    shiftMap.put("isNoonRest", shiftDef.getIsNoonRest());
                    shiftMap.put("noonRestStart", shiftDef.getNoonRestStart());
                    shiftMap.put("noonRestEnd", shiftDef.getNoonRestEnd());
                    shiftMap.put("workTotalTime", shiftDef.getWorkTotalTime());
                    if (empInfo.getOrgid() != null) {
                        SysCorpOrg org = sysCorpOrgMapper.selectByPrimaryKey(empInfo.getOrgid());
                        if (org != null) {
                            shiftMap.put("addr", org.getOfficeAddr());//部门办公地点
                        }
                    }
                    return Lists.newArrayList(shiftMap);
                } else {
                    return waConfigMapper.getMobilePbByDate(queryShiftParams);
                }
            }
        }
    }

    /**
     * 查询指定月份的员工日历排班（门户/员工日历查询使用）
     *
     * @param tenantId
     * @param empId
     * @param searchMonth
     * @param startDate
     * @param endDate
     * @return
     */
    public List<EmpCalendarShiftForMonthDto> getEmpCalendarShiftByMonth(String tenantId,
                                                                        Long empId,
                                                                        Integer searchMonth,
                                                                        Long startDate,
                                                                        Long endDate) {
        SysEmpInfo empInfo = sysEmpInfoMapper.selectByPrimaryKey(empId);
        if (null == empInfo) {
            return Lists.newArrayList();
        }

        if (null == startDate || null == endDate) {
            Map<String, Long> timePeriodByMonth;
            try {
                timePeriodByMonth = EmpCalendarShiftForMonthDto.calCalendarTimePeriodByMonth(searchMonth);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                return Lists.newArrayList();
            }
            if (MapUtils.isEmpty(timePeriodByMonth)) {
                return Lists.newArrayList();
            }
            startDate = timePeriodByMonth.get("start");
            endDate = timePeriodByMonth.get("end");
        }

        //班次调整查询
        Map<String, Integer> empChangeShiftMap = getEmpChangeShiftMapByTimeStamp(tenantId, Lists.newArrayList(empId), startDate, endDate);
        Map<Integer, WaShiftDef> corpAllShiftDef = this.getCorpAllShiftDef(tenantId);
        List<EmpCalendarShiftForMonthDto> calendarShiftList;

        Integer tmType = empInfo.getTmType();
        if (remoteSmartWorkTimeService.validateEnableSmartShiftPlan(empInfo.getBelongOrgId())) {
            List<WorkTimeCalendar> calendarList = remoteSmartWorkTimeService.getEmpWorkTimeCalendarByYm(empInfo.getEmpid(), searchMonth);
            calendarShiftList = CollectionUtils.isNotEmpty(calendarList)
                    ? calendarList.stream().map(row -> {
                EmpCalendarShiftForMonthDto shiftForMonthDto = new EmpCalendarShiftForMonthDto();
                shiftForMonthDto.setDateType(row.getType())
                        .setWorkDate(String.valueOf(row.getDate()))
                        .setShiftDefName(BaseConst.WA_DATE_TYPE.get(row.getType()));
                return shiftForMonthDto;
            }).collect(Collectors.toList()) : null;
        } else {
            if (tmType != null && tmType == 2) {//门店考勤
                Map<String, Object> map = new HashMap<>();
                map.put("empId", empId);
                map.put("searchMonth", searchMonth);
                List<Map> calendarList = waStoreTimeMapper.getMobileStorePbByMonth(map);
                calendarShiftList = CollectionUtils.isNotEmpty(calendarList)
                        ? calendarList.stream()
                        .map(row -> {
                            EmpCalendarShiftForMonthDto shiftForMonthDto = new EmpCalendarShiftForMonthDto();
                            shiftForMonthDto.setDateType(Integer.valueOf(row.get("dateType").toString()))
                                    .setWorkDate(String.valueOf(row.get("workDate")))
                                    .setShiftDefName(String.valueOf(row.get("shiftDefName")));
                            return shiftForMonthDto;
                        }).collect(Collectors.toList()) : null;
            } else {//正常考勤
                Map<String, Object> map = new HashMap<>();
                map.put("empId", empId);
                map.put("start", startDate);
                map.put("end", endDate);
                List<Map> calendarList = waConfigMapper.getMobilePbByMonth(map);
                calendarShiftList = CollectionUtils.isNotEmpty(calendarList)
                        ? calendarList.stream()
                        .map(row -> {
                            EmpCalendarShiftForMonthDto shiftForMonthDto = new EmpCalendarShiftForMonthDto();
                            shiftForMonthDto.setDateType(Integer.valueOf(row.get("dateType").toString()))
                                    .setWorkDate(String.valueOf(row.get("workDate")))
                                    .setShiftDefName(String.valueOf(row.get("shiftDefName")));
                            return shiftForMonthDto;
                        }).collect(Collectors.toList()) : null;
            }
        }
        if (CollectionUtils.isNotEmpty(calendarShiftList) && MapUtils.isNotEmpty(empChangeShiftMap)) {
            calendarShiftList.forEach(ec -> {
                Long workDate = DateUtil.convertStringToDateTime(ec.getWorkDate(), "yyyyMMdd", true);
                String shiftKey = String.format("%s_%s", empId, workDate);
                Integer changeShiftId;
                WaShiftDef changeShiftDef;
                if (empChangeShiftMap.containsKey(shiftKey)
                        && null != (changeShiftId = empChangeShiftMap.get(shiftKey))
                        && null != (changeShiftDef = corpAllShiftDef.get(changeShiftId))) {
                    ec.setDateType(changeShiftDef.getDateType());
                    ec.setShiftDefName(DateTypeEnum.getName(changeShiftDef.getDateType()));
                    empChangeShiftMap.remove(shiftKey);
                }
            });
        }

        // 未排班直接替换班次的数据
        if (MapUtils.isNotEmpty(empChangeShiftMap)) {
            empChangeShiftMap.forEach((empIdAndWorkDateKey, changeShiftId) -> {
                WaShiftDef changeShiftDef;
                if (corpAllShiftDef.containsKey(changeShiftId)
                        && null != (changeShiftDef = corpAllShiftDef.get(changeShiftId))) {
                    String[] keyArray = empIdAndWorkDateKey.split("_");
                    Long workDate = Long.valueOf(keyArray[1]);

                    EmpCalendarShiftForMonthDto ec = new EmpCalendarShiftForMonthDto();
                    ec.setDateType(changeShiftDef.getDateType());
                    ec.setWorkDate(DateUtil.parseDateToPattern(new Date(workDate * 1000), "yyyyMMdd"));
                    ec.setShiftDefName(DateTypeEnum.getName(changeShiftDef.getDateType()));
                }
            });
        }
        return calendarShiftList;
    }

    /**
     * 考勤日历-班次查询
     *
     * @param belongOrgId
     * @param empId
     * @param start
     * @param end
     * @param dataScope
     * @return
     */
    public List<CalendarEventDto> getEmpCalendarShiftListByYm(String belongOrgId, Long empId,
                                                              String start, String end, String dataScope) {
        Long startDate = DateUtil.convertStringToDateTime(start, "yyyy-MM-dd", Boolean.TRUE);
        Long endDate = DateUtil.convertStringToDateTime(end, "yyyy-MM-dd", Boolean.TRUE);

        ListEmpCalendarShiftQueryDto queryDto = new ListEmpCalendarShiftQueryDto();
        queryDto.setBelongOrgId(belongOrgId).setStartDate(startDate)
                .setEndDate(endDate).setEmpId(empId).setDataScope(dataScope);
        List<Map> canlendarShiftList = workCalendarFeignService.getEmpCalendarShiftList(queryDto);

        // 查询替换班次信息
        List<Map> changeList = workCalendarFeignService.getEmpCalendarChangeShiftList(queryDto);
        if (CollectionUtils.isNotEmpty(changeList)) {
            List<String> changeDateList = changeList.stream()
                    .map(o -> (String) o.get("date")).collect(Collectors.toList());
            canlendarShiftList = canlendarShiftList.stream().filter(o -> {
                String date = (String) o.get("date");
                if (changeDateList.contains(date)) {
                    return false;
                }
                return true;
            }).collect(Collectors.toList());
            canlendarShiftList.addAll(changeList);
        }

        SimpleDateFormat ymdDf = new SimpleDateFormat("yyyyMMdd");
        return canlendarShiftList.stream().map(o -> {
            CalendarEventDto event = new CalendarEventDto();
            event.setId((Integer) o.get("id"));

            String name = (String) o.get("name");
            if (null != o.get("i18nShiftDefName")) {
                String i18n = LangParseUtil.getI18nLanguage(o.get("i18nShiftDefName").toString(), null);
                if (StringUtil.isNotBlank(i18n)) {
                    name = i18n;
                }
            }
            event.setTitle(o.get("code") + " " + name);

            event.setDateType((Integer) o.get("dateType"));

            Long workDate = null;
            try {
                String workDateYmd = (String) o.get("date");
                workDate = ymdDf.parse(workDateYmd).getTime() / 1000;
            } catch (ParseException e) {
                log.error("getEmpCalendarShiftListByYm error msg {}", e.getMessage(), e);
            }
            event.setStart(DateUtil.getDateStrByTimesamp(workDate));
            event.setStartTimestamp(workDate);
            return event;
        }).collect(Collectors.toList());
    }
}
