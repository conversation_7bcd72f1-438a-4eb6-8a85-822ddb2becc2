package com.caidaocloud.attendance.service.domain.repository;

import com.caidao1.commons.mybatis.MyPageBounds;
import com.caidao1.report.dto.PageBean;
import com.caidao1.wa.mybatis.model.WaAnalyze;
import com.caidao1.wa.mybatis.model.WaParseGroup;
import com.caidaocloud.attendance.core.commons.AttendanceBasePage;
import com.caidaocloud.attendance.core.commons.AttendancePageResult;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordDo;
import com.caidaocloud.attendance.service.domain.entity.WaRegisterRecordOfPortalDo;
import com.caidaocloud.attendance.service.infrastructure.repository.po.EmpParseGroup;
import com.caidaocloud.attendance.service.interfaces.dto.RegisterRecordRequestDto;
import com.caidaocloud.dto.PageResult;
import com.caidaocloud.dto.QueryPageBean;
import com.github.miemiedev.mybatis.paginator.domain.PageList;

import java.util.List;
import java.util.Map;

/**
 * 考勤打卡记录
 *
 * <AUTHOR>
 * @Date 2021/3/23
 */
public interface IRegisterRecordRepository {
    void batchSave(List<WaRegisterRecordDo> recordDoLists);

    AttendancePageResult<WaRegisterRecordDo> getRegisterPageList(RegisterRecordRequestDto requestDto);

    Map getRegisterDetailById(Long corpId, Long registerId);

    List<WaRegisterRecordDo> getEmpRegisterRecordList(Long empId, Long startDate, Long endDate, List<Integer> typeList, List<Integer> statusList);

    WaRegisterRecordDo getWaRegisterRecordDoById(Integer recordId);

    int updateByPrimaryKeySelective(WaRegisterRecordDo waRegisterRecordDo);

    Map getDayAnalyzeDetailById(Integer analyzeId);

    WaParseGroup selectAttendanceRuleByEmpidAndDate(String belongOrgId, Long empid, Long date);

    List<WaRegisterRecordDo> selectAllRecordListByBelongDate(String belongOrgId, Long empId, Long belongDate);

    List<WaRegisterRecordDo> getAllRecordListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate);

    PageList<Map> selectWaAnalyseListByWaGroup(MyPageBounds pageBounds, Map<String, Object> paramsMap);

    List<WaRegisterRecordDo> selectRegListByIds(String belongOrgId, List<Integer> registerIds);

    AttendancePageResult<WaRegisterRecordDo> getRegisterPageListByEmpId(RegisterRecordRequestDto requestDto, Long empId);

    AttendancePageResult<WaRegisterRecordDo> getAllRegisterRecordPageList(AttendanceBasePage basePage, String belongOrgId, List<Long> empIds, Long startDate, Long endDate, List<Integer> typeList, Integer ifValid, Integer clockSiteStatus, List<Integer> approvalStatusList);

    List<Map> getEmpWorkTimeRecordByDay(Long empid, Long daytime, boolean includeOutReg);

    List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long startDate, Long endDate);

    List<WaRegisterRecordDo> getEmpBdkRegisterList(String belongOrgId, List<Long> empIdList, Long belongDate);

    List<WaRegisterRecordDo> getRegisterRecordPageList(PageBean pageBean, String belongOrgId, Long startTime, Long endTime, List<Long> empIds, List<Integer> types, Integer clockSiteStatus);

    AttendancePageResult<WaRegisterRecordDo> getPageList(AttendanceBasePage basePage, String belongOrgId, Long startTime, Long endTime, List<Long> empIds, List<Integer> types, Integer clockSiteStatus);

    List<Long> selectRegEmpIdList(String belongOrgId, Long startTime, Long endTime);

    List<EmpParseGroup> selectEmpParseGroupListByDate(String belongOrgId, List<Long> empIds, Long date);

    List<EmpParseGroup> selectEmpParseGroupListByDateRange(String belongOrgId, List<Long> empIds, Long startDate, Long endDate);

    int updateValidStateByIds(String belongOrgId, List<Integer> ids, Long userId, Integer ifValid);

    void deleteByIds(String belongOrgId, List<Integer> ids);

    void updateClockSiteStatus(String belongOrgId, List<Integer> recordIds, Integer clockSiteStatus);

    List<WaRegisterRecordDo> getWaRegisterRecordByBdkId(String tenantId, Long recordId);

    PageResult<WaRegisterRecordOfPortalDo> getPageOfPortal(QueryPageBean queryPageBean);

    PageList<Map> selectWaAnalyseList(MyPageBounds pageBounds, Map<String, Object> paramsMap);

    List<Long> selectWaAbnormalAnalyseList(String tenantId, Long startDate, Long endDate, Integer analyzeResult, List<Integer> waGroupIds);

    AttendancePageResult<WaAnalyze> selectAnalyseList(AttendanceBasePage basePage, Map<String, Object> paramsMap);
}
